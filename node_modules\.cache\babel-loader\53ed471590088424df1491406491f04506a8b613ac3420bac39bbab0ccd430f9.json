{"ast": null, "code": "import { addMonths } from \"./addMonths.js\";\n\n/**\n * The {@link addYears} function options.\n */\n\n/**\n * @name addYears\n * @category Year Helpers\n * @summary Add the specified number of years to the given date.\n *\n * @description\n * Add the specified number of years to the given date.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n * @typeParam ResultDate - The result `Date` type.\n *\n * @param date - The date to be changed\n * @param amount - The amount of years to be added.\n * @param options - The options\n *\n * @returns The new date with the years added\n *\n * @example\n * // Add 5 years to 1 September 2014:\n * const result = addYears(new Date(2014, 8, 1), 5)\n * //=> Sun Sep 01 2019 00:00:00\n */\nexport function addYears(date, amount, options) {\n  return addMonths(date, amount * 12, options);\n}\n\n// Fallback for modularized imports:\nexport default addYears;", "map": {"version": 3, "names": ["addMonths", "addYears", "date", "amount", "options"], "sources": ["C:/Users/<USER>/node_modules/date-fns/addYears.js"], "sourcesContent": ["import { addMonths } from \"./addMonths.js\";\n\n/**\n * The {@link addYears} function options.\n */\n\n/**\n * @name addYears\n * @category Year Helpers\n * @summary Add the specified number of years to the given date.\n *\n * @description\n * Add the specified number of years to the given date.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n * @typeParam ResultDate - The result `Date` type.\n *\n * @param date - The date to be changed\n * @param amount - The amount of years to be added.\n * @param options - The options\n *\n * @returns The new date with the years added\n *\n * @example\n * // Add 5 years to 1 September 2014:\n * const result = addYears(new Date(2014, 8, 1), 5)\n * //=> Sun Sep 01 2019 00:00:00\n */\nexport function addYears(date, amount, options) {\n  return addMonths(date, amount * 12, options);\n}\n\n// Fallback for modularized imports:\nexport default addYears;\n"], "mappings": "AAAA,SAASA,SAAS,QAAQ,gBAAgB;;AAE1C;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,QAAQA,CAACC,IAAI,EAAEC,MAAM,EAAEC,OAAO,EAAE;EAC9C,OAAOJ,SAAS,CAACE,IAAI,EAAEC,MAAM,GAAG,EAAE,EAAEC,OAAO,CAAC;AAC9C;;AAEA;AACA,eAAeH,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}