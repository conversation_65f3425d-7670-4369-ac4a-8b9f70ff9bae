{"ast": null, "code": "import { version } from './version';\n/** Current session will be checked for refresh at this interval. */\nexport const AUTO_REFRESH_TICK_DURATION_MS = 30 * 1000;\n/**\n * A token refresh will be attempted this many ticks before the current session expires. */\nexport const AUTO_REFRESH_TICK_THRESHOLD = 3;\n/*\n * Earliest time before an access token expires that the session should be refreshed.\n */\nexport const EXPIRY_MARGIN_MS = AUTO_REFRESH_TICK_THRESHOLD * AUTO_REFRESH_TICK_DURATION_MS;\nexport const GOTRUE_URL = 'http://localhost:9999';\nexport const STORAGE_KEY = 'supabase.auth.token';\nexport const AUDIENCE = '';\nexport const DEFAULT_HEADERS = {\n  'X-Client-Info': `gotrue-js/${version}`\n};\nexport const NETWORK_FAILURE = {\n  MAX_RETRIES: 10,\n  RETRY_INTERVAL: 2 // in deciseconds\n};\nexport const API_VERSION_HEADER_NAME = 'X-Supabase-Api-Version';\nexport const API_VERSIONS = {\n  '2024-01-01': {\n    timestamp: Date.parse('2024-01-01T00:00:00.0Z'),\n    name: '2024-01-01'\n  }\n};\nexport const BASE64URL_REGEX = /^([a-z0-9_-]{4})*($|[a-z0-9_-]{3}$|[a-z0-9_-]{2}$)$/i;\nexport const JWKS_TTL = 600000; // 10 minutes", "map": {"version": 3, "names": ["version", "AUTO_REFRESH_TICK_DURATION_MS", "AUTO_REFRESH_TICK_THRESHOLD", "EXPIRY_MARGIN_MS", "GOTRUE_URL", "STORAGE_KEY", "AUDIENCE", "DEFAULT_HEADERS", "NETWORK_FAILURE", "MAX_RETRIES", "RETRY_INTERVAL", "API_VERSION_HEADER_NAME", "API_VERSIONS", "timestamp", "Date", "parse", "name", "BASE64URL_REGEX", "JWKS_TTL"], "sources": ["C:\\Users\\<USER>\\lostandfound\\discfinder-app\\node_modules\\@supabase\\auth-js\\src\\lib\\constants.ts"], "sourcesContent": ["import { version } from './version'\n\n/** Current session will be checked for refresh at this interval. */\nexport const AUTO_REFRESH_TICK_DURATION_MS = 30 * 1000\n\n/**\n * A token refresh will be attempted this many ticks before the current session expires. */\nexport const AUTO_REFRESH_TICK_THRESHOLD = 3\n\n/*\n * Earliest time before an access token expires that the session should be refreshed.\n */\nexport const EXPIRY_MARGIN_MS = AUTO_REFRESH_TICK_THRESHOLD * AUTO_REFRESH_TICK_DURATION_MS\n\nexport const GOTRUE_URL = 'http://localhost:9999'\nexport const STORAGE_KEY = 'supabase.auth.token'\nexport const AUDIENCE = ''\nexport const DEFAULT_HEADERS = { 'X-Client-Info': `gotrue-js/${version}` }\nexport const NETWORK_FAILURE = {\n  MAX_RETRIES: 10,\n  RETRY_INTERVAL: 2, // in deciseconds\n}\n\nexport const API_VERSION_HEADER_NAME = 'X-Supabase-Api-Version'\nexport const API_VERSIONS = {\n  '2024-01-01': {\n    timestamp: Date.parse('2024-01-01T00:00:00.0Z'),\n    name: '2024-01-01',\n  },\n}\n\nexport const BASE64URL_REGEX = /^([a-z0-9_-]{4})*($|[a-z0-9_-]{3}$|[a-z0-9_-]{2}$)$/i\n\nexport const JWKS_TTL = 600000 // 10 minutes\n"], "mappings": "AAAA,SAASA,OAAO,QAAQ,WAAW;AAEnC;AACA,OAAO,MAAMC,6BAA6B,GAAG,EAAE,GAAG,IAAI;AAEtD;;AAEA,OAAO,MAAMC,2BAA2B,GAAG,CAAC;AAE5C;;;AAGA,OAAO,MAAMC,gBAAgB,GAAGD,2BAA2B,GAAGD,6BAA6B;AAE3F,OAAO,MAAMG,UAAU,GAAG,uBAAuB;AACjD,OAAO,MAAMC,WAAW,GAAG,qBAAqB;AAChD,OAAO,MAAMC,QAAQ,GAAG,EAAE;AAC1B,OAAO,MAAMC,eAAe,GAAG;EAAE,eAAe,EAAE,aAAaP,OAAO;AAAE,CAAE;AAC1E,OAAO,MAAMQ,eAAe,GAAG;EAC7BC,WAAW,EAAE,EAAE;EACfC,cAAc,EAAE,CAAC,CAAE;CACpB;AAED,OAAO,MAAMC,uBAAuB,GAAG,wBAAwB;AAC/D,OAAO,MAAMC,YAAY,GAAG;EAC1B,YAAY,EAAE;IACZC,SAAS,EAAEC,IAAI,CAACC,KAAK,CAAC,wBAAwB,CAAC;IAC/CC,IAAI,EAAE;;CAET;AAED,OAAO,MAAMC,eAAe,GAAG,sDAAsD;AAErF,OAAO,MAAMC,QAAQ,GAAG,MAAM,EAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}