{"ast": null, "code": "import { toDate } from \"./toDate.js\";\n\n/**\n * The {@link isMonday} function options.\n */\n\n/**\n * @name isMonday\n * @category Weekday Helpers\n * @summary Is the given date Monday?\n *\n * @description\n * Is the given date Monday?\n *\n * @param date - The date to check\n * @param options - An object with options\n *\n * @returns The date is Monday\n *\n * @example\n * // Is 22 September 2014 Monday?\n * const result = isMonday(new Date(2014, 8, 22))\n * //=> true\n */\nexport function isMonday(date, options) {\n  return toDate(date, options?.in).getDay() === 1;\n}\n\n// Fallback for modularized imports:\nexport default isMonday;", "map": {"version": 3, "names": ["toDate", "isMonday", "date", "options", "in", "getDay"], "sources": ["C:/Users/<USER>/node_modules/date-fns/isMonday.js"], "sourcesContent": ["import { toDate } from \"./toDate.js\";\n\n/**\n * The {@link isMonday} function options.\n */\n\n/**\n * @name isMonday\n * @category Weekday Helpers\n * @summary Is the given date Monday?\n *\n * @description\n * Is the given date Monday?\n *\n * @param date - The date to check\n * @param options - An object with options\n *\n * @returns The date is Monday\n *\n * @example\n * // Is 22 September 2014 Monday?\n * const result = isMonday(new Date(2014, 8, 22))\n * //=> true\n */\nexport function isMonday(date, options) {\n  return toDate(date, options?.in).getDay() === 1;\n}\n\n// Fallback for modularized imports:\nexport default isMonday;\n"], "mappings": "AAAA,SAASA,MAAM,QAAQ,aAAa;;AAEpC;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,QAAQA,CAACC,IAAI,EAAEC,OAAO,EAAE;EACtC,OAAOH,MAAM,CAACE,IAAI,EAAEC,OAAO,EAAEC,EAAE,CAAC,CAACC,MAAM,CAAC,CAAC,KAAK,CAAC;AACjD;;AAEA;AACA,eAAeJ,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}