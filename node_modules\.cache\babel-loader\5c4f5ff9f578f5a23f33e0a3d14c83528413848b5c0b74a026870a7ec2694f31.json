{"ast": null, "code": "import GoTrueAdminApi from './GoTrueAdminApi';\nimport GoTrueClient from './GoTrueClient';\nimport AuthAdminApi from './AuthAdminApi';\nimport AuthClient from './AuthClient';\nexport { GoTrueAdminApi, GoTrueClient, AuthAdminApi, AuthClient };\nexport * from './lib/types';\nexport * from './lib/errors';\nexport { navigatorLock, NavigatorLockAcquireTimeoutError, internals as lockInternals, processLock } from './lib/locks';", "map": {"version": 3, "names": ["GoTrueAdminApi", "GoTrueClient", "AuthAdminApi", "AuthClient", "navigator<PERSON><PERSON>", "NavigatorLockAcquireTimeoutError", "internals", "lockInternals", "processLock"], "sources": ["C:\\Users\\<USER>\\lostandfound\\node_modules\\@supabase\\auth-js\\src\\index.ts"], "sourcesContent": ["import GoTrueAdminApi from './GoTrueAdminApi'\nimport GoTrueClient from './GoTrueClient'\nimport AuthAdmin<PERSON>pi from './AuthAdminApi'\nimport AuthClient from './AuthClient'\nexport { GoTrueAdminApi, GoTrueClient, AuthAdminApi, AuthClient }\nexport * from './lib/types'\nexport * from './lib/errors'\nexport {\n  navigatorLock,\n  NavigatorLockAcquireTimeoutError,\n  internals as lockInternals,\n  processLock,\n} from './lib/locks'\n"], "mappings": "AAAA,OAAOA,cAAc,MAAM,kBAAkB;AAC7C,OAAOC,YAAY,MAAM,gBAAgB;AACzC,OAAOC,YAAY,MAAM,gBAAgB;AACzC,OAAOC,UAAU,MAAM,cAAc;AACrC,SAASH,cAAc,EAAEC,YAAY,EAAEC,YAAY,EAAEC,UAAU;AAC/D,cAAc,aAAa;AAC3B,cAAc,cAAc;AAC5B,SACEC,aAAa,EACbC,gCAAgC,EAChCC,SAAS,IAAIC,aAAa,EAC1BC,WAAW,QACN,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}