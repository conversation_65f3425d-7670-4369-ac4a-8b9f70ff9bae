{"ast": null, "code": "var _s = $RefreshSig$();\n/**\n * Phone number utilities for normalizing and formatting phone numbers\n */\n\nimport { useState } from 'react';\n\n/**\n * Normalize a phone number to E.164 format\n * @param phone - Raw phone number input\n * @returns Normalized phone number or null if invalid\n */\nexport function normalizePhoneNumber(phone) {\n  if (!phone) return null;\n\n  // Remove all non-digit characters\n  const digits = phone.replace(/\\D/g, '');\n\n  // If empty after cleaning, return null\n  if (!digits) return null;\n\n  // If it's 10 digits, assume US number and add country code\n  if (digits.length === 10) {\n    return `+1${digits}`;\n  }\n\n  // If it's 11 digits and starts with 1, format as US number\n  if (digits.length === 11 && digits.startsWith('1')) {\n    return `+${digits}`;\n  }\n\n  // If it's 7 digits, it's missing area code - keep as is for manual review\n  if (digits.length === 7) {\n    return digits;\n  }\n\n  // For other lengths, return the digits (may need manual review)\n  return digits.length > 0 ? digits : null;\n}\n\n/**\n * Format a phone number for display\n * @param phone - Normalized phone number\n * @returns Formatted phone number for display\n */\nexport function formatPhoneNumber(phone) {\n  if (!phone) return '';\n\n  // If it's a normalized US number (+1XXXXXXXXXX)\n  if (phone.startsWith('+1') && phone.length === 12) {\n    const digits = phone.slice(2); // Remove +1\n    return `(${digits.slice(0, 3)}) ${digits.slice(3, 6)}-${digits.slice(6)}`;\n  }\n\n  // If it's 10 digits without country code\n  if (phone.length === 10 && /^\\d{10}$/.test(phone)) {\n    return `(${phone.slice(0, 3)}) ${phone.slice(3, 6)}-${phone.slice(6)}`;\n  }\n\n  // If it's 7 digits (missing area code)\n  if (phone.length === 7 && /^\\d{7}$/.test(phone)) {\n    return `${phone.slice(0, 3)}-${phone.slice(3)}`;\n  }\n\n  // For other formats, return as-is\n  return phone;\n}\n\n/**\n * Validate a phone number\n * @param phone - Phone number to validate\n * @returns Object with validation result and error message\n */\nexport function validatePhoneNumber(phone) {\n  if (!phone) {\n    return {\n      isValid: true\n    }; // Phone is optional\n  }\n  const digits = phone.replace(/\\D/g, '');\n  if (!digits) {\n    return {\n      isValid: false,\n      error: 'Phone number must contain digits'\n    };\n  }\n  if (digits.length < 7) {\n    return {\n      isValid: false,\n      error: 'Phone number must be at least 7 digits'\n    };\n  }\n  if (digits.length === 7) {\n    return {\n      isValid: true,\n      warning: 'Phone number appears to be missing area code'\n    };\n  }\n  if (digits.length === 10) {\n    return {\n      isValid: true\n    };\n  }\n  if (digits.length === 11 && digits.startsWith('1')) {\n    return {\n      isValid: true\n    };\n  }\n  if (digits.length > 15) {\n    return {\n      isValid: false,\n      error: 'Phone number is too long'\n    };\n  }\n  return {\n    isValid: true,\n    warning: 'Phone number format may need review'\n  };\n}\n\n/**\n * React hook for phone number input handling\n * @param initialValue - Initial phone number value\n * @returns Object with value, formatted display, validation, and change handler\n */\nexport function usePhoneNumber(initialValue = '') {\n  _s();\n  const [value, setValue] = useState(initialValue);\n  const [displayValue, setDisplayValue] = useState(formatPhoneNumber(initialValue));\n  const validation = validatePhoneNumber(value);\n  const normalizedValue = normalizePhoneNumber(value);\n  const handleChange = newValue => {\n    setValue(newValue);\n    setDisplayValue(formatPhoneNumber(newValue));\n  };\n  const handleBlur = () => {\n    // On blur, normalize and format the value\n    const normalized = normalizePhoneNumber(value);\n    if (normalized) {\n      setValue(normalized);\n      setDisplayValue(formatPhoneNumber(normalized));\n    }\n  };\n  return {\n    value,\n    displayValue,\n    normalizedValue,\n    validation,\n    handleChange,\n    handleBlur,\n    setValue: handleChange\n  };\n}\n\n// Note: React import would be needed for the hook, but keeping this file as utility functions\n// The hook can be implemented in components that need it\n_s(usePhoneNumber, \"7BOxVntT+x/LE+0Jq23iRl4Klg0=\");", "map": {"version": 3, "names": ["useState", "normalizePhoneNumber", "phone", "digits", "replace", "length", "startsWith", "formatPhoneNumber", "slice", "test", "validatePhoneNumber", "<PERSON><PERSON><PERSON><PERSON>", "error", "warning", "usePhoneNumber", "initialValue", "_s", "value", "setValue", "displayValue", "setDisplayValue", "validation", "normalizedValue", "handleChange", "newValue", "handleBlur", "normalized"], "sources": ["C:/Users/<USER>/lostandfound/discfinder-app/src/utils/phoneUtils.ts"], "sourcesContent": ["/**\n * Phone number utilities for normalizing and formatting phone numbers\n */\n\nimport { useState } from 'react';\n\n/**\n * Normalize a phone number to E.164 format\n * @param phone - Raw phone number input\n * @returns Normalized phone number or null if invalid\n */\nexport function normalizePhoneNumber(phone: string | null | undefined): string | null {\n  if (!phone) return null;\n  \n  // Remove all non-digit characters\n  const digits = phone.replace(/\\D/g, '');\n  \n  // If empty after cleaning, return null\n  if (!digits) return null;\n  \n  // If it's 10 digits, assume US number and add country code\n  if (digits.length === 10) {\n    return `+1${digits}`;\n  }\n  \n  // If it's 11 digits and starts with 1, format as US number\n  if (digits.length === 11 && digits.startsWith('1')) {\n    return `+${digits}`;\n  }\n  \n  // If it's 7 digits, it's missing area code - keep as is for manual review\n  if (digits.length === 7) {\n    return digits;\n  }\n  \n  // For other lengths, return the digits (may need manual review)\n  return digits.length > 0 ? digits : null;\n}\n\n/**\n * Format a phone number for display\n * @param phone - Normalized phone number\n * @returns Formatted phone number for display\n */\nexport function formatPhoneNumber(phone: string | null | undefined): string {\n  if (!phone) return '';\n  \n  // If it's a normalized US number (+1XXXXXXXXXX)\n  if (phone.startsWith('+1') && phone.length === 12) {\n    const digits = phone.slice(2); // Remove +1\n    return `(${digits.slice(0, 3)}) ${digits.slice(3, 6)}-${digits.slice(6)}`;\n  }\n  \n  // If it's 10 digits without country code\n  if (phone.length === 10 && /^\\d{10}$/.test(phone)) {\n    return `(${phone.slice(0, 3)}) ${phone.slice(3, 6)}-${phone.slice(6)}`;\n  }\n  \n  // If it's 7 digits (missing area code)\n  if (phone.length === 7 && /^\\d{7}$/.test(phone)) {\n    return `${phone.slice(0, 3)}-${phone.slice(3)}`;\n  }\n  \n  // For other formats, return as-is\n  return phone;\n}\n\n/**\n * Validate a phone number\n * @param phone - Phone number to validate\n * @returns Object with validation result and error message\n */\nexport function validatePhoneNumber(phone: string | null | undefined): {\n  isValid: boolean;\n  error?: string;\n  warning?: string;\n} {\n  if (!phone) {\n    return { isValid: true }; // Phone is optional\n  }\n  \n  const digits = phone.replace(/\\D/g, '');\n  \n  if (!digits) {\n    return { isValid: false, error: 'Phone number must contain digits' };\n  }\n  \n  if (digits.length < 7) {\n    return { isValid: false, error: 'Phone number must be at least 7 digits' };\n  }\n  \n  if (digits.length === 7) {\n    return { \n      isValid: true, \n      warning: 'Phone number appears to be missing area code' \n    };\n  }\n  \n  if (digits.length === 10) {\n    return { isValid: true };\n  }\n  \n  if (digits.length === 11 && digits.startsWith('1')) {\n    return { isValid: true };\n  }\n  \n  if (digits.length > 15) {\n    return { isValid: false, error: 'Phone number is too long' };\n  }\n  \n  return { \n    isValid: true, \n    warning: 'Phone number format may need review' \n  };\n}\n\n/**\n * React hook for phone number input handling\n * @param initialValue - Initial phone number value\n * @returns Object with value, formatted display, validation, and change handler\n */\nexport function usePhoneNumber(initialValue: string = '') {\n  const [value, setValue] = useState(initialValue);\n  const [displayValue, setDisplayValue] = useState(formatPhoneNumber(initialValue));\n  \n  const validation = validatePhoneNumber(value);\n  const normalizedValue = normalizePhoneNumber(value);\n  \n  const handleChange = (newValue: string) => {\n    setValue(newValue);\n    setDisplayValue(formatPhoneNumber(newValue));\n  };\n  \n  const handleBlur = () => {\n    // On blur, normalize and format the value\n    const normalized = normalizePhoneNumber(value);\n    if (normalized) {\n      setValue(normalized);\n      setDisplayValue(formatPhoneNumber(normalized));\n    }\n  };\n  \n  return {\n    value,\n    displayValue,\n    normalizedValue,\n    validation,\n    handleChange,\n    handleBlur,\n    setValue: handleChange\n  };\n}\n\n// Note: React import would be needed for the hook, but keeping this file as utility functions\n// The hook can be implemented in components that need it\n"], "mappings": ";AAAA;AACA;AACA;;AAEA,SAASA,QAAQ,QAAQ,OAAO;;AAEhC;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,oBAAoBA,CAACC,KAAgC,EAAiB;EACpF,IAAI,CAACA,KAAK,EAAE,OAAO,IAAI;;EAEvB;EACA,MAAMC,MAAM,GAAGD,KAAK,CAACE,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC;;EAEvC;EACA,IAAI,CAACD,MAAM,EAAE,OAAO,IAAI;;EAExB;EACA,IAAIA,MAAM,CAACE,MAAM,KAAK,EAAE,EAAE;IACxB,OAAO,KAAKF,MAAM,EAAE;EACtB;;EAEA;EACA,IAAIA,MAAM,CAACE,MAAM,KAAK,EAAE,IAAIF,MAAM,CAACG,UAAU,CAAC,GAAG,CAAC,EAAE;IAClD,OAAO,IAAIH,MAAM,EAAE;EACrB;;EAEA;EACA,IAAIA,MAAM,CAACE,MAAM,KAAK,CAAC,EAAE;IACvB,OAAOF,MAAM;EACf;;EAEA;EACA,OAAOA,MAAM,CAACE,MAAM,GAAG,CAAC,GAAGF,MAAM,GAAG,IAAI;AAC1C;;AAEA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASI,iBAAiBA,CAACL,KAAgC,EAAU;EAC1E,IAAI,CAACA,KAAK,EAAE,OAAO,EAAE;;EAErB;EACA,IAAIA,KAAK,CAACI,UAAU,CAAC,IAAI,CAAC,IAAIJ,KAAK,CAACG,MAAM,KAAK,EAAE,EAAE;IACjD,MAAMF,MAAM,GAAGD,KAAK,CAACM,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;IAC/B,OAAO,IAAIL,MAAM,CAACK,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,KAAKL,MAAM,CAACK,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,IAAIL,MAAM,CAACK,KAAK,CAAC,CAAC,CAAC,EAAE;EAC3E;;EAEA;EACA,IAAIN,KAAK,CAACG,MAAM,KAAK,EAAE,IAAI,UAAU,CAACI,IAAI,CAACP,KAAK,CAAC,EAAE;IACjD,OAAO,IAAIA,KAAK,CAACM,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,KAAKN,KAAK,CAACM,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,IAAIN,KAAK,CAACM,KAAK,CAAC,CAAC,CAAC,EAAE;EACxE;;EAEA;EACA,IAAIN,KAAK,CAACG,MAAM,KAAK,CAAC,IAAI,SAAS,CAACI,IAAI,CAACP,KAAK,CAAC,EAAE;IAC/C,OAAO,GAAGA,KAAK,CAACM,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,IAAIN,KAAK,CAACM,KAAK,CAAC,CAAC,CAAC,EAAE;EACjD;;EAEA;EACA,OAAON,KAAK;AACd;;AAEA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASQ,mBAAmBA,CAACR,KAAgC,EAIlE;EACA,IAAI,CAACA,KAAK,EAAE;IACV,OAAO;MAAES,OAAO,EAAE;IAAK,CAAC,CAAC,CAAC;EAC5B;EAEA,MAAMR,MAAM,GAAGD,KAAK,CAACE,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC;EAEvC,IAAI,CAACD,MAAM,EAAE;IACX,OAAO;MAAEQ,OAAO,EAAE,KAAK;MAAEC,KAAK,EAAE;IAAmC,CAAC;EACtE;EAEA,IAAIT,MAAM,CAACE,MAAM,GAAG,CAAC,EAAE;IACrB,OAAO;MAAEM,OAAO,EAAE,KAAK;MAAEC,KAAK,EAAE;IAAyC,CAAC;EAC5E;EAEA,IAAIT,MAAM,CAACE,MAAM,KAAK,CAAC,EAAE;IACvB,OAAO;MACLM,OAAO,EAAE,IAAI;MACbE,OAAO,EAAE;IACX,CAAC;EACH;EAEA,IAAIV,MAAM,CAACE,MAAM,KAAK,EAAE,EAAE;IACxB,OAAO;MAAEM,OAAO,EAAE;IAAK,CAAC;EAC1B;EAEA,IAAIR,MAAM,CAACE,MAAM,KAAK,EAAE,IAAIF,MAAM,CAACG,UAAU,CAAC,GAAG,CAAC,EAAE;IAClD,OAAO;MAAEK,OAAO,EAAE;IAAK,CAAC;EAC1B;EAEA,IAAIR,MAAM,CAACE,MAAM,GAAG,EAAE,EAAE;IACtB,OAAO;MAAEM,OAAO,EAAE,KAAK;MAAEC,KAAK,EAAE;IAA2B,CAAC;EAC9D;EAEA,OAAO;IACLD,OAAO,EAAE,IAAI;IACbE,OAAO,EAAE;EACX,CAAC;AACH;;AAEA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,cAAcA,CAACC,YAAoB,GAAG,EAAE,EAAE;EAAAC,EAAA;EACxD,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAGlB,QAAQ,CAACe,YAAY,CAAC;EAChD,MAAM,CAACI,YAAY,EAAEC,eAAe,CAAC,GAAGpB,QAAQ,CAACO,iBAAiB,CAACQ,YAAY,CAAC,CAAC;EAEjF,MAAMM,UAAU,GAAGX,mBAAmB,CAACO,KAAK,CAAC;EAC7C,MAAMK,eAAe,GAAGrB,oBAAoB,CAACgB,KAAK,CAAC;EAEnD,MAAMM,YAAY,GAAIC,QAAgB,IAAK;IACzCN,QAAQ,CAACM,QAAQ,CAAC;IAClBJ,eAAe,CAACb,iBAAiB,CAACiB,QAAQ,CAAC,CAAC;EAC9C,CAAC;EAED,MAAMC,UAAU,GAAGA,CAAA,KAAM;IACvB;IACA,MAAMC,UAAU,GAAGzB,oBAAoB,CAACgB,KAAK,CAAC;IAC9C,IAAIS,UAAU,EAAE;MACdR,QAAQ,CAACQ,UAAU,CAAC;MACpBN,eAAe,CAACb,iBAAiB,CAACmB,UAAU,CAAC,CAAC;IAChD;EACF,CAAC;EAED,OAAO;IACLT,KAAK;IACLE,YAAY;IACZG,eAAe;IACfD,UAAU;IACVE,YAAY;IACZE,UAAU;IACVP,QAAQ,EAAEK;EACZ,CAAC;AACH;;AAEA;AACA;AAAAP,EAAA,CAjCgBF,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}