{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\lostandfound\\\\discfinder-app\\\\src\\\\components\\\\SourceManager.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { supabaseService } from '../lib/supabase';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst SourceManager = ({\n  onClose\n}) => {\n  _s();\n  const [sources, setSources] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [editingSource, setEditingSource] = useState(null);\n  const [newSource, setNewSource] = useState({\n    name: '',\n    description: '',\n    is_active: true,\n    sort_order: 0,\n    legacy_row_id: ''\n  });\n  const [showAddForm, setShowAddForm] = useState(false);\n  useEffect(() => {\n    loadSources();\n  }, []);\n  const loadSources = async () => {\n    try {\n      setLoading(true);\n      const data = await supabaseService.getSources();\n      setSources(data);\n    } catch (err) {\n      setError('Failed to load sources');\n      console.error('Error loading sources:', err);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleCreateSource = async e => {\n    e.preventDefault();\n    try {\n      await supabaseService.createSource(newSource);\n      setNewSource({\n        name: '',\n        description: '',\n        is_active: true,\n        sort_order: 0\n      });\n      setShowAddForm(false);\n      await loadSources();\n    } catch (err) {\n      setError('Failed to create source');\n      console.error('Error creating source:', err);\n    }\n  };\n  const handleUpdateSource = async source => {\n    try {\n      await supabaseService.updateSource(source.id, {\n        name: source.name,\n        description: source.description,\n        is_active: source.is_active,\n        sort_order: source.sort_order\n      });\n      setEditingSource(null);\n      await loadSources();\n    } catch (err) {\n      setError('Failed to update source');\n      console.error('Error updating source:', err);\n    }\n  };\n  const handleToggleActive = async source => {\n    try {\n      await supabaseService.updateSource(source.id, {\n        is_active: !source.is_active\n      });\n      await loadSources();\n    } catch (err) {\n      setError('Failed to update source status');\n      console.error('Error updating source status:', err);\n    }\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-lg p-6\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center\",\n          children: \"Loading sources...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 84,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 83,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 82,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white rounded-lg p-6 max-w-4xl w-full max-h-[90vh] overflow-y-auto\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex justify-between items-center mb-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-2xl font-bold\",\n          children: \"Manage Sources\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 94,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: onClose,\n          className: \"text-gray-500 hover:text-gray-700 text-2xl\",\n          children: \"\\xD7\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 95,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 93,\n        columnNumber: 9\n      }, this), error && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4\",\n        children: error\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 104,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mb-6\",\n        children: /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setShowAddForm(!showAddForm),\n          className: \"bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700\",\n          children: showAddForm ? 'Cancel' : 'Add New Source'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 110,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 109,\n        columnNumber: 9\n      }, this), showAddForm && /*#__PURE__*/_jsxDEV(\"form\", {\n        onSubmit: handleCreateSource,\n        className: \"mb-6 p-4 border rounded\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-lg font-semibold mb-4\",\n          children: \"Add New Source\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 120,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-sm font-medium mb-1\",\n              children: \"Name *\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 123,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              value: newSource.name,\n              onChange: e => setNewSource({\n                ...newSource,\n                name: e.target.value\n              }),\n              required: true,\n              className: \"w-full border rounded px-3 py-2\",\n              placeholder: \"e.g., Jones Park, Emporia\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 124,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 122,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-sm font-medium mb-1\",\n              children: \"Sort Order\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 134,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"number\",\n              value: newSource.sort_order,\n              onChange: e => setNewSource({\n                ...newSource,\n                sort_order: parseInt(e.target.value) || 0\n              }),\n              className: \"w-full border rounded px-3 py-2\",\n              placeholder: \"0\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 135,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 133,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"md:col-span-2\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-sm font-medium mb-1\",\n              children: \"Description\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 144,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n              value: newSource.description,\n              onChange: e => setNewSource({\n                ...newSource,\n                description: e.target.value\n              }),\n              className: \"w-full border rounded px-3 py-2\",\n              rows: 2,\n              placeholder: \"Optional description or additional context\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 145,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 143,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"md:col-span-2\",\n            children: /*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"flex items-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"checkbox\",\n                checked: newSource.is_active,\n                onChange: e => setNewSource({\n                  ...newSource,\n                  is_active: e.target.checked\n                }),\n                className: \"mr-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 155,\n                columnNumber: 19\n              }, this), \"Active (visible in dropdown)\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 154,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 153,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 121,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mt-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"submit\",\n            className: \"bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700 mr-2\",\n            children: \"Create Source\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 166,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"button\",\n            onClick: () => setShowAddForm(false),\n            className: \"bg-gray-500 text-white px-4 py-2 rounded hover:bg-gray-600\",\n            children: \"Cancel\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 172,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 165,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 119,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"space-y-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-lg font-semibold\",\n          children: \"Existing Sources\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 184,\n          columnNumber: 11\n        }, this), sources.length === 0 ? /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-500\",\n          children: \"No sources found.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 186,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-2\",\n          children: sources.map(source => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: `border rounded p-4 ${source.is_active ? 'bg-white' : 'bg-gray-100'}`,\n            children: (editingSource === null || editingSource === void 0 ? void 0 : editingSource.id) === source.id ? /*#__PURE__*/_jsxDEV(EditSourceForm, {\n              source: editingSource,\n              onSave: handleUpdateSource,\n              onCancel: () => setEditingSource(null),\n              onChange: setEditingSource\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 195,\n              columnNumber: 21\n            }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex justify-between items-start\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex-1\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center gap-2\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                    className: \"font-medium\",\n                    children: source.name\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 205,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: `px-2 py-1 text-xs rounded ${source.is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`,\n                    children: source.is_active ? 'Active' : 'Inactive'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 206,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-sm text-gray-500\",\n                    children: [\"Order: \", source.sort_order]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 213,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 204,\n                  columnNumber: 25\n                }, this), source.description && /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-sm text-gray-600 mt-1\",\n                  children: source.description\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 218,\n                  columnNumber: 27\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 203,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex gap-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => setEditingSource(source),\n                  className: \"text-blue-600 hover:text-blue-800 text-sm\",\n                  children: \"Edit\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 222,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => handleToggleActive(source),\n                  className: `text-sm ${source.is_active ? 'text-red-600 hover:text-red-800' : 'text-green-600 hover:text-green-800'}`,\n                  children: source.is_active ? 'Deactivate' : 'Activate'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 228,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 221,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 202,\n              columnNumber: 21\n            }, this)\n          }, source.id, false, {\n            fileName: _jsxFileName,\n            lineNumber: 190,\n            columnNumber: 17\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 188,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 183,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 92,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 91,\n    columnNumber: 5\n  }, this);\n};\n_s(SourceManager, \"80rxbwHEpwnK2GiyHkIQ1dyQPMY=\");\n_c = SourceManager;\nconst EditSourceForm = ({\n  source,\n  onSave,\n  onCancel,\n  onChange\n}) => {\n  const handleSubmit = e => {\n    e.preventDefault();\n    onSave(source);\n  };\n  return /*#__PURE__*/_jsxDEV(\"form\", {\n    onSubmit: handleSubmit,\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          className: \"block text-sm font-medium mb-1\",\n          children: \"Name *\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 268,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"text\",\n          value: source.name,\n          onChange: e => onChange({\n            ...source,\n            name: e.target.value\n          }),\n          required: true,\n          className: \"w-full border rounded px-3 py-2\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 269,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 267,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          className: \"block text-sm font-medium mb-1\",\n          children: \"Sort Order\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 278,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"number\",\n          value: source.sort_order,\n          onChange: e => onChange({\n            ...source,\n            sort_order: parseInt(e.target.value) || 0\n          }),\n          className: \"w-full border rounded px-3 py-2\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 279,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 277,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"md:col-span-2\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          className: \"block text-sm font-medium mb-1\",\n          children: \"Description\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 287,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n          value: source.description || '',\n          onChange: e => onChange({\n            ...source,\n            description: e.target.value\n          }),\n          className: \"w-full border rounded px-3 py-2\",\n          rows: 2\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 288,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 286,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"md:col-span-2\",\n        children: /*#__PURE__*/_jsxDEV(\"label\", {\n          className: \"flex items-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"checkbox\",\n            checked: source.is_active,\n            onChange: e => onChange({\n              ...source,\n              is_active: e.target.checked\n            }),\n            className: \"mr-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 297,\n            columnNumber: 13\n          }, this), \"Active (visible in dropdown)\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 296,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 295,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 266,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mt-4\",\n      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n        type: \"submit\",\n        className: \"bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700 mr-2\",\n        children: \"Save Changes\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 308,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        type: \"button\",\n        onClick: onCancel,\n        className: \"bg-gray-500 text-white px-4 py-2 rounded hover:bg-gray-600\",\n        children: \"Cancel\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 314,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 307,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 265,\n    columnNumber: 5\n  }, this);\n};\n_c2 = EditSourceForm;\nexport default SourceManager;\nvar _c, _c2;\n$RefreshReg$(_c, \"SourceManager\");\n$RefreshReg$(_c2, \"EditSourceForm\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "supabaseService", "jsxDEV", "_jsxDEV", "SourceManager", "onClose", "_s", "sources", "setSources", "loading", "setLoading", "error", "setError", "editingSource", "setEditingSource", "newSource", "setNewSource", "name", "description", "is_active", "sort_order", "legacy_row_id", "showAddForm", "setShowAddForm", "loadSources", "data", "getSources", "err", "console", "handleCreateSource", "e", "preventDefault", "createSource", "handleUpdateSource", "source", "updateSource", "id", "handleToggleActive", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "onSubmit", "type", "value", "onChange", "target", "required", "placeholder", "parseInt", "rows", "checked", "length", "map", "EditSourceForm", "onSave", "onCancel", "_c", "handleSubmit", "_c2", "$RefreshReg$"], "sources": ["C:/Users/<USER>/lostandfound/discfinder-app/src/components/SourceManager.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { supabaseService, Source } from '../lib/supabase';\n\ninterface SourceManagerProps {\n  onClose: () => void;\n}\n\nconst SourceManager: React.FC<SourceManagerProps> = ({ onClose }) => {\n  const [sources, setSources] = useState<Source[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n  const [editingSource, setEditingSource] = useState<Source | null>(null);\n  const [newSource, setNewSource] = useState({\n    name: '',\n    description: '',\n    is_active: true,\n    sort_order: 0,\n    legacy_row_id: ''\n  });\n  const [showAddForm, setShowAddForm] = useState(false);\n\n  useEffect(() => {\n    loadSources();\n  }, []);\n\n  const loadSources = async () => {\n    try {\n      setLoading(true);\n      const data = await supabaseService.getSources();\n      setSources(data);\n    } catch (err) {\n      setError('Failed to load sources');\n      console.error('Error loading sources:', err);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleCreateSource = async (e: React.FormEvent) => {\n    e.preventDefault();\n    try {\n      await supabaseService.createSource(newSource);\n      setNewSource({ name: '', description: '', is_active: true, sort_order: 0 });\n      setShowAddForm(false);\n      await loadSources();\n    } catch (err) {\n      setError('Failed to create source');\n      console.error('Error creating source:', err);\n    }\n  };\n\n  const handleUpdateSource = async (source: Source) => {\n    try {\n      await supabaseService.updateSource(source.id, {\n        name: source.name,\n        description: source.description,\n        is_active: source.is_active,\n        sort_order: source.sort_order\n      });\n      setEditingSource(null);\n      await loadSources();\n    } catch (err) {\n      setError('Failed to update source');\n      console.error('Error updating source:', err);\n    }\n  };\n\n  const handleToggleActive = async (source: Source) => {\n    try {\n      await supabaseService.updateSource(source.id, {\n        is_active: !source.is_active\n      });\n      await loadSources();\n    } catch (err) {\n      setError('Failed to update source status');\n      console.error('Error updating source status:', err);\n    }\n  };\n\n  if (loading) {\n    return (\n      <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\">\n        <div className=\"bg-white rounded-lg p-6\">\n          <div className=\"text-center\">Loading sources...</div>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\">\n      <div className=\"bg-white rounded-lg p-6 max-w-4xl w-full max-h-[90vh] overflow-y-auto\">\n        <div className=\"flex justify-between items-center mb-6\">\n          <h2 className=\"text-2xl font-bold\">Manage Sources</h2>\n          <button\n            onClick={onClose}\n            className=\"text-gray-500 hover:text-gray-700 text-2xl\"\n          >\n            ×\n          </button>\n        </div>\n\n        {error && (\n          <div className=\"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4\">\n            {error}\n          </div>\n        )}\n\n        <div className=\"mb-6\">\n          <button\n            onClick={() => setShowAddForm(!showAddForm)}\n            className=\"bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700\"\n          >\n            {showAddForm ? 'Cancel' : 'Add New Source'}\n          </button>\n        </div>\n\n        {showAddForm && (\n          <form onSubmit={handleCreateSource} className=\"mb-6 p-4 border rounded\">\n            <h3 className=\"text-lg font-semibold mb-4\">Add New Source</h3>\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n              <div>\n                <label className=\"block text-sm font-medium mb-1\">Name *</label>\n                <input\n                  type=\"text\"\n                  value={newSource.name}\n                  onChange={(e) => setNewSource({ ...newSource, name: e.target.value })}\n                  required\n                  className=\"w-full border rounded px-3 py-2\"\n                  placeholder=\"e.g., Jones Park, Emporia\"\n                />\n              </div>\n              <div>\n                <label className=\"block text-sm font-medium mb-1\">Sort Order</label>\n                <input\n                  type=\"number\"\n                  value={newSource.sort_order}\n                  onChange={(e) => setNewSource({ ...newSource, sort_order: parseInt(e.target.value) || 0 })}\n                  className=\"w-full border rounded px-3 py-2\"\n                  placeholder=\"0\"\n                />\n              </div>\n              <div className=\"md:col-span-2\">\n                <label className=\"block text-sm font-medium mb-1\">Description</label>\n                <textarea\n                  value={newSource.description}\n                  onChange={(e) => setNewSource({ ...newSource, description: e.target.value })}\n                  className=\"w-full border rounded px-3 py-2\"\n                  rows={2}\n                  placeholder=\"Optional description or additional context\"\n                />\n              </div>\n              <div className=\"md:col-span-2\">\n                <label className=\"flex items-center\">\n                  <input\n                    type=\"checkbox\"\n                    checked={newSource.is_active}\n                    onChange={(e) => setNewSource({ ...newSource, is_active: e.target.checked })}\n                    className=\"mr-2\"\n                  />\n                  Active (visible in dropdown)\n                </label>\n              </div>\n            </div>\n            <div className=\"mt-4\">\n              <button\n                type=\"submit\"\n                className=\"bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700 mr-2\"\n              >\n                Create Source\n              </button>\n              <button\n                type=\"button\"\n                onClick={() => setShowAddForm(false)}\n                className=\"bg-gray-500 text-white px-4 py-2 rounded hover:bg-gray-600\"\n              >\n                Cancel\n              </button>\n            </div>\n          </form>\n        )}\n\n        <div className=\"space-y-4\">\n          <h3 className=\"text-lg font-semibold\">Existing Sources</h3>\n          {sources.length === 0 ? (\n            <p className=\"text-gray-500\">No sources found.</p>\n          ) : (\n            <div className=\"space-y-2\">\n              {sources.map((source) => (\n                <div\n                  key={source.id}\n                  className={`border rounded p-4 ${source.is_active ? 'bg-white' : 'bg-gray-100'}`}\n                >\n                  {editingSource?.id === source.id ? (\n                    <EditSourceForm\n                      source={editingSource}\n                      onSave={handleUpdateSource}\n                      onCancel={() => setEditingSource(null)}\n                      onChange={setEditingSource}\n                    />\n                  ) : (\n                    <div className=\"flex justify-between items-start\">\n                      <div className=\"flex-1\">\n                        <div className=\"flex items-center gap-2\">\n                          <h4 className=\"font-medium\">{source.name}</h4>\n                          <span className={`px-2 py-1 text-xs rounded ${\n                            source.is_active \n                              ? 'bg-green-100 text-green-800' \n                              : 'bg-red-100 text-red-800'\n                          }`}>\n                            {source.is_active ? 'Active' : 'Inactive'}\n                          </span>\n                          <span className=\"text-sm text-gray-500\">\n                            Order: {source.sort_order}\n                          </span>\n                        </div>\n                        {source.description && (\n                          <p className=\"text-sm text-gray-600 mt-1\">{source.description}</p>\n                        )}\n                      </div>\n                      <div className=\"flex gap-2\">\n                        <button\n                          onClick={() => setEditingSource(source)}\n                          className=\"text-blue-600 hover:text-blue-800 text-sm\"\n                        >\n                          Edit\n                        </button>\n                        <button\n                          onClick={() => handleToggleActive(source)}\n                          className={`text-sm ${\n                            source.is_active \n                              ? 'text-red-600 hover:text-red-800' \n                              : 'text-green-600 hover:text-green-800'\n                          }`}\n                        >\n                          {source.is_active ? 'Deactivate' : 'Activate'}\n                        </button>\n                      </div>\n                    </div>\n                  )}\n                </div>\n              ))}\n            </div>\n          )}\n        </div>\n      </div>\n    </div>\n  );\n};\n\ninterface EditSourceFormProps {\n  source: Source;\n  onSave: (source: Source) => void;\n  onCancel: () => void;\n  onChange: (source: Source) => void;\n}\n\nconst EditSourceForm: React.FC<EditSourceFormProps> = ({ source, onSave, onCancel, onChange }) => {\n  const handleSubmit = (e: React.FormEvent) => {\n    e.preventDefault();\n    onSave(source);\n  };\n\n  return (\n    <form onSubmit={handleSubmit}>\n      <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n        <div>\n          <label className=\"block text-sm font-medium mb-1\">Name *</label>\n          <input\n            type=\"text\"\n            value={source.name}\n            onChange={(e) => onChange({ ...source, name: e.target.value })}\n            required\n            className=\"w-full border rounded px-3 py-2\"\n          />\n        </div>\n        <div>\n          <label className=\"block text-sm font-medium mb-1\">Sort Order</label>\n          <input\n            type=\"number\"\n            value={source.sort_order}\n            onChange={(e) => onChange({ ...source, sort_order: parseInt(e.target.value) || 0 })}\n            className=\"w-full border rounded px-3 py-2\"\n          />\n        </div>\n        <div className=\"md:col-span-2\">\n          <label className=\"block text-sm font-medium mb-1\">Description</label>\n          <textarea\n            value={source.description || ''}\n            onChange={(e) => onChange({ ...source, description: e.target.value })}\n            className=\"w-full border rounded px-3 py-2\"\n            rows={2}\n          />\n        </div>\n        <div className=\"md:col-span-2\">\n          <label className=\"flex items-center\">\n            <input\n              type=\"checkbox\"\n              checked={source.is_active}\n              onChange={(e) => onChange({ ...source, is_active: e.target.checked })}\n              className=\"mr-2\"\n            />\n            Active (visible in dropdown)\n          </label>\n        </div>\n      </div>\n      <div className=\"mt-4\">\n        <button\n          type=\"submit\"\n          className=\"bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700 mr-2\"\n        >\n          Save Changes\n        </button>\n        <button\n          type=\"button\"\n          onClick={onCancel}\n          className=\"bg-gray-500 text-white px-4 py-2 rounded hover:bg-gray-600\"\n        >\n          Cancel\n        </button>\n      </div>\n    </form>\n  );\n};\n\nexport default SourceManager;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,eAAe,QAAgB,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAM1D,MAAMC,aAA2C,GAAGA,CAAC;EAAEC;AAAQ,CAAC,KAAK;EAAAC,EAAA;EACnE,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGT,QAAQ,CAAW,EAAE,CAAC;EACpD,MAAM,CAACU,OAAO,EAAEC,UAAU,CAAC,GAAGX,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACY,KAAK,EAAEC,QAAQ,CAAC,GAAGb,QAAQ,CAAgB,IAAI,CAAC;EACvD,MAAM,CAACc,aAAa,EAAEC,gBAAgB,CAAC,GAAGf,QAAQ,CAAgB,IAAI,CAAC;EACvE,MAAM,CAACgB,SAAS,EAAEC,YAAY,CAAC,GAAGjB,QAAQ,CAAC;IACzCkB,IAAI,EAAE,EAAE;IACRC,WAAW,EAAE,EAAE;IACfC,SAAS,EAAE,IAAI;IACfC,UAAU,EAAE,CAAC;IACbC,aAAa,EAAE;EACjB,CAAC,CAAC;EACF,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGxB,QAAQ,CAAC,KAAK,CAAC;EAErDC,SAAS,CAAC,MAAM;IACdwB,WAAW,CAAC,CAAC;EACf,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMA,WAAW,GAAG,MAAAA,CAAA,KAAY;IAC9B,IAAI;MACFd,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMe,IAAI,GAAG,MAAMxB,eAAe,CAACyB,UAAU,CAAC,CAAC;MAC/ClB,UAAU,CAACiB,IAAI,CAAC;IAClB,CAAC,CAAC,OAAOE,GAAG,EAAE;MACZf,QAAQ,CAAC,wBAAwB,CAAC;MAClCgB,OAAO,CAACjB,KAAK,CAAC,wBAAwB,EAAEgB,GAAG,CAAC;IAC9C,CAAC,SAAS;MACRjB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMmB,kBAAkB,GAAG,MAAOC,CAAkB,IAAK;IACvDA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClB,IAAI;MACF,MAAM9B,eAAe,CAAC+B,YAAY,CAACjB,SAAS,CAAC;MAC7CC,YAAY,CAAC;QAAEC,IAAI,EAAE,EAAE;QAAEC,WAAW,EAAE,EAAE;QAAEC,SAAS,EAAE,IAAI;QAAEC,UAAU,EAAE;MAAE,CAAC,CAAC;MAC3EG,cAAc,CAAC,KAAK,CAAC;MACrB,MAAMC,WAAW,CAAC,CAAC;IACrB,CAAC,CAAC,OAAOG,GAAG,EAAE;MACZf,QAAQ,CAAC,yBAAyB,CAAC;MACnCgB,OAAO,CAACjB,KAAK,CAAC,wBAAwB,EAAEgB,GAAG,CAAC;IAC9C;EACF,CAAC;EAED,MAAMM,kBAAkB,GAAG,MAAOC,MAAc,IAAK;IACnD,IAAI;MACF,MAAMjC,eAAe,CAACkC,YAAY,CAACD,MAAM,CAACE,EAAE,EAAE;QAC5CnB,IAAI,EAAEiB,MAAM,CAACjB,IAAI;QACjBC,WAAW,EAAEgB,MAAM,CAAChB,WAAW;QAC/BC,SAAS,EAAEe,MAAM,CAACf,SAAS;QAC3BC,UAAU,EAAEc,MAAM,CAACd;MACrB,CAAC,CAAC;MACFN,gBAAgB,CAAC,IAAI,CAAC;MACtB,MAAMU,WAAW,CAAC,CAAC;IACrB,CAAC,CAAC,OAAOG,GAAG,EAAE;MACZf,QAAQ,CAAC,yBAAyB,CAAC;MACnCgB,OAAO,CAACjB,KAAK,CAAC,wBAAwB,EAAEgB,GAAG,CAAC;IAC9C;EACF,CAAC;EAED,MAAMU,kBAAkB,GAAG,MAAOH,MAAc,IAAK;IACnD,IAAI;MACF,MAAMjC,eAAe,CAACkC,YAAY,CAACD,MAAM,CAACE,EAAE,EAAE;QAC5CjB,SAAS,EAAE,CAACe,MAAM,CAACf;MACrB,CAAC,CAAC;MACF,MAAMK,WAAW,CAAC,CAAC;IACrB,CAAC,CAAC,OAAOG,GAAG,EAAE;MACZf,QAAQ,CAAC,gCAAgC,CAAC;MAC1CgB,OAAO,CAACjB,KAAK,CAAC,+BAA+B,EAAEgB,GAAG,CAAC;IACrD;EACF,CAAC;EAED,IAAIlB,OAAO,EAAE;IACX,oBACEN,OAAA;MAAKmC,SAAS,EAAC,4EAA4E;MAAAC,QAAA,eACzFpC,OAAA;QAAKmC,SAAS,EAAC,yBAAyB;QAAAC,QAAA,eACtCpC,OAAA;UAAKmC,SAAS,EAAC,aAAa;UAAAC,QAAA,EAAC;QAAkB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,oBACExC,OAAA;IAAKmC,SAAS,EAAC,4EAA4E;IAAAC,QAAA,eACzFpC,OAAA;MAAKmC,SAAS,EAAC,uEAAuE;MAAAC,QAAA,gBACpFpC,OAAA;QAAKmC,SAAS,EAAC,wCAAwC;QAAAC,QAAA,gBACrDpC,OAAA;UAAImC,SAAS,EAAC,oBAAoB;UAAAC,QAAA,EAAC;QAAc;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACtDxC,OAAA;UACEyC,OAAO,EAAEvC,OAAQ;UACjBiC,SAAS,EAAC,4CAA4C;UAAAC,QAAA,EACvD;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,EAELhC,KAAK,iBACJR,OAAA;QAAKmC,SAAS,EAAC,sEAAsE;QAAAC,QAAA,EAClF5B;MAAK;QAAA6B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,eAEDxC,OAAA;QAAKmC,SAAS,EAAC,MAAM;QAAAC,QAAA,eACnBpC,OAAA;UACEyC,OAAO,EAAEA,CAAA,KAAMrB,cAAc,CAAC,CAACD,WAAW,CAAE;UAC5CgB,SAAS,EAAC,4DAA4D;UAAAC,QAAA,EAErEjB,WAAW,GAAG,QAAQ,GAAG;QAAgB;UAAAkB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,EAELrB,WAAW,iBACVnB,OAAA;QAAM0C,QAAQ,EAAEhB,kBAAmB;QAACS,SAAS,EAAC,yBAAyB;QAAAC,QAAA,gBACrEpC,OAAA;UAAImC,SAAS,EAAC,4BAA4B;UAAAC,QAAA,EAAC;QAAc;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC9DxC,OAAA;UAAKmC,SAAS,EAAC,uCAAuC;UAAAC,QAAA,gBACpDpC,OAAA;YAAAoC,QAAA,gBACEpC,OAAA;cAAOmC,SAAS,EAAC,gCAAgC;cAAAC,QAAA,EAAC;YAAM;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAChExC,OAAA;cACE2C,IAAI,EAAC,MAAM;cACXC,KAAK,EAAEhC,SAAS,CAACE,IAAK;cACtB+B,QAAQ,EAAGlB,CAAC,IAAKd,YAAY,CAAC;gBAAE,GAAGD,SAAS;gBAAEE,IAAI,EAAEa,CAAC,CAACmB,MAAM,CAACF;cAAM,CAAC,CAAE;cACtEG,QAAQ;cACRZ,SAAS,EAAC,iCAAiC;cAC3Ca,WAAW,EAAC;YAA2B;cAAAX,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eACNxC,OAAA;YAAAoC,QAAA,gBACEpC,OAAA;cAAOmC,SAAS,EAAC,gCAAgC;cAAAC,QAAA,EAAC;YAAU;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACpExC,OAAA;cACE2C,IAAI,EAAC,QAAQ;cACbC,KAAK,EAAEhC,SAAS,CAACK,UAAW;cAC5B4B,QAAQ,EAAGlB,CAAC,IAAKd,YAAY,CAAC;gBAAE,GAAGD,SAAS;gBAAEK,UAAU,EAAEgC,QAAQ,CAACtB,CAAC,CAACmB,MAAM,CAACF,KAAK,CAAC,IAAI;cAAE,CAAC,CAAE;cAC3FT,SAAS,EAAC,iCAAiC;cAC3Ca,WAAW,EAAC;YAAG;cAAAX,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eACNxC,OAAA;YAAKmC,SAAS,EAAC,eAAe;YAAAC,QAAA,gBAC5BpC,OAAA;cAAOmC,SAAS,EAAC,gCAAgC;cAAAC,QAAA,EAAC;YAAW;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACrExC,OAAA;cACE4C,KAAK,EAAEhC,SAAS,CAACG,WAAY;cAC7B8B,QAAQ,EAAGlB,CAAC,IAAKd,YAAY,CAAC;gBAAE,GAAGD,SAAS;gBAAEG,WAAW,EAAEY,CAAC,CAACmB,MAAM,CAACF;cAAM,CAAC,CAAE;cAC7ET,SAAS,EAAC,iCAAiC;cAC3Ce,IAAI,EAAE,CAAE;cACRF,WAAW,EAAC;YAA4C;cAAAX,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eACNxC,OAAA;YAAKmC,SAAS,EAAC,eAAe;YAAAC,QAAA,eAC5BpC,OAAA;cAAOmC,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBAClCpC,OAAA;gBACE2C,IAAI,EAAC,UAAU;gBACfQ,OAAO,EAAEvC,SAAS,CAACI,SAAU;gBAC7B6B,QAAQ,EAAGlB,CAAC,IAAKd,YAAY,CAAC;kBAAE,GAAGD,SAAS;kBAAEI,SAAS,EAAEW,CAAC,CAACmB,MAAM,CAACK;gBAAQ,CAAC,CAAE;gBAC7EhB,SAAS,EAAC;cAAM;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjB,CAAC,gCAEJ;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACNxC,OAAA;UAAKmC,SAAS,EAAC,MAAM;UAAAC,QAAA,gBACnBpC,OAAA;YACE2C,IAAI,EAAC,QAAQ;YACbR,SAAS,EAAC,mEAAmE;YAAAC,QAAA,EAC9E;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTxC,OAAA;YACE2C,IAAI,EAAC,QAAQ;YACbF,OAAO,EAAEA,CAAA,KAAMrB,cAAc,CAAC,KAAK,CAAE;YACrCe,SAAS,EAAC,4DAA4D;YAAAC,QAAA,EACvE;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CACP,eAEDxC,OAAA;QAAKmC,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACxBpC,OAAA;UAAImC,SAAS,EAAC,uBAAuB;UAAAC,QAAA,EAAC;QAAgB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,EAC1DpC,OAAO,CAACgD,MAAM,KAAK,CAAC,gBACnBpD,OAAA;UAAGmC,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAAiB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,gBAElDxC,OAAA;UAAKmC,SAAS,EAAC,WAAW;UAAAC,QAAA,EACvBhC,OAAO,CAACiD,GAAG,CAAEtB,MAAM,iBAClB/B,OAAA;YAEEmC,SAAS,EAAE,sBAAsBJ,MAAM,CAACf,SAAS,GAAG,UAAU,GAAG,aAAa,EAAG;YAAAoB,QAAA,EAEhF,CAAA1B,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAEuB,EAAE,MAAKF,MAAM,CAACE,EAAE,gBAC9BjC,OAAA,CAACsD,cAAc;cACbvB,MAAM,EAAErB,aAAc;cACtB6C,MAAM,EAAEzB,kBAAmB;cAC3B0B,QAAQ,EAAEA,CAAA,KAAM7C,gBAAgB,CAAC,IAAI,CAAE;cACvCkC,QAAQ,EAAElC;YAAiB;cAAA0B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5B,CAAC,gBAEFxC,OAAA;cAAKmC,SAAS,EAAC,kCAAkC;cAAAC,QAAA,gBAC/CpC,OAAA;gBAAKmC,SAAS,EAAC,QAAQ;gBAAAC,QAAA,gBACrBpC,OAAA;kBAAKmC,SAAS,EAAC,yBAAyB;kBAAAC,QAAA,gBACtCpC,OAAA;oBAAImC,SAAS,EAAC,aAAa;oBAAAC,QAAA,EAAEL,MAAM,CAACjB;kBAAI;oBAAAuB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eAC9CxC,OAAA;oBAAMmC,SAAS,EAAE,6BACfJ,MAAM,CAACf,SAAS,GACZ,6BAA6B,GAC7B,yBAAyB,EAC5B;oBAAAoB,QAAA,EACAL,MAAM,CAACf,SAAS,GAAG,QAAQ,GAAG;kBAAU;oBAAAqB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACrC,CAAC,eACPxC,OAAA;oBAAMmC,SAAS,EAAC,uBAAuB;oBAAAC,QAAA,GAAC,SAC/B,EAACL,MAAM,CAACd,UAAU;kBAAA;oBAAAoB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACrB,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC,EACLT,MAAM,CAAChB,WAAW,iBACjBf,OAAA;kBAAGmC,SAAS,EAAC,4BAA4B;kBAAAC,QAAA,EAAEL,MAAM,CAAChB;gBAAW;kBAAAsB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAClE;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eACNxC,OAAA;gBAAKmC,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBpC,OAAA;kBACEyC,OAAO,EAAEA,CAAA,KAAM9B,gBAAgB,CAACoB,MAAM,CAAE;kBACxCI,SAAS,EAAC,2CAA2C;kBAAAC,QAAA,EACtD;gBAED;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACTxC,OAAA;kBACEyC,OAAO,EAAEA,CAAA,KAAMP,kBAAkB,CAACH,MAAM,CAAE;kBAC1CI,SAAS,EAAE,WACTJ,MAAM,CAACf,SAAS,GACZ,iCAAiC,GACjC,qCAAqC,EACxC;kBAAAoB,QAAA,EAEFL,MAAM,CAACf,SAAS,GAAG,YAAY,GAAG;gBAAU;kBAAAqB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UACN,GAjDIT,MAAM,CAACE,EAAE;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAkDX,CACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACrC,EAAA,CAjPIF,aAA2C;AAAAwD,EAAA,GAA3CxD,aAA2C;AA0PjD,MAAMqD,cAA6C,GAAGA,CAAC;EAAEvB,MAAM;EAAEwB,MAAM;EAAEC,QAAQ;EAAEX;AAAS,CAAC,KAAK;EAChG,MAAMa,YAAY,GAAI/B,CAAkB,IAAK;IAC3CA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClB2B,MAAM,CAACxB,MAAM,CAAC;EAChB,CAAC;EAED,oBACE/B,OAAA;IAAM0C,QAAQ,EAAEgB,YAAa;IAAAtB,QAAA,gBAC3BpC,OAAA;MAAKmC,SAAS,EAAC,uCAAuC;MAAAC,QAAA,gBACpDpC,OAAA;QAAAoC,QAAA,gBACEpC,OAAA;UAAOmC,SAAS,EAAC,gCAAgC;UAAAC,QAAA,EAAC;QAAM;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eAChExC,OAAA;UACE2C,IAAI,EAAC,MAAM;UACXC,KAAK,EAAEb,MAAM,CAACjB,IAAK;UACnB+B,QAAQ,EAAGlB,CAAC,IAAKkB,QAAQ,CAAC;YAAE,GAAGd,MAAM;YAAEjB,IAAI,EAAEa,CAAC,CAACmB,MAAM,CAACF;UAAM,CAAC,CAAE;UAC/DG,QAAQ;UACRZ,SAAS,EAAC;QAAiC;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5C,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eACNxC,OAAA;QAAAoC,QAAA,gBACEpC,OAAA;UAAOmC,SAAS,EAAC,gCAAgC;UAAAC,QAAA,EAAC;QAAU;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACpExC,OAAA;UACE2C,IAAI,EAAC,QAAQ;UACbC,KAAK,EAAEb,MAAM,CAACd,UAAW;UACzB4B,QAAQ,EAAGlB,CAAC,IAAKkB,QAAQ,CAAC;YAAE,GAAGd,MAAM;YAAEd,UAAU,EAAEgC,QAAQ,CAACtB,CAAC,CAACmB,MAAM,CAACF,KAAK,CAAC,IAAI;UAAE,CAAC,CAAE;UACpFT,SAAS,EAAC;QAAiC;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5C,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eACNxC,OAAA;QAAKmC,SAAS,EAAC,eAAe;QAAAC,QAAA,gBAC5BpC,OAAA;UAAOmC,SAAS,EAAC,gCAAgC;UAAAC,QAAA,EAAC;QAAW;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACrExC,OAAA;UACE4C,KAAK,EAAEb,MAAM,CAAChB,WAAW,IAAI,EAAG;UAChC8B,QAAQ,EAAGlB,CAAC,IAAKkB,QAAQ,CAAC;YAAE,GAAGd,MAAM;YAAEhB,WAAW,EAAEY,CAAC,CAACmB,MAAM,CAACF;UAAM,CAAC,CAAE;UACtET,SAAS,EAAC,iCAAiC;UAC3Ce,IAAI,EAAE;QAAE;UAAAb,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eACNxC,OAAA;QAAKmC,SAAS,EAAC,eAAe;QAAAC,QAAA,eAC5BpC,OAAA;UAAOmC,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAClCpC,OAAA;YACE2C,IAAI,EAAC,UAAU;YACfQ,OAAO,EAAEpB,MAAM,CAACf,SAAU;YAC1B6B,QAAQ,EAAGlB,CAAC,IAAKkB,QAAQ,CAAC;cAAE,GAAGd,MAAM;cAAEf,SAAS,EAAEW,CAAC,CAACmB,MAAM,CAACK;YAAQ,CAAC,CAAE;YACtEhB,SAAS,EAAC;UAAM;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjB,CAAC,gCAEJ;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eACNxC,OAAA;MAAKmC,SAAS,EAAC,MAAM;MAAAC,QAAA,gBACnBpC,OAAA;QACE2C,IAAI,EAAC,QAAQ;QACbR,SAAS,EAAC,mEAAmE;QAAAC,QAAA,EAC9E;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACTxC,OAAA;QACE2C,IAAI,EAAC,QAAQ;QACbF,OAAO,EAAEe,QAAS;QAClBrB,SAAS,EAAC,4DAA4D;QAAAC,QAAA,EACvE;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACF,CAAC;AAEX,CAAC;AAACmB,GAAA,GAlEIL,cAA6C;AAoEnD,eAAerD,aAAa;AAAC,IAAAwD,EAAA,EAAAE,GAAA;AAAAC,YAAA,CAAAH,EAAA;AAAAG,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}