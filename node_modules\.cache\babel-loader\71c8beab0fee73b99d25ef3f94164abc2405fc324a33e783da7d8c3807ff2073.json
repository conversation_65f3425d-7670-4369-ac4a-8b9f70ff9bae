{"ast": null, "code": "import { toDate } from \"./toDate.js\";\n\n/**\n * The {@link setDate} function options.\n */\n\n/**\n * @name setDate\n * @category Day Helpers\n * @summary Set the day of the month to the given date.\n *\n * @description\n * Set the day of the month to the given date.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows using extensions like [`UTCDate`](https://github.com/date-fns/utc).\n * @typeParam ResultDate - The result `Date` type, it is the type returned from the context function if it is passed, or inferred from the arguments.\n *\n * @param date - The date to be changed\n * @param dayOfMonth - The day of the month of the new date\n * @param options - The options\n *\n * @returns The new date with the day of the month set\n *\n * @example\n * // Set the 30th day of the month to 1 September 2014:\n * const result = setDate(new Date(2014, 8, 1), 30)\n * //=> Tue Sep 30 2014 00:00:00\n */\nexport function setDate(date, dayOfMonth, options) {\n  const _date = toDate(date, options?.in);\n  _date.setDate(dayOfMonth);\n  return _date;\n}\n\n// Fallback for modularized imports:\nexport default setDate;", "map": {"version": 3, "names": ["toDate", "setDate", "date", "dayOfMonth", "options", "_date", "in"], "sources": ["C:/Users/<USER>/node_modules/date-fns/setDate.js"], "sourcesContent": ["import { toDate } from \"./toDate.js\";\n\n/**\n * The {@link setDate} function options.\n */\n\n/**\n * @name setDate\n * @category Day Helpers\n * @summary Set the day of the month to the given date.\n *\n * @description\n * Set the day of the month to the given date.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows using extensions like [`UTCDate`](https://github.com/date-fns/utc).\n * @typeParam ResultDate - The result `Date` type, it is the type returned from the context function if it is passed, or inferred from the arguments.\n *\n * @param date - The date to be changed\n * @param dayOfMonth - The day of the month of the new date\n * @param options - The options\n *\n * @returns The new date with the day of the month set\n *\n * @example\n * // Set the 30th day of the month to 1 September 2014:\n * const result = setDate(new Date(2014, 8, 1), 30)\n * //=> Tue Sep 30 2014 00:00:00\n */\nexport function setDate(date, dayOfMonth, options) {\n  const _date = toDate(date, options?.in);\n  _date.setDate(dayOfMonth);\n  return _date;\n}\n\n// Fallback for modularized imports:\nexport default setDate;\n"], "mappings": "AAAA,SAASA,MAAM,QAAQ,aAAa;;AAEpC;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,OAAOA,CAACC,IAAI,EAAEC,UAAU,EAAEC,OAAO,EAAE;EACjD,MAAMC,KAAK,GAAGL,MAAM,CAACE,IAAI,EAAEE,OAAO,EAAEE,EAAE,CAAC;EACvCD,KAAK,CAACJ,OAAO,CAACE,UAAU,CAAC;EACzB,OAAOE,KAAK;AACd;;AAEA;AACA,eAAeJ,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}