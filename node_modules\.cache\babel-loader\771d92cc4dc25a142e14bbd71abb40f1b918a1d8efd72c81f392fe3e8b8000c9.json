{"ast": null, "code": "import { toDate } from \"./toDate.js\";\n\n/**\n * The {@link isWeekend} function options.\n */\n\n/**\n * @name isWeekend\n * @category Weekday Helpers\n * @summary Does the given date fall on a weekend?\n *\n * @description\n * Does the given date fall on a weekend? A weekend is either Saturday (`6`) or Sunday (`0`).\n *\n * @param date - The date to check\n * @param options - An object with options\n *\n * @returns The date falls on a weekend\n *\n * @example\n * // Does 5 October 2014 fall on a weekend?\n * const result = isWeekend(new Date(2014, 9, 5))\n * //=> true\n */\nexport function isWeekend(date, options) {\n  const day = toDate(date, options?.in).getDay();\n  return day === 0 || day === 6;\n}\n\n// Fallback for modularized imports:\nexport default isWeekend;", "map": {"version": 3, "names": ["toDate", "isWeekend", "date", "options", "day", "in", "getDay"], "sources": ["C:/Users/<USER>/node_modules/date-fns/isWeekend.js"], "sourcesContent": ["import { toDate } from \"./toDate.js\";\n\n/**\n * The {@link isWeekend} function options.\n */\n\n/**\n * @name isWeekend\n * @category Weekday Helpers\n * @summary Does the given date fall on a weekend?\n *\n * @description\n * Does the given date fall on a weekend? A weekend is either Saturday (`6`) or Sunday (`0`).\n *\n * @param date - The date to check\n * @param options - An object with options\n *\n * @returns The date falls on a weekend\n *\n * @example\n * // Does 5 October 2014 fall on a weekend?\n * const result = isWeekend(new Date(2014, 9, 5))\n * //=> true\n */\nexport function isWeekend(date, options) {\n  const day = toDate(date, options?.in).getDay();\n  return day === 0 || day === 6;\n}\n\n// Fallback for modularized imports:\nexport default isWeekend;\n"], "mappings": "AAAA,SAASA,MAAM,QAAQ,aAAa;;AAEpC;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,SAASA,CAACC,IAAI,EAAEC,OAAO,EAAE;EACvC,MAAMC,GAAG,GAAGJ,MAAM,CAACE,IAAI,EAAEC,OAAO,EAAEE,EAAE,CAAC,CAACC,MAAM,CAAC,CAAC;EAC9C,OAAOF,GAAG,KAAK,CAAC,IAAIA,GAAG,KAAK,CAAC;AAC/B;;AAEA;AACA,eAAeH,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}