{"ast": null, "code": "import { toDate } from \"./toDate.js\";\n\n/**\n * @name getSeconds\n * @category Second Helpers\n * @summary Get the seconds of the given date.\n *\n * @description\n * Get the seconds of the given date.\n *\n * @param date - The given date\n *\n * @returns The seconds\n *\n * @example\n * // Get the seconds of 29 February 2012 11:45:05.123:\n * const result = getSeconds(new Date(2012, 1, 29, 11, 45, 5, 123))\n * //=> 5\n */\nexport function getSeconds(date) {\n  return toDate(date).getSeconds();\n}\n\n// Fallback for modularized imports:\nexport default getSeconds;", "map": {"version": 3, "names": ["toDate", "getSeconds", "date"], "sources": ["C:/Users/<USER>/node_modules/date-fns/getSeconds.js"], "sourcesContent": ["import { toDate } from \"./toDate.js\";\n\n/**\n * @name getSeconds\n * @category Second Helpers\n * @summary Get the seconds of the given date.\n *\n * @description\n * Get the seconds of the given date.\n *\n * @param date - The given date\n *\n * @returns The seconds\n *\n * @example\n * // Get the seconds of 29 February 2012 11:45:05.123:\n * const result = getSeconds(new Date(2012, 1, 29, 11, 45, 5, 123))\n * //=> 5\n */\nexport function getSeconds(date) {\n  return toDate(date).getSeconds();\n}\n\n// Fallback for modularized imports:\nexport default getSeconds;\n"], "mappings": "AAAA,SAASA,MAAM,QAAQ,aAAa;;AAEpC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,UAAUA,CAACC,IAAI,EAAE;EAC/B,OAAOF,MAAM,CAACE,IAAI,CAAC,CAACD,UAAU,CAAC,CAAC;AAClC;;AAEA;AACA,eAAeA,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}