{"ast": null, "code": "import { previousDay } from \"./previousDay.js\";\n\n/**\n * The {@link previousSaturday} function options.\n */\n\n/**\n * @name previousSaturday\n * @category Weekday Helpers\n * @summary When is the previous Saturday?\n *\n * @description\n * When is the previous Saturday?\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n * @typeParam ResultDate - The result `Date` type, it is the type returned from the context function if it is passed, or inferred from the arguments.\n *\n * @param date - The date to start counting from\n * @param options - The options\n *\n * @returns The previous Saturday\n *\n * @example\n * // When is the previous Saturday before Jun, 20, 2021?\n * const result = previousSaturday(new Date(2021, 5, 20))\n * //=> Sat June 19 2021 00:00:00\n */\nexport function previousSaturday(date, options) {\n  return previousDay(date, 6, options);\n}\n\n// Fallback for modularized imports:\nexport default previousSaturday;", "map": {"version": 3, "names": ["previousDay", "previousSaturday", "date", "options"], "sources": ["C:/Users/<USER>/node_modules/date-fns/previousSaturday.js"], "sourcesContent": ["import { previousDay } from \"./previousDay.js\";\n\n/**\n * The {@link previousSaturday} function options.\n */\n\n/**\n * @name previousSaturday\n * @category Weekday Helpers\n * @summary When is the previous Saturday?\n *\n * @description\n * When is the previous Saturday?\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n * @typeParam ResultDate - The result `Date` type, it is the type returned from the context function if it is passed, or inferred from the arguments.\n *\n * @param date - The date to start counting from\n * @param options - The options\n *\n * @returns The previous Saturday\n *\n * @example\n * // When is the previous Saturday before Jun, 20, 2021?\n * const result = previousSaturday(new Date(2021, 5, 20))\n * //=> Sat June 19 2021 00:00:00\n */\nexport function previousSaturday(date, options) {\n  return previousDay(date, 6, options);\n}\n\n// Fallback for modularized imports:\nexport default previousSaturday;\n"], "mappings": "AAAA,SAASA,WAAW,QAAQ,kBAAkB;;AAE9C;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,gBAAgBA,CAACC,IAAI,EAAEC,OAAO,EAAE;EAC9C,OAAOH,WAAW,CAACE,IAAI,EAAE,CAAC,EAAEC,OAAO,CAAC;AACtC;;AAEA;AACA,eAAeF,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}