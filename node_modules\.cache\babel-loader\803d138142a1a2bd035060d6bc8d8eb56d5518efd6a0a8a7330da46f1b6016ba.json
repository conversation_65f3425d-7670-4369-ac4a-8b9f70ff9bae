{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\lostandfound\\\\discfinder-app\\\\src\\\\components\\\\SourceManager.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { supabaseService } from '../lib/supabase';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst SourceManager = ({\n  onClose\n}) => {\n  _s();\n  const [sources, setSources] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [editingSource, setEditingSource] = useState(null);\n  const [newSource, setNewSource] = useState({\n    name: '',\n    description: '',\n    is_active: true,\n    sort_order: 0\n  });\n  const [showAddForm, setShowAddForm] = useState(false);\n  useEffect(() => {\n    loadSources();\n  }, []);\n  const loadSources = async () => {\n    try {\n      setLoading(true);\n      const data = await supabaseService.getSources();\n      setSources(data);\n    } catch (err) {\n      setError('Failed to load sources');\n      console.error('Error loading sources:', err);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleCreateSource = async e => {\n    e.preventDefault();\n    try {\n      await supabaseService.createSource(newSource);\n      setNewSource({\n        name: '',\n        description: '',\n        is_active: true,\n        sort_order: 0\n      });\n      setShowAddForm(false);\n      await loadSources();\n    } catch (err) {\n      setError('Failed to create source');\n      console.error('Error creating source:', err);\n    }\n  };\n  const handleUpdateSource = async source => {\n    try {\n      await supabaseService.updateSource(source.id, {\n        name: source.name,\n        description: source.description,\n        is_active: source.is_active,\n        sort_order: source.sort_order\n      });\n      setEditingSource(null);\n      await loadSources();\n    } catch (err) {\n      setError('Failed to update source');\n      console.error('Error updating source:', err);\n    }\n  };\n  const handleToggleActive = async source => {\n    try {\n      await supabaseService.updateSource(source.id, {\n        is_active: !source.is_active\n      });\n      await loadSources();\n    } catch (err) {\n      setError('Failed to update source status');\n      console.error('Error updating source status:', err);\n    }\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-lg p-6\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center\",\n          children: \"Loading sources...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 83,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 82,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 81,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white rounded-lg p-6 max-w-4xl w-full max-h-[90vh] overflow-y-auto\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex justify-between items-center mb-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-2xl font-bold\",\n          children: \"Manage Sources\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 93,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: onClose,\n          className: \"text-gray-500 hover:text-gray-700 text-2xl\",\n          children: \"\\xD7\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 94,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 92,\n        columnNumber: 9\n      }, this), error && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4\",\n        children: error\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 103,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mb-6\",\n        children: /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setShowAddForm(!showAddForm),\n          className: \"bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700\",\n          children: showAddForm ? 'Cancel' : 'Add New Source'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 109,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 108,\n        columnNumber: 9\n      }, this), showAddForm && /*#__PURE__*/_jsxDEV(\"form\", {\n        onSubmit: handleCreateSource,\n        className: \"mb-6 p-4 border rounded\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-lg font-semibold mb-4\",\n          children: \"Add New Source\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 119,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-sm font-medium mb-1\",\n              children: \"Name *\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 122,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              value: newSource.name,\n              onChange: e => setNewSource({\n                ...newSource,\n                name: e.target.value\n              }),\n              required: true,\n              className: \"w-full border rounded px-3 py-2\",\n              placeholder: \"e.g., Jones Park, Emporia\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 123,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 121,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-sm font-medium mb-1\",\n              children: \"Sort Order\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 133,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"number\",\n              value: newSource.sort_order,\n              onChange: e => setNewSource({\n                ...newSource,\n                sort_order: parseInt(e.target.value) || 0\n              }),\n              className: \"w-full border rounded px-3 py-2\",\n              placeholder: \"0\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 134,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 132,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"md:col-span-2\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-sm font-medium mb-1\",\n              children: \"Description\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 143,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n              value: newSource.description,\n              onChange: e => setNewSource({\n                ...newSource,\n                description: e.target.value\n              }),\n              className: \"w-full border rounded px-3 py-2\",\n              rows: 2,\n              placeholder: \"Optional description or additional context\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 144,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 142,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"md:col-span-2\",\n            children: /*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"flex items-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"checkbox\",\n                checked: newSource.is_active,\n                onChange: e => setNewSource({\n                  ...newSource,\n                  is_active: e.target.checked\n                }),\n                className: \"mr-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 154,\n                columnNumber: 19\n              }, this), \"Active (visible in dropdown)\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 153,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 152,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 120,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mt-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"submit\",\n            className: \"bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700 mr-2\",\n            children: \"Create Source\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 165,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"button\",\n            onClick: () => setShowAddForm(false),\n            className: \"bg-gray-500 text-white px-4 py-2 rounded hover:bg-gray-600\",\n            children: \"Cancel\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 171,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 164,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 118,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"space-y-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-lg font-semibold\",\n          children: \"Existing Sources\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 183,\n          columnNumber: 11\n        }, this), sources.length === 0 ? /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-500\",\n          children: \"No sources found.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 185,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-2\",\n          children: sources.map(source => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: `border rounded p-4 ${source.is_active ? 'bg-white' : 'bg-gray-100'}`,\n            children: (editingSource === null || editingSource === void 0 ? void 0 : editingSource.id) === source.id ? /*#__PURE__*/_jsxDEV(EditSourceForm, {\n              source: editingSource,\n              onSave: handleUpdateSource,\n              onCancel: () => setEditingSource(null),\n              onChange: setEditingSource\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 194,\n              columnNumber: 21\n            }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex justify-between items-start\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex-1\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center gap-2\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                    className: \"font-medium\",\n                    children: source.name\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 204,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: `px-2 py-1 text-xs rounded ${source.is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`,\n                    children: source.is_active ? 'Active' : 'Inactive'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 205,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-sm text-gray-500\",\n                    children: [\"Order: \", source.sort_order]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 212,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 203,\n                  columnNumber: 25\n                }, this), source.description && /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-sm text-gray-600 mt-1\",\n                  children: source.description\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 217,\n                  columnNumber: 27\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 202,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex gap-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => setEditingSource(source),\n                  className: \"text-blue-600 hover:text-blue-800 text-sm\",\n                  children: \"Edit\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 221,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => handleToggleActive(source),\n                  className: `text-sm ${source.is_active ? 'text-red-600 hover:text-red-800' : 'text-green-600 hover:text-green-800'}`,\n                  children: source.is_active ? 'Deactivate' : 'Activate'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 227,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 220,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 201,\n              columnNumber: 21\n            }, this)\n          }, source.id, false, {\n            fileName: _jsxFileName,\n            lineNumber: 189,\n            columnNumber: 17\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 187,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 182,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 91,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 90,\n    columnNumber: 5\n  }, this);\n};\n_s(SourceManager, \"5pLxTspqoGJMrd5SRhg9J4Vcgoo=\");\n_c = SourceManager;\nconst EditSourceForm = ({\n  source,\n  onSave,\n  onCancel,\n  onChange\n}) => {\n  const handleSubmit = e => {\n    e.preventDefault();\n    onSave(source);\n  };\n  return /*#__PURE__*/_jsxDEV(\"form\", {\n    onSubmit: handleSubmit,\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          className: \"block text-sm font-medium mb-1\",\n          children: \"Name *\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 267,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"text\",\n          value: source.name,\n          onChange: e => onChange({\n            ...source,\n            name: e.target.value\n          }),\n          required: true,\n          className: \"w-full border rounded px-3 py-2\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 268,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 266,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          className: \"block text-sm font-medium mb-1\",\n          children: \"Sort Order\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 277,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"number\",\n          value: source.sort_order,\n          onChange: e => onChange({\n            ...source,\n            sort_order: parseInt(e.target.value) || 0\n          }),\n          className: \"w-full border rounded px-3 py-2\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 278,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 276,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"md:col-span-2\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          className: \"block text-sm font-medium mb-1\",\n          children: \"Description\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 286,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n          value: source.description || '',\n          onChange: e => onChange({\n            ...source,\n            description: e.target.value\n          }),\n          className: \"w-full border rounded px-3 py-2\",\n          rows: 2\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 287,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 285,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"md:col-span-2\",\n        children: /*#__PURE__*/_jsxDEV(\"label\", {\n          className: \"flex items-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"checkbox\",\n            checked: source.is_active,\n            onChange: e => onChange({\n              ...source,\n              is_active: e.target.checked\n            }),\n            className: \"mr-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 296,\n            columnNumber: 13\n          }, this), \"Active (visible in dropdown)\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 295,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 294,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 265,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mt-4\",\n      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n        type: \"submit\",\n        className: \"bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700 mr-2\",\n        children: \"Save Changes\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 307,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        type: \"button\",\n        onClick: onCancel,\n        className: \"bg-gray-500 text-white px-4 py-2 rounded hover:bg-gray-600\",\n        children: \"Cancel\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 313,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 306,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 264,\n    columnNumber: 5\n  }, this);\n};\n_c2 = EditSourceForm;\nexport default SourceManager;\nvar _c, _c2;\n$RefreshReg$(_c, \"SourceManager\");\n$RefreshReg$(_c2, \"EditSourceForm\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "supabaseService", "jsxDEV", "_jsxDEV", "SourceManager", "onClose", "_s", "sources", "setSources", "loading", "setLoading", "error", "setError", "editingSource", "setEditingSource", "newSource", "setNewSource", "name", "description", "is_active", "sort_order", "showAddForm", "setShowAddForm", "loadSources", "data", "getSources", "err", "console", "handleCreateSource", "e", "preventDefault", "createSource", "handleUpdateSource", "source", "updateSource", "id", "handleToggleActive", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "onSubmit", "type", "value", "onChange", "target", "required", "placeholder", "parseInt", "rows", "checked", "length", "map", "EditSourceForm", "onSave", "onCancel", "_c", "handleSubmit", "_c2", "$RefreshReg$"], "sources": ["C:/Users/<USER>/lostandfound/discfinder-app/src/components/SourceManager.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { supabaseService, Source } from '../lib/supabase';\n\ninterface SourceManagerProps {\n  onClose: () => void;\n}\n\nconst SourceManager: React.FC<SourceManagerProps> = ({ onClose }) => {\n  const [sources, setSources] = useState<Source[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n  const [editingSource, setEditingSource] = useState<Source | null>(null);\n  const [newSource, setNewSource] = useState({\n    name: '',\n    description: '',\n    is_active: true,\n    sort_order: 0\n  });\n  const [showAddForm, setShowAddForm] = useState(false);\n\n  useEffect(() => {\n    loadSources();\n  }, []);\n\n  const loadSources = async () => {\n    try {\n      setLoading(true);\n      const data = await supabaseService.getSources();\n      setSources(data);\n    } catch (err) {\n      setError('Failed to load sources');\n      console.error('Error loading sources:', err);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleCreateSource = async (e: React.FormEvent) => {\n    e.preventDefault();\n    try {\n      await supabaseService.createSource(newSource);\n      setNewSource({ name: '', description: '', is_active: true, sort_order: 0 });\n      setShowAddForm(false);\n      await loadSources();\n    } catch (err) {\n      setError('Failed to create source');\n      console.error('Error creating source:', err);\n    }\n  };\n\n  const handleUpdateSource = async (source: Source) => {\n    try {\n      await supabaseService.updateSource(source.id, {\n        name: source.name,\n        description: source.description,\n        is_active: source.is_active,\n        sort_order: source.sort_order\n      });\n      setEditingSource(null);\n      await loadSources();\n    } catch (err) {\n      setError('Failed to update source');\n      console.error('Error updating source:', err);\n    }\n  };\n\n  const handleToggleActive = async (source: Source) => {\n    try {\n      await supabaseService.updateSource(source.id, {\n        is_active: !source.is_active\n      });\n      await loadSources();\n    } catch (err) {\n      setError('Failed to update source status');\n      console.error('Error updating source status:', err);\n    }\n  };\n\n  if (loading) {\n    return (\n      <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\">\n        <div className=\"bg-white rounded-lg p-6\">\n          <div className=\"text-center\">Loading sources...</div>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\">\n      <div className=\"bg-white rounded-lg p-6 max-w-4xl w-full max-h-[90vh] overflow-y-auto\">\n        <div className=\"flex justify-between items-center mb-6\">\n          <h2 className=\"text-2xl font-bold\">Manage Sources</h2>\n          <button\n            onClick={onClose}\n            className=\"text-gray-500 hover:text-gray-700 text-2xl\"\n          >\n            ×\n          </button>\n        </div>\n\n        {error && (\n          <div className=\"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4\">\n            {error}\n          </div>\n        )}\n\n        <div className=\"mb-6\">\n          <button\n            onClick={() => setShowAddForm(!showAddForm)}\n            className=\"bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700\"\n          >\n            {showAddForm ? 'Cancel' : 'Add New Source'}\n          </button>\n        </div>\n\n        {showAddForm && (\n          <form onSubmit={handleCreateSource} className=\"mb-6 p-4 border rounded\">\n            <h3 className=\"text-lg font-semibold mb-4\">Add New Source</h3>\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n              <div>\n                <label className=\"block text-sm font-medium mb-1\">Name *</label>\n                <input\n                  type=\"text\"\n                  value={newSource.name}\n                  onChange={(e) => setNewSource({ ...newSource, name: e.target.value })}\n                  required\n                  className=\"w-full border rounded px-3 py-2\"\n                  placeholder=\"e.g., Jones Park, Emporia\"\n                />\n              </div>\n              <div>\n                <label className=\"block text-sm font-medium mb-1\">Sort Order</label>\n                <input\n                  type=\"number\"\n                  value={newSource.sort_order}\n                  onChange={(e) => setNewSource({ ...newSource, sort_order: parseInt(e.target.value) || 0 })}\n                  className=\"w-full border rounded px-3 py-2\"\n                  placeholder=\"0\"\n                />\n              </div>\n              <div className=\"md:col-span-2\">\n                <label className=\"block text-sm font-medium mb-1\">Description</label>\n                <textarea\n                  value={newSource.description}\n                  onChange={(e) => setNewSource({ ...newSource, description: e.target.value })}\n                  className=\"w-full border rounded px-3 py-2\"\n                  rows={2}\n                  placeholder=\"Optional description or additional context\"\n                />\n              </div>\n              <div className=\"md:col-span-2\">\n                <label className=\"flex items-center\">\n                  <input\n                    type=\"checkbox\"\n                    checked={newSource.is_active}\n                    onChange={(e) => setNewSource({ ...newSource, is_active: e.target.checked })}\n                    className=\"mr-2\"\n                  />\n                  Active (visible in dropdown)\n                </label>\n              </div>\n            </div>\n            <div className=\"mt-4\">\n              <button\n                type=\"submit\"\n                className=\"bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700 mr-2\"\n              >\n                Create Source\n              </button>\n              <button\n                type=\"button\"\n                onClick={() => setShowAddForm(false)}\n                className=\"bg-gray-500 text-white px-4 py-2 rounded hover:bg-gray-600\"\n              >\n                Cancel\n              </button>\n            </div>\n          </form>\n        )}\n\n        <div className=\"space-y-4\">\n          <h3 className=\"text-lg font-semibold\">Existing Sources</h3>\n          {sources.length === 0 ? (\n            <p className=\"text-gray-500\">No sources found.</p>\n          ) : (\n            <div className=\"space-y-2\">\n              {sources.map((source) => (\n                <div\n                  key={source.id}\n                  className={`border rounded p-4 ${source.is_active ? 'bg-white' : 'bg-gray-100'}`}\n                >\n                  {editingSource?.id === source.id ? (\n                    <EditSourceForm\n                      source={editingSource}\n                      onSave={handleUpdateSource}\n                      onCancel={() => setEditingSource(null)}\n                      onChange={setEditingSource}\n                    />\n                  ) : (\n                    <div className=\"flex justify-between items-start\">\n                      <div className=\"flex-1\">\n                        <div className=\"flex items-center gap-2\">\n                          <h4 className=\"font-medium\">{source.name}</h4>\n                          <span className={`px-2 py-1 text-xs rounded ${\n                            source.is_active \n                              ? 'bg-green-100 text-green-800' \n                              : 'bg-red-100 text-red-800'\n                          }`}>\n                            {source.is_active ? 'Active' : 'Inactive'}\n                          </span>\n                          <span className=\"text-sm text-gray-500\">\n                            Order: {source.sort_order}\n                          </span>\n                        </div>\n                        {source.description && (\n                          <p className=\"text-sm text-gray-600 mt-1\">{source.description}</p>\n                        )}\n                      </div>\n                      <div className=\"flex gap-2\">\n                        <button\n                          onClick={() => setEditingSource(source)}\n                          className=\"text-blue-600 hover:text-blue-800 text-sm\"\n                        >\n                          Edit\n                        </button>\n                        <button\n                          onClick={() => handleToggleActive(source)}\n                          className={`text-sm ${\n                            source.is_active \n                              ? 'text-red-600 hover:text-red-800' \n                              : 'text-green-600 hover:text-green-800'\n                          }`}\n                        >\n                          {source.is_active ? 'Deactivate' : 'Activate'}\n                        </button>\n                      </div>\n                    </div>\n                  )}\n                </div>\n              ))}\n            </div>\n          )}\n        </div>\n      </div>\n    </div>\n  );\n};\n\ninterface EditSourceFormProps {\n  source: Source;\n  onSave: (source: Source) => void;\n  onCancel: () => void;\n  onChange: (source: Source) => void;\n}\n\nconst EditSourceForm: React.FC<EditSourceFormProps> = ({ source, onSave, onCancel, onChange }) => {\n  const handleSubmit = (e: React.FormEvent) => {\n    e.preventDefault();\n    onSave(source);\n  };\n\n  return (\n    <form onSubmit={handleSubmit}>\n      <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n        <div>\n          <label className=\"block text-sm font-medium mb-1\">Name *</label>\n          <input\n            type=\"text\"\n            value={source.name}\n            onChange={(e) => onChange({ ...source, name: e.target.value })}\n            required\n            className=\"w-full border rounded px-3 py-2\"\n          />\n        </div>\n        <div>\n          <label className=\"block text-sm font-medium mb-1\">Sort Order</label>\n          <input\n            type=\"number\"\n            value={source.sort_order}\n            onChange={(e) => onChange({ ...source, sort_order: parseInt(e.target.value) || 0 })}\n            className=\"w-full border rounded px-3 py-2\"\n          />\n        </div>\n        <div className=\"md:col-span-2\">\n          <label className=\"block text-sm font-medium mb-1\">Description</label>\n          <textarea\n            value={source.description || ''}\n            onChange={(e) => onChange({ ...source, description: e.target.value })}\n            className=\"w-full border rounded px-3 py-2\"\n            rows={2}\n          />\n        </div>\n        <div className=\"md:col-span-2\">\n          <label className=\"flex items-center\">\n            <input\n              type=\"checkbox\"\n              checked={source.is_active}\n              onChange={(e) => onChange({ ...source, is_active: e.target.checked })}\n              className=\"mr-2\"\n            />\n            Active (visible in dropdown)\n          </label>\n        </div>\n      </div>\n      <div className=\"mt-4\">\n        <button\n          type=\"submit\"\n          className=\"bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700 mr-2\"\n        >\n          Save Changes\n        </button>\n        <button\n          type=\"button\"\n          onClick={onCancel}\n          className=\"bg-gray-500 text-white px-4 py-2 rounded hover:bg-gray-600\"\n        >\n          Cancel\n        </button>\n      </div>\n    </form>\n  );\n};\n\nexport default SourceManager;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,eAAe,QAAgB,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAM1D,MAAMC,aAA2C,GAAGA,CAAC;EAAEC;AAAQ,CAAC,KAAK;EAAAC,EAAA;EACnE,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGT,QAAQ,CAAW,EAAE,CAAC;EACpD,MAAM,CAACU,OAAO,EAAEC,UAAU,CAAC,GAAGX,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACY,KAAK,EAAEC,QAAQ,CAAC,GAAGb,QAAQ,CAAgB,IAAI,CAAC;EACvD,MAAM,CAACc,aAAa,EAAEC,gBAAgB,CAAC,GAAGf,QAAQ,CAAgB,IAAI,CAAC;EACvE,MAAM,CAACgB,SAAS,EAAEC,YAAY,CAAC,GAAGjB,QAAQ,CAAC;IACzCkB,IAAI,EAAE,EAAE;IACRC,WAAW,EAAE,EAAE;IACfC,SAAS,EAAE,IAAI;IACfC,UAAU,EAAE;EACd,CAAC,CAAC;EACF,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGvB,QAAQ,CAAC,KAAK,CAAC;EAErDC,SAAS,CAAC,MAAM;IACduB,WAAW,CAAC,CAAC;EACf,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMA,WAAW,GAAG,MAAAA,CAAA,KAAY;IAC9B,IAAI;MACFb,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMc,IAAI,GAAG,MAAMvB,eAAe,CAACwB,UAAU,CAAC,CAAC;MAC/CjB,UAAU,CAACgB,IAAI,CAAC;IAClB,CAAC,CAAC,OAAOE,GAAG,EAAE;MACZd,QAAQ,CAAC,wBAAwB,CAAC;MAClCe,OAAO,CAAChB,KAAK,CAAC,wBAAwB,EAAEe,GAAG,CAAC;IAC9C,CAAC,SAAS;MACRhB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMkB,kBAAkB,GAAG,MAAOC,CAAkB,IAAK;IACvDA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClB,IAAI;MACF,MAAM7B,eAAe,CAAC8B,YAAY,CAAChB,SAAS,CAAC;MAC7CC,YAAY,CAAC;QAAEC,IAAI,EAAE,EAAE;QAAEC,WAAW,EAAE,EAAE;QAAEC,SAAS,EAAE,IAAI;QAAEC,UAAU,EAAE;MAAE,CAAC,CAAC;MAC3EE,cAAc,CAAC,KAAK,CAAC;MACrB,MAAMC,WAAW,CAAC,CAAC;IACrB,CAAC,CAAC,OAAOG,GAAG,EAAE;MACZd,QAAQ,CAAC,yBAAyB,CAAC;MACnCe,OAAO,CAAChB,KAAK,CAAC,wBAAwB,EAAEe,GAAG,CAAC;IAC9C;EACF,CAAC;EAED,MAAMM,kBAAkB,GAAG,MAAOC,MAAc,IAAK;IACnD,IAAI;MACF,MAAMhC,eAAe,CAACiC,YAAY,CAACD,MAAM,CAACE,EAAE,EAAE;QAC5ClB,IAAI,EAAEgB,MAAM,CAAChB,IAAI;QACjBC,WAAW,EAAEe,MAAM,CAACf,WAAW;QAC/BC,SAAS,EAAEc,MAAM,CAACd,SAAS;QAC3BC,UAAU,EAAEa,MAAM,CAACb;MACrB,CAAC,CAAC;MACFN,gBAAgB,CAAC,IAAI,CAAC;MACtB,MAAMS,WAAW,CAAC,CAAC;IACrB,CAAC,CAAC,OAAOG,GAAG,EAAE;MACZd,QAAQ,CAAC,yBAAyB,CAAC;MACnCe,OAAO,CAAChB,KAAK,CAAC,wBAAwB,EAAEe,GAAG,CAAC;IAC9C;EACF,CAAC;EAED,MAAMU,kBAAkB,GAAG,MAAOH,MAAc,IAAK;IACnD,IAAI;MACF,MAAMhC,eAAe,CAACiC,YAAY,CAACD,MAAM,CAACE,EAAE,EAAE;QAC5ChB,SAAS,EAAE,CAACc,MAAM,CAACd;MACrB,CAAC,CAAC;MACF,MAAMI,WAAW,CAAC,CAAC;IACrB,CAAC,CAAC,OAAOG,GAAG,EAAE;MACZd,QAAQ,CAAC,gCAAgC,CAAC;MAC1Ce,OAAO,CAAChB,KAAK,CAAC,+BAA+B,EAAEe,GAAG,CAAC;IACrD;EACF,CAAC;EAED,IAAIjB,OAAO,EAAE;IACX,oBACEN,OAAA;MAAKkC,SAAS,EAAC,4EAA4E;MAAAC,QAAA,eACzFnC,OAAA;QAAKkC,SAAS,EAAC,yBAAyB;QAAAC,QAAA,eACtCnC,OAAA;UAAKkC,SAAS,EAAC,aAAa;UAAAC,QAAA,EAAC;QAAkB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,oBACEvC,OAAA;IAAKkC,SAAS,EAAC,4EAA4E;IAAAC,QAAA,eACzFnC,OAAA;MAAKkC,SAAS,EAAC,uEAAuE;MAAAC,QAAA,gBACpFnC,OAAA;QAAKkC,SAAS,EAAC,wCAAwC;QAAAC,QAAA,gBACrDnC,OAAA;UAAIkC,SAAS,EAAC,oBAAoB;UAAAC,QAAA,EAAC;QAAc;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACtDvC,OAAA;UACEwC,OAAO,EAAEtC,OAAQ;UACjBgC,SAAS,EAAC,4CAA4C;UAAAC,QAAA,EACvD;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,EAEL/B,KAAK,iBACJR,OAAA;QAAKkC,SAAS,EAAC,sEAAsE;QAAAC,QAAA,EAClF3B;MAAK;QAAA4B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,eAEDvC,OAAA;QAAKkC,SAAS,EAAC,MAAM;QAAAC,QAAA,eACnBnC,OAAA;UACEwC,OAAO,EAAEA,CAAA,KAAMrB,cAAc,CAAC,CAACD,WAAW,CAAE;UAC5CgB,SAAS,EAAC,4DAA4D;UAAAC,QAAA,EAErEjB,WAAW,GAAG,QAAQ,GAAG;QAAgB;UAAAkB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,EAELrB,WAAW,iBACVlB,OAAA;QAAMyC,QAAQ,EAAEhB,kBAAmB;QAACS,SAAS,EAAC,yBAAyB;QAAAC,QAAA,gBACrEnC,OAAA;UAAIkC,SAAS,EAAC,4BAA4B;UAAAC,QAAA,EAAC;QAAc;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC9DvC,OAAA;UAAKkC,SAAS,EAAC,uCAAuC;UAAAC,QAAA,gBACpDnC,OAAA;YAAAmC,QAAA,gBACEnC,OAAA;cAAOkC,SAAS,EAAC,gCAAgC;cAAAC,QAAA,EAAC;YAAM;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAChEvC,OAAA;cACE0C,IAAI,EAAC,MAAM;cACXC,KAAK,EAAE/B,SAAS,CAACE,IAAK;cACtB8B,QAAQ,EAAGlB,CAAC,IAAKb,YAAY,CAAC;gBAAE,GAAGD,SAAS;gBAAEE,IAAI,EAAEY,CAAC,CAACmB,MAAM,CAACF;cAAM,CAAC,CAAE;cACtEG,QAAQ;cACRZ,SAAS,EAAC,iCAAiC;cAC3Ca,WAAW,EAAC;YAA2B;cAAAX,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eACNvC,OAAA;YAAAmC,QAAA,gBACEnC,OAAA;cAAOkC,SAAS,EAAC,gCAAgC;cAAAC,QAAA,EAAC;YAAU;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACpEvC,OAAA;cACE0C,IAAI,EAAC,QAAQ;cACbC,KAAK,EAAE/B,SAAS,CAACK,UAAW;cAC5B2B,QAAQ,EAAGlB,CAAC,IAAKb,YAAY,CAAC;gBAAE,GAAGD,SAAS;gBAAEK,UAAU,EAAE+B,QAAQ,CAACtB,CAAC,CAACmB,MAAM,CAACF,KAAK,CAAC,IAAI;cAAE,CAAC,CAAE;cAC3FT,SAAS,EAAC,iCAAiC;cAC3Ca,WAAW,EAAC;YAAG;cAAAX,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eACNvC,OAAA;YAAKkC,SAAS,EAAC,eAAe;YAAAC,QAAA,gBAC5BnC,OAAA;cAAOkC,SAAS,EAAC,gCAAgC;cAAAC,QAAA,EAAC;YAAW;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACrEvC,OAAA;cACE2C,KAAK,EAAE/B,SAAS,CAACG,WAAY;cAC7B6B,QAAQ,EAAGlB,CAAC,IAAKb,YAAY,CAAC;gBAAE,GAAGD,SAAS;gBAAEG,WAAW,EAAEW,CAAC,CAACmB,MAAM,CAACF;cAAM,CAAC,CAAE;cAC7ET,SAAS,EAAC,iCAAiC;cAC3Ce,IAAI,EAAE,CAAE;cACRF,WAAW,EAAC;YAA4C;cAAAX,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eACNvC,OAAA;YAAKkC,SAAS,EAAC,eAAe;YAAAC,QAAA,eAC5BnC,OAAA;cAAOkC,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBAClCnC,OAAA;gBACE0C,IAAI,EAAC,UAAU;gBACfQ,OAAO,EAAEtC,SAAS,CAACI,SAAU;gBAC7B4B,QAAQ,EAAGlB,CAAC,IAAKb,YAAY,CAAC;kBAAE,GAAGD,SAAS;kBAAEI,SAAS,EAAEU,CAAC,CAACmB,MAAM,CAACK;gBAAQ,CAAC,CAAE;gBAC7EhB,SAAS,EAAC;cAAM;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjB,CAAC,gCAEJ;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACNvC,OAAA;UAAKkC,SAAS,EAAC,MAAM;UAAAC,QAAA,gBACnBnC,OAAA;YACE0C,IAAI,EAAC,QAAQ;YACbR,SAAS,EAAC,mEAAmE;YAAAC,QAAA,EAC9E;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTvC,OAAA;YACE0C,IAAI,EAAC,QAAQ;YACbF,OAAO,EAAEA,CAAA,KAAMrB,cAAc,CAAC,KAAK,CAAE;YACrCe,SAAS,EAAC,4DAA4D;YAAAC,QAAA,EACvE;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CACP,eAEDvC,OAAA;QAAKkC,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACxBnC,OAAA;UAAIkC,SAAS,EAAC,uBAAuB;UAAAC,QAAA,EAAC;QAAgB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,EAC1DnC,OAAO,CAAC+C,MAAM,KAAK,CAAC,gBACnBnD,OAAA;UAAGkC,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAAiB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,gBAElDvC,OAAA;UAAKkC,SAAS,EAAC,WAAW;UAAAC,QAAA,EACvB/B,OAAO,CAACgD,GAAG,CAAEtB,MAAM,iBAClB9B,OAAA;YAEEkC,SAAS,EAAE,sBAAsBJ,MAAM,CAACd,SAAS,GAAG,UAAU,GAAG,aAAa,EAAG;YAAAmB,QAAA,EAEhF,CAAAzB,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAEsB,EAAE,MAAKF,MAAM,CAACE,EAAE,gBAC9BhC,OAAA,CAACqD,cAAc;cACbvB,MAAM,EAAEpB,aAAc;cACtB4C,MAAM,EAAEzB,kBAAmB;cAC3B0B,QAAQ,EAAEA,CAAA,KAAM5C,gBAAgB,CAAC,IAAI,CAAE;cACvCiC,QAAQ,EAAEjC;YAAiB;cAAAyB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5B,CAAC,gBAEFvC,OAAA;cAAKkC,SAAS,EAAC,kCAAkC;cAAAC,QAAA,gBAC/CnC,OAAA;gBAAKkC,SAAS,EAAC,QAAQ;gBAAAC,QAAA,gBACrBnC,OAAA;kBAAKkC,SAAS,EAAC,yBAAyB;kBAAAC,QAAA,gBACtCnC,OAAA;oBAAIkC,SAAS,EAAC,aAAa;oBAAAC,QAAA,EAAEL,MAAM,CAAChB;kBAAI;oBAAAsB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eAC9CvC,OAAA;oBAAMkC,SAAS,EAAE,6BACfJ,MAAM,CAACd,SAAS,GACZ,6BAA6B,GAC7B,yBAAyB,EAC5B;oBAAAmB,QAAA,EACAL,MAAM,CAACd,SAAS,GAAG,QAAQ,GAAG;kBAAU;oBAAAoB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACrC,CAAC,eACPvC,OAAA;oBAAMkC,SAAS,EAAC,uBAAuB;oBAAAC,QAAA,GAAC,SAC/B,EAACL,MAAM,CAACb,UAAU;kBAAA;oBAAAmB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACrB,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC,EACLT,MAAM,CAACf,WAAW,iBACjBf,OAAA;kBAAGkC,SAAS,EAAC,4BAA4B;kBAAAC,QAAA,EAAEL,MAAM,CAACf;gBAAW;kBAAAqB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAClE;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eACNvC,OAAA;gBAAKkC,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBnC,OAAA;kBACEwC,OAAO,EAAEA,CAAA,KAAM7B,gBAAgB,CAACmB,MAAM,CAAE;kBACxCI,SAAS,EAAC,2CAA2C;kBAAAC,QAAA,EACtD;gBAED;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACTvC,OAAA;kBACEwC,OAAO,EAAEA,CAAA,KAAMP,kBAAkB,CAACH,MAAM,CAAE;kBAC1CI,SAAS,EAAE,WACTJ,MAAM,CAACd,SAAS,GACZ,iCAAiC,GACjC,qCAAqC,EACxC;kBAAAmB,QAAA,EAEFL,MAAM,CAACd,SAAS,GAAG,YAAY,GAAG;gBAAU;kBAAAoB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UACN,GAjDIT,MAAM,CAACE,EAAE;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAkDX,CACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACpC,EAAA,CAhPIF,aAA2C;AAAAuD,EAAA,GAA3CvD,aAA2C;AAyPjD,MAAMoD,cAA6C,GAAGA,CAAC;EAAEvB,MAAM;EAAEwB,MAAM;EAAEC,QAAQ;EAAEX;AAAS,CAAC,KAAK;EAChG,MAAMa,YAAY,GAAI/B,CAAkB,IAAK;IAC3CA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClB2B,MAAM,CAACxB,MAAM,CAAC;EAChB,CAAC;EAED,oBACE9B,OAAA;IAAMyC,QAAQ,EAAEgB,YAAa;IAAAtB,QAAA,gBAC3BnC,OAAA;MAAKkC,SAAS,EAAC,uCAAuC;MAAAC,QAAA,gBACpDnC,OAAA;QAAAmC,QAAA,gBACEnC,OAAA;UAAOkC,SAAS,EAAC,gCAAgC;UAAAC,QAAA,EAAC;QAAM;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eAChEvC,OAAA;UACE0C,IAAI,EAAC,MAAM;UACXC,KAAK,EAAEb,MAAM,CAAChB,IAAK;UACnB8B,QAAQ,EAAGlB,CAAC,IAAKkB,QAAQ,CAAC;YAAE,GAAGd,MAAM;YAAEhB,IAAI,EAAEY,CAAC,CAACmB,MAAM,CAACF;UAAM,CAAC,CAAE;UAC/DG,QAAQ;UACRZ,SAAS,EAAC;QAAiC;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5C,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eACNvC,OAAA;QAAAmC,QAAA,gBACEnC,OAAA;UAAOkC,SAAS,EAAC,gCAAgC;UAAAC,QAAA,EAAC;QAAU;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACpEvC,OAAA;UACE0C,IAAI,EAAC,QAAQ;UACbC,KAAK,EAAEb,MAAM,CAACb,UAAW;UACzB2B,QAAQ,EAAGlB,CAAC,IAAKkB,QAAQ,CAAC;YAAE,GAAGd,MAAM;YAAEb,UAAU,EAAE+B,QAAQ,CAACtB,CAAC,CAACmB,MAAM,CAACF,KAAK,CAAC,IAAI;UAAE,CAAC,CAAE;UACpFT,SAAS,EAAC;QAAiC;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5C,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eACNvC,OAAA;QAAKkC,SAAS,EAAC,eAAe;QAAAC,QAAA,gBAC5BnC,OAAA;UAAOkC,SAAS,EAAC,gCAAgC;UAAAC,QAAA,EAAC;QAAW;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACrEvC,OAAA;UACE2C,KAAK,EAAEb,MAAM,CAACf,WAAW,IAAI,EAAG;UAChC6B,QAAQ,EAAGlB,CAAC,IAAKkB,QAAQ,CAAC;YAAE,GAAGd,MAAM;YAAEf,WAAW,EAAEW,CAAC,CAACmB,MAAM,CAACF;UAAM,CAAC,CAAE;UACtET,SAAS,EAAC,iCAAiC;UAC3Ce,IAAI,EAAE;QAAE;UAAAb,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eACNvC,OAAA;QAAKkC,SAAS,EAAC,eAAe;QAAAC,QAAA,eAC5BnC,OAAA;UAAOkC,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAClCnC,OAAA;YACE0C,IAAI,EAAC,UAAU;YACfQ,OAAO,EAAEpB,MAAM,CAACd,SAAU;YAC1B4B,QAAQ,EAAGlB,CAAC,IAAKkB,QAAQ,CAAC;cAAE,GAAGd,MAAM;cAAEd,SAAS,EAAEU,CAAC,CAACmB,MAAM,CAACK;YAAQ,CAAC,CAAE;YACtEhB,SAAS,EAAC;UAAM;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjB,CAAC,gCAEJ;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eACNvC,OAAA;MAAKkC,SAAS,EAAC,MAAM;MAAAC,QAAA,gBACnBnC,OAAA;QACE0C,IAAI,EAAC,QAAQ;QACbR,SAAS,EAAC,mEAAmE;QAAAC,QAAA,EAC9E;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACTvC,OAAA;QACE0C,IAAI,EAAC,QAAQ;QACbF,OAAO,EAAEe,QAAS;QAClBrB,SAAS,EAAC,4DAA4D;QAAAC,QAAA,EACvE;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACF,CAAC;AAEX,CAAC;AAACmB,GAAA,GAlEIL,cAA6C;AAoEnD,eAAepD,aAAa;AAAC,IAAAuD,EAAA,EAAAE,GAAA;AAAAC,YAAA,CAAAH,EAAA;AAAAG,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}