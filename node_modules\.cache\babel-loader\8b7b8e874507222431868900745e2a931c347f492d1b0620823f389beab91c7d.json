{"ast": null, "code": "var __awaiter = this && this.__awaiter || function (thisArg, _arguments, P, generator) {\n  function adopt(value) {\n    return value instanceof P ? value : new P(function (resolve) {\n      resolve(value);\n    });\n  }\n  return new (P || (P = Promise))(function (resolve, reject) {\n    function fulfilled(value) {\n      try {\n        step(generator.next(value));\n      } catch (e) {\n        reject(e);\n      }\n    }\n    function rejected(value) {\n      try {\n        step(generator[\"throw\"](value));\n      } catch (e) {\n        reject(e);\n      }\n    }\n    function step(result) {\n      result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected);\n    }\n    step((generator = generator.apply(thisArg, _arguments || [])).next());\n  });\n};\nimport { isStorageError, StorageError, StorageUnknownError } from '../lib/errors';\nimport { get, head, post, remove } from '../lib/fetch';\nimport { recursiveToCamel, resolveFetch } from '../lib/helpers';\nconst DEFAULT_SEARCH_OPTIONS = {\n  limit: 100,\n  offset: 0,\n  sortBy: {\n    column: 'name',\n    order: 'asc'\n  }\n};\nconst DEFAULT_FILE_OPTIONS = {\n  cacheControl: '3600',\n  contentType: 'text/plain;charset=UTF-8',\n  upsert: false\n};\nexport default class StorageFileApi {\n  constructor(url, headers = {}, bucketId, fetch) {\n    this.url = url;\n    this.headers = headers;\n    this.bucketId = bucketId;\n    this.fetch = resolveFetch(fetch);\n  }\n  /**\n   * Uploads a file to an existing bucket or replaces an existing file at the specified path with a new one.\n   *\n   * @param method HTTP method.\n   * @param path The relative file path. Should be of the format `folder/subfolder/filename.png`. The bucket must already exist before attempting to upload.\n   * @param fileBody The body of the file to be stored in the bucket.\n   */\n  uploadOrUpdate(method, path, fileBody, fileOptions) {\n    return __awaiter(this, void 0, void 0, function* () {\n      try {\n        let body;\n        const options = Object.assign(Object.assign({}, DEFAULT_FILE_OPTIONS), fileOptions);\n        let headers = Object.assign(Object.assign({}, this.headers), method === 'POST' && {\n          'x-upsert': String(options.upsert)\n        });\n        const metadata = options.metadata;\n        if (typeof Blob !== 'undefined' && fileBody instanceof Blob) {\n          body = new FormData();\n          body.append('cacheControl', options.cacheControl);\n          if (metadata) {\n            body.append('metadata', this.encodeMetadata(metadata));\n          }\n          body.append('', fileBody);\n        } else if (typeof FormData !== 'undefined' && fileBody instanceof FormData) {\n          body = fileBody;\n          body.append('cacheControl', options.cacheControl);\n          if (metadata) {\n            body.append('metadata', this.encodeMetadata(metadata));\n          }\n        } else {\n          body = fileBody;\n          headers['cache-control'] = `max-age=${options.cacheControl}`;\n          headers['content-type'] = options.contentType;\n          if (metadata) {\n            headers['x-metadata'] = this.toBase64(this.encodeMetadata(metadata));\n          }\n        }\n        if (fileOptions === null || fileOptions === void 0 ? void 0 : fileOptions.headers) {\n          headers = Object.assign(Object.assign({}, headers), fileOptions.headers);\n        }\n        const cleanPath = this._removeEmptyFolders(path);\n        const _path = this._getFinalPath(cleanPath);\n        const res = yield this.fetch(`${this.url}/object/${_path}`, Object.assign({\n          method,\n          body: body,\n          headers\n        }, (options === null || options === void 0 ? void 0 : options.duplex) ? {\n          duplex: options.duplex\n        } : {}));\n        const data = yield res.json();\n        if (res.ok) {\n          return {\n            data: {\n              path: cleanPath,\n              id: data.Id,\n              fullPath: data.Key\n            },\n            error: null\n          };\n        } else {\n          const error = data;\n          return {\n            data: null,\n            error\n          };\n        }\n      } catch (error) {\n        if (isStorageError(error)) {\n          return {\n            data: null,\n            error\n          };\n        }\n        throw error;\n      }\n    });\n  }\n  /**\n   * Uploads a file to an existing bucket.\n   *\n   * @param path The file path, including the file name. Should be of the format `folder/subfolder/filename.png`. The bucket must already exist before attempting to upload.\n   * @param fileBody The body of the file to be stored in the bucket.\n   */\n  upload(path, fileBody, fileOptions) {\n    return __awaiter(this, void 0, void 0, function* () {\n      return this.uploadOrUpdate('POST', path, fileBody, fileOptions);\n    });\n  }\n  /**\n   * Upload a file with a token generated from `createSignedUploadUrl`.\n   * @param path The file path, including the file name. Should be of the format `folder/subfolder/filename.png`. The bucket must already exist before attempting to upload.\n   * @param token The token generated from `createSignedUploadUrl`\n   * @param fileBody The body of the file to be stored in the bucket.\n   */\n  uploadToSignedUrl(path, token, fileBody, fileOptions) {\n    return __awaiter(this, void 0, void 0, function* () {\n      const cleanPath = this._removeEmptyFolders(path);\n      const _path = this._getFinalPath(cleanPath);\n      const url = new URL(this.url + `/object/upload/sign/${_path}`);\n      url.searchParams.set('token', token);\n      try {\n        let body;\n        const options = Object.assign({\n          upsert: DEFAULT_FILE_OPTIONS.upsert\n        }, fileOptions);\n        const headers = Object.assign(Object.assign({}, this.headers), {\n          'x-upsert': String(options.upsert)\n        });\n        if (typeof Blob !== 'undefined' && fileBody instanceof Blob) {\n          body = new FormData();\n          body.append('cacheControl', options.cacheControl);\n          body.append('', fileBody);\n        } else if (typeof FormData !== 'undefined' && fileBody instanceof FormData) {\n          body = fileBody;\n          body.append('cacheControl', options.cacheControl);\n        } else {\n          body = fileBody;\n          headers['cache-control'] = `max-age=${options.cacheControl}`;\n          headers['content-type'] = options.contentType;\n        }\n        const res = yield this.fetch(url.toString(), {\n          method: 'PUT',\n          body: body,\n          headers\n        });\n        const data = yield res.json();\n        if (res.ok) {\n          return {\n            data: {\n              path: cleanPath,\n              fullPath: data.Key\n            },\n            error: null\n          };\n        } else {\n          const error = data;\n          return {\n            data: null,\n            error\n          };\n        }\n      } catch (error) {\n        if (isStorageError(error)) {\n          return {\n            data: null,\n            error\n          };\n        }\n        throw error;\n      }\n    });\n  }\n  /**\n   * Creates a signed upload URL.\n   * Signed upload URLs can be used to upload files to the bucket without further authentication.\n   * They are valid for 2 hours.\n   * @param path The file path, including the current file name. For example `folder/image.png`.\n   * @param options.upsert If set to true, allows the file to be overwritten if it already exists.\n   */\n  createSignedUploadUrl(path, options) {\n    return __awaiter(this, void 0, void 0, function* () {\n      try {\n        let _path = this._getFinalPath(path);\n        const headers = Object.assign({}, this.headers);\n        if (options === null || options === void 0 ? void 0 : options.upsert) {\n          headers['x-upsert'] = 'true';\n        }\n        const data = yield post(this.fetch, `${this.url}/object/upload/sign/${_path}`, {}, {\n          headers\n        });\n        const url = new URL(this.url + data.url);\n        const token = url.searchParams.get('token');\n        if (!token) {\n          throw new StorageError('No token returned by API');\n        }\n        return {\n          data: {\n            signedUrl: url.toString(),\n            path,\n            token\n          },\n          error: null\n        };\n      } catch (error) {\n        if (isStorageError(error)) {\n          return {\n            data: null,\n            error\n          };\n        }\n        throw error;\n      }\n    });\n  }\n  /**\n   * Replaces an existing file at the specified path with a new one.\n   *\n   * @param path The relative file path. Should be of the format `folder/subfolder/filename.png`. The bucket must already exist before attempting to update.\n   * @param fileBody The body of the file to be stored in the bucket.\n   */\n  update(path, fileBody, fileOptions) {\n    return __awaiter(this, void 0, void 0, function* () {\n      return this.uploadOrUpdate('PUT', path, fileBody, fileOptions);\n    });\n  }\n  /**\n   * Moves an existing file to a new path in the same bucket.\n   *\n   * @param fromPath The original file path, including the current file name. For example `folder/image.png`.\n   * @param toPath The new file path, including the new file name. For example `folder/image-new.png`.\n   * @param options The destination options.\n   */\n  move(fromPath, toPath, options) {\n    return __awaiter(this, void 0, void 0, function* () {\n      try {\n        const data = yield post(this.fetch, `${this.url}/object/move`, {\n          bucketId: this.bucketId,\n          sourceKey: fromPath,\n          destinationKey: toPath,\n          destinationBucket: options === null || options === void 0 ? void 0 : options.destinationBucket\n        }, {\n          headers: this.headers\n        });\n        return {\n          data,\n          error: null\n        };\n      } catch (error) {\n        if (isStorageError(error)) {\n          return {\n            data: null,\n            error\n          };\n        }\n        throw error;\n      }\n    });\n  }\n  /**\n   * Copies an existing file to a new path in the same bucket.\n   *\n   * @param fromPath The original file path, including the current file name. For example `folder/image.png`.\n   * @param toPath The new file path, including the new file name. For example `folder/image-copy.png`.\n   * @param options The destination options.\n   */\n  copy(fromPath, toPath, options) {\n    return __awaiter(this, void 0, void 0, function* () {\n      try {\n        const data = yield post(this.fetch, `${this.url}/object/copy`, {\n          bucketId: this.bucketId,\n          sourceKey: fromPath,\n          destinationKey: toPath,\n          destinationBucket: options === null || options === void 0 ? void 0 : options.destinationBucket\n        }, {\n          headers: this.headers\n        });\n        return {\n          data: {\n            path: data.Key\n          },\n          error: null\n        };\n      } catch (error) {\n        if (isStorageError(error)) {\n          return {\n            data: null,\n            error\n          };\n        }\n        throw error;\n      }\n    });\n  }\n  /**\n   * Creates a signed URL. Use a signed URL to share a file for a fixed amount of time.\n   *\n   * @param path The file path, including the current file name. For example `folder/image.png`.\n   * @param expiresIn The number of seconds until the signed URL expires. For example, `60` for a URL which is valid for one minute.\n   * @param options.download triggers the file as a download if set to true. Set this parameter as the name of the file if you want to trigger the download with a different filename.\n   * @param options.transform Transform the asset before serving it to the client.\n   */\n  createSignedUrl(path, expiresIn, options) {\n    return __awaiter(this, void 0, void 0, function* () {\n      try {\n        let _path = this._getFinalPath(path);\n        let data = yield post(this.fetch, `${this.url}/object/sign/${_path}`, Object.assign({\n          expiresIn\n        }, (options === null || options === void 0 ? void 0 : options.transform) ? {\n          transform: options.transform\n        } : {}), {\n          headers: this.headers\n        });\n        const downloadQueryParam = (options === null || options === void 0 ? void 0 : options.download) ? `&download=${options.download === true ? '' : options.download}` : '';\n        const signedUrl = encodeURI(`${this.url}${data.signedURL}${downloadQueryParam}`);\n        data = {\n          signedUrl\n        };\n        return {\n          data,\n          error: null\n        };\n      } catch (error) {\n        if (isStorageError(error)) {\n          return {\n            data: null,\n            error\n          };\n        }\n        throw error;\n      }\n    });\n  }\n  /**\n   * Creates multiple signed URLs. Use a signed URL to share a file for a fixed amount of time.\n   *\n   * @param paths The file paths to be downloaded, including the current file names. For example `['folder/image.png', 'folder2/image2.png']`.\n   * @param expiresIn The number of seconds until the signed URLs expire. For example, `60` for URLs which are valid for one minute.\n   * @param options.download triggers the file as a download if set to true. Set this parameter as the name of the file if you want to trigger the download with a different filename.\n   */\n  createSignedUrls(paths, expiresIn, options) {\n    return __awaiter(this, void 0, void 0, function* () {\n      try {\n        const data = yield post(this.fetch, `${this.url}/object/sign/${this.bucketId}`, {\n          expiresIn,\n          paths\n        }, {\n          headers: this.headers\n        });\n        const downloadQueryParam = (options === null || options === void 0 ? void 0 : options.download) ? `&download=${options.download === true ? '' : options.download}` : '';\n        return {\n          data: data.map(datum => Object.assign(Object.assign({}, datum), {\n            signedUrl: datum.signedURL ? encodeURI(`${this.url}${datum.signedURL}${downloadQueryParam}`) : null\n          })),\n          error: null\n        };\n      } catch (error) {\n        if (isStorageError(error)) {\n          return {\n            data: null,\n            error\n          };\n        }\n        throw error;\n      }\n    });\n  }\n  /**\n   * Downloads a file from a private bucket. For public buckets, make a request to the URL returned from `getPublicUrl` instead.\n   *\n   * @param path The full path and file name of the file to be downloaded. For example `folder/image.png`.\n   * @param options.transform Transform the asset before serving it to the client.\n   */\n  download(path, options) {\n    return __awaiter(this, void 0, void 0, function* () {\n      const wantsTransformation = typeof (options === null || options === void 0 ? void 0 : options.transform) !== 'undefined';\n      const renderPath = wantsTransformation ? 'render/image/authenticated' : 'object';\n      const transformationQuery = this.transformOptsToQueryString((options === null || options === void 0 ? void 0 : options.transform) || {});\n      const queryString = transformationQuery ? `?${transformationQuery}` : '';\n      try {\n        const _path = this._getFinalPath(path);\n        const res = yield get(this.fetch, `${this.url}/${renderPath}/${_path}${queryString}`, {\n          headers: this.headers,\n          noResolveJson: true\n        });\n        const data = yield res.blob();\n        return {\n          data,\n          error: null\n        };\n      } catch (error) {\n        if (isStorageError(error)) {\n          return {\n            data: null,\n            error\n          };\n        }\n        throw error;\n      }\n    });\n  }\n  /**\n   * Retrieves the details of an existing file.\n   * @param path\n   */\n  info(path) {\n    return __awaiter(this, void 0, void 0, function* () {\n      const _path = this._getFinalPath(path);\n      try {\n        const data = yield get(this.fetch, `${this.url}/object/info/${_path}`, {\n          headers: this.headers\n        });\n        return {\n          data: recursiveToCamel(data),\n          error: null\n        };\n      } catch (error) {\n        if (isStorageError(error)) {\n          return {\n            data: null,\n            error\n          };\n        }\n        throw error;\n      }\n    });\n  }\n  /**\n   * Checks the existence of a file.\n   * @param path\n   */\n  exists(path) {\n    return __awaiter(this, void 0, void 0, function* () {\n      const _path = this._getFinalPath(path);\n      try {\n        yield head(this.fetch, `${this.url}/object/${_path}`, {\n          headers: this.headers\n        });\n        return {\n          data: true,\n          error: null\n        };\n      } catch (error) {\n        if (isStorageError(error) && error instanceof StorageUnknownError) {\n          const originalError = error.originalError;\n          if ([400, 404].includes(originalError === null || originalError === void 0 ? void 0 : originalError.status)) {\n            return {\n              data: false,\n              error\n            };\n          }\n        }\n        throw error;\n      }\n    });\n  }\n  /**\n   * A simple convenience function to get the URL for an asset in a public bucket. If you do not want to use this function, you can construct the public URL by concatenating the bucket URL with the path to the asset.\n   * This function does not verify if the bucket is public. If a public URL is created for a bucket which is not public, you will not be able to download the asset.\n   *\n   * @param path The path and name of the file to generate the public URL for. For example `folder/image.png`.\n   * @param options.download Triggers the file as a download if set to true. Set this parameter as the name of the file if you want to trigger the download with a different filename.\n   * @param options.transform Transform the asset before serving it to the client.\n   */\n  getPublicUrl(path, options) {\n    const _path = this._getFinalPath(path);\n    const _queryString = [];\n    const downloadQueryParam = (options === null || options === void 0 ? void 0 : options.download) ? `download=${options.download === true ? '' : options.download}` : '';\n    if (downloadQueryParam !== '') {\n      _queryString.push(downloadQueryParam);\n    }\n    const wantsTransformation = typeof (options === null || options === void 0 ? void 0 : options.transform) !== 'undefined';\n    const renderPath = wantsTransformation ? 'render/image' : 'object';\n    const transformationQuery = this.transformOptsToQueryString((options === null || options === void 0 ? void 0 : options.transform) || {});\n    if (transformationQuery !== '') {\n      _queryString.push(transformationQuery);\n    }\n    let queryString = _queryString.join('&');\n    if (queryString !== '') {\n      queryString = `?${queryString}`;\n    }\n    return {\n      data: {\n        publicUrl: encodeURI(`${this.url}/${renderPath}/public/${_path}${queryString}`)\n      }\n    };\n  }\n  /**\n   * Deletes files within the same bucket\n   *\n   * @param paths An array of files to delete, including the path and file name. For example [`'folder/image.png'`].\n   */\n  remove(paths) {\n    return __awaiter(this, void 0, void 0, function* () {\n      try {\n        const data = yield remove(this.fetch, `${this.url}/object/${this.bucketId}`, {\n          prefixes: paths\n        }, {\n          headers: this.headers\n        });\n        return {\n          data,\n          error: null\n        };\n      } catch (error) {\n        if (isStorageError(error)) {\n          return {\n            data: null,\n            error\n          };\n        }\n        throw error;\n      }\n    });\n  }\n  /**\n   * Get file metadata\n   * @param id the file id to retrieve metadata\n   */\n  // async getMetadata(\n  //   id: string\n  // ): Promise<\n  //   | {\n  //       data: Metadata\n  //       error: null\n  //     }\n  //   | {\n  //       data: null\n  //       error: StorageError\n  //     }\n  // > {\n  //   try {\n  //     const data = await get(this.fetch, `${this.url}/metadata/${id}`, { headers: this.headers })\n  //     return { data, error: null }\n  //   } catch (error) {\n  //     if (isStorageError(error)) {\n  //       return { data: null, error }\n  //     }\n  //     throw error\n  //   }\n  // }\n  /**\n   * Update file metadata\n   * @param id the file id to update metadata\n   * @param meta the new file metadata\n   */\n  // async updateMetadata(\n  //   id: string,\n  //   meta: Metadata\n  // ): Promise<\n  //   | {\n  //       data: Metadata\n  //       error: null\n  //     }\n  //   | {\n  //       data: null\n  //       error: StorageError\n  //     }\n  // > {\n  //   try {\n  //     const data = await post(\n  //       this.fetch,\n  //       `${this.url}/metadata/${id}`,\n  //       { ...meta },\n  //       { headers: this.headers }\n  //     )\n  //     return { data, error: null }\n  //   } catch (error) {\n  //     if (isStorageError(error)) {\n  //       return { data: null, error }\n  //     }\n  //     throw error\n  //   }\n  // }\n  /**\n   * Lists all the files within a bucket.\n   * @param path The folder path.\n   */\n  list(path, options, parameters) {\n    return __awaiter(this, void 0, void 0, function* () {\n      try {\n        const body = Object.assign(Object.assign(Object.assign({}, DEFAULT_SEARCH_OPTIONS), options), {\n          prefix: path || ''\n        });\n        const data = yield post(this.fetch, `${this.url}/object/list/${this.bucketId}`, body, {\n          headers: this.headers\n        }, parameters);\n        return {\n          data,\n          error: null\n        };\n      } catch (error) {\n        if (isStorageError(error)) {\n          return {\n            data: null,\n            error\n          };\n        }\n        throw error;\n      }\n    });\n  }\n  encodeMetadata(metadata) {\n    return JSON.stringify(metadata);\n  }\n  toBase64(data) {\n    if (typeof Buffer !== 'undefined') {\n      return Buffer.from(data).toString('base64');\n    }\n    return btoa(data);\n  }\n  _getFinalPath(path) {\n    return `${this.bucketId}/${path}`;\n  }\n  _removeEmptyFolders(path) {\n    return path.replace(/^\\/|\\/$/g, '').replace(/\\/+/g, '/');\n  }\n  transformOptsToQueryString(transform) {\n    const params = [];\n    if (transform.width) {\n      params.push(`width=${transform.width}`);\n    }\n    if (transform.height) {\n      params.push(`height=${transform.height}`);\n    }\n    if (transform.resize) {\n      params.push(`resize=${transform.resize}`);\n    }\n    if (transform.format) {\n      params.push(`format=${transform.format}`);\n    }\n    if (transform.quality) {\n      params.push(`quality=${transform.quality}`);\n    }\n    return params.join('&');\n  }\n}", "map": {"version": 3, "names": ["isStorageError", "StorageError", "StorageUnknownError", "get", "head", "post", "remove", "recursiveToCamel", "resolveFetch", "DEFAULT_SEARCH_OPTIONS", "limit", "offset", "sortBy", "column", "order", "DEFAULT_FILE_OPTIONS", "cacheControl", "contentType", "upsert", "StorageFileApi", "constructor", "url", "headers", "bucketId", "fetch", "uploadOrUpdate", "method", "path", "fileBody", "fileOptions", "body", "options", "Object", "assign", "String", "metadata", "Blob", "FormData", "append", "encodeMetadata", "toBase64", "cleanPath", "_removeEmptyFolders", "_path", "_get<PERSON><PERSON><PERSON><PERSON>", "res", "duplex", "data", "json", "ok", "id", "Id", "fullPath", "Key", "error", "upload", "uploadToSignedUrl", "token", "URL", "searchParams", "set", "toString", "createSignedUploadUrl", "signedUrl", "update", "move", "fromPath", "to<PERSON><PERSON>", "sourceKey", "destinationKey", "destinationBucket", "copy", "createSignedUrl", "expiresIn", "transform", "downloadQueryParam", "download", "encodeURI", "signedURL", "createSignedUrls", "paths", "map", "datum", "wantsTransformation", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>y", "transformOptsToQueryString", "queryString", "noResolveJson", "blob", "info", "exists", "originalError", "includes", "status", "getPublicUrl", "_queryString", "push", "join", "publicUrl", "prefixes", "list", "parameters", "prefix", "JSON", "stringify", "<PERSON><PERSON><PERSON>", "from", "btoa", "replace", "params", "width", "height", "resize", "format", "quality"], "sources": ["C:\\Users\\<USER>\\node_modules\\@supabase\\storage-js\\src\\packages\\StorageFileApi.ts"], "sourcesContent": ["import { isStorageError, StorageError, StorageUnknownError } from '../lib/errors'\nimport { Fetch, get, head, post, remove } from '../lib/fetch'\nimport { recursiveToCamel, resolveFetch } from '../lib/helpers'\nimport {\n  FileObject,\n  FileOptions,\n  SearchOptions,\n  FetchParameters,\n  TransformOptions,\n  DestinationOptions,\n  FileObjectV2,\n  Camelize,\n} from '../lib/types'\n\nconst DEFAULT_SEARCH_OPTIONS = {\n  limit: 100,\n  offset: 0,\n  sortBy: {\n    column: 'name',\n    order: 'asc',\n  },\n}\n\nconst DEFAULT_FILE_OPTIONS: FileOptions = {\n  cacheControl: '3600',\n  contentType: 'text/plain;charset=UTF-8',\n  upsert: false,\n}\n\ntype FileBody =\n  | ArrayBuffer\n  | ArrayBufferView\n  | Blob\n  | Buffer\n  | File\n  | FormData\n  | NodeJS.ReadableStream\n  | ReadableStream<Uint8Array>\n  | URLSearchParams\n  | string\n\nexport default class StorageFileApi {\n  protected url: string\n  protected headers: { [key: string]: string }\n  protected bucketId?: string\n  protected fetch: Fetch\n\n  constructor(\n    url: string,\n    headers: { [key: string]: string } = {},\n    bucketId?: string,\n    fetch?: Fetch\n  ) {\n    this.url = url\n    this.headers = headers\n    this.bucketId = bucketId\n    this.fetch = resolveFetch(fetch)\n  }\n\n  /**\n   * Uploads a file to an existing bucket or replaces an existing file at the specified path with a new one.\n   *\n   * @param method HTTP method.\n   * @param path The relative file path. Should be of the format `folder/subfolder/filename.png`. The bucket must already exist before attempting to upload.\n   * @param fileBody The body of the file to be stored in the bucket.\n   */\n  private async uploadOrUpdate(\n    method: 'POST' | 'PUT',\n    path: string,\n    fileBody: FileBody,\n    fileOptions?: FileOptions\n  ): Promise<\n    | {\n        data: { id: string; path: string; fullPath: string }\n        error: null\n      }\n    | {\n        data: null\n        error: StorageError\n      }\n  > {\n    try {\n      let body\n      const options = { ...DEFAULT_FILE_OPTIONS, ...fileOptions }\n      let headers: Record<string, string> = {\n        ...this.headers,\n        ...(method === 'POST' && { 'x-upsert': String(options.upsert as boolean) }),\n      }\n\n      const metadata = options.metadata\n\n      if (typeof Blob !== 'undefined' && fileBody instanceof Blob) {\n        body = new FormData()\n        body.append('cacheControl', options.cacheControl as string)\n        if (metadata) {\n          body.append('metadata', this.encodeMetadata(metadata))\n        }\n        body.append('', fileBody)\n      } else if (typeof FormData !== 'undefined' && fileBody instanceof FormData) {\n        body = fileBody\n        body.append('cacheControl', options.cacheControl as string)\n        if (metadata) {\n          body.append('metadata', this.encodeMetadata(metadata))\n        }\n      } else {\n        body = fileBody\n        headers['cache-control'] = `max-age=${options.cacheControl}`\n        headers['content-type'] = options.contentType as string\n\n        if (metadata) {\n          headers['x-metadata'] = this.toBase64(this.encodeMetadata(metadata))\n        }\n      }\n\n      if (fileOptions?.headers) {\n        headers = { ...headers, ...fileOptions.headers }\n      }\n\n      const cleanPath = this._removeEmptyFolders(path)\n      const _path = this._getFinalPath(cleanPath)\n      const res = await this.fetch(`${this.url}/object/${_path}`, {\n        method,\n        body: body as BodyInit,\n        headers,\n        ...(options?.duplex ? { duplex: options.duplex } : {}),\n      })\n\n      const data = await res.json()\n\n      if (res.ok) {\n        return {\n          data: { path: cleanPath, id: data.Id, fullPath: data.Key },\n          error: null,\n        }\n      } else {\n        const error = data\n        return { data: null, error }\n      }\n    } catch (error) {\n      if (isStorageError(error)) {\n        return { data: null, error }\n      }\n\n      throw error\n    }\n  }\n\n  /**\n   * Uploads a file to an existing bucket.\n   *\n   * @param path The file path, including the file name. Should be of the format `folder/subfolder/filename.png`. The bucket must already exist before attempting to upload.\n   * @param fileBody The body of the file to be stored in the bucket.\n   */\n  async upload(\n    path: string,\n    fileBody: FileBody,\n    fileOptions?: FileOptions\n  ): Promise<\n    | {\n        data: { id: string; path: string; fullPath: string }\n        error: null\n      }\n    | {\n        data: null\n        error: StorageError\n      }\n  > {\n    return this.uploadOrUpdate('POST', path, fileBody, fileOptions)\n  }\n\n  /**\n   * Upload a file with a token generated from `createSignedUploadUrl`.\n   * @param path The file path, including the file name. Should be of the format `folder/subfolder/filename.png`. The bucket must already exist before attempting to upload.\n   * @param token The token generated from `createSignedUploadUrl`\n   * @param fileBody The body of the file to be stored in the bucket.\n   */\n  async uploadToSignedUrl(\n    path: string,\n    token: string,\n    fileBody: FileBody,\n    fileOptions?: FileOptions\n  ) {\n    const cleanPath = this._removeEmptyFolders(path)\n    const _path = this._getFinalPath(cleanPath)\n\n    const url = new URL(this.url + `/object/upload/sign/${_path}`)\n    url.searchParams.set('token', token)\n\n    try {\n      let body\n      const options = { upsert: DEFAULT_FILE_OPTIONS.upsert, ...fileOptions }\n      const headers: Record<string, string> = {\n        ...this.headers,\n        ...{ 'x-upsert': String(options.upsert as boolean) },\n      }\n\n      if (typeof Blob !== 'undefined' && fileBody instanceof Blob) {\n        body = new FormData()\n        body.append('cacheControl', options.cacheControl as string)\n        body.append('', fileBody)\n      } else if (typeof FormData !== 'undefined' && fileBody instanceof FormData) {\n        body = fileBody\n        body.append('cacheControl', options.cacheControl as string)\n      } else {\n        body = fileBody\n        headers['cache-control'] = `max-age=${options.cacheControl}`\n        headers['content-type'] = options.contentType as string\n      }\n\n      const res = await this.fetch(url.toString(), {\n        method: 'PUT',\n        body: body as BodyInit,\n        headers,\n      })\n\n      const data = await res.json()\n\n      if (res.ok) {\n        return {\n          data: { path: cleanPath, fullPath: data.Key },\n          error: null,\n        }\n      } else {\n        const error = data\n        return { data: null, error }\n      }\n    } catch (error) {\n      if (isStorageError(error)) {\n        return { data: null, error }\n      }\n\n      throw error\n    }\n  }\n\n  /**\n   * Creates a signed upload URL.\n   * Signed upload URLs can be used to upload files to the bucket without further authentication.\n   * They are valid for 2 hours.\n   * @param path The file path, including the current file name. For example `folder/image.png`.\n   * @param options.upsert If set to true, allows the file to be overwritten if it already exists.\n   */\n  async createSignedUploadUrl(\n    path: string,\n    options?: { upsert: boolean }\n  ): Promise<\n    | {\n        data: { signedUrl: string; token: string; path: string }\n        error: null\n      }\n    | {\n        data: null\n        error: StorageError\n      }\n  > {\n    try {\n      let _path = this._getFinalPath(path)\n\n      const headers = { ...this.headers }\n\n      if (options?.upsert) {\n        headers['x-upsert'] = 'true'\n      }\n\n      const data = await post(\n        this.fetch,\n        `${this.url}/object/upload/sign/${_path}`,\n        {},\n        { headers }\n      )\n\n      const url = new URL(this.url + data.url)\n\n      const token = url.searchParams.get('token')\n\n      if (!token) {\n        throw new StorageError('No token returned by API')\n      }\n\n      return { data: { signedUrl: url.toString(), path, token }, error: null }\n    } catch (error) {\n      if (isStorageError(error)) {\n        return { data: null, error }\n      }\n\n      throw error\n    }\n  }\n\n  /**\n   * Replaces an existing file at the specified path with a new one.\n   *\n   * @param path The relative file path. Should be of the format `folder/subfolder/filename.png`. The bucket must already exist before attempting to update.\n   * @param fileBody The body of the file to be stored in the bucket.\n   */\n  async update(\n    path: string,\n    fileBody:\n      | ArrayBuffer\n      | ArrayBufferView\n      | Blob\n      | Buffer\n      | File\n      | FormData\n      | NodeJS.ReadableStream\n      | ReadableStream<Uint8Array>\n      | URLSearchParams\n      | string,\n    fileOptions?: FileOptions\n  ): Promise<\n    | {\n        data: { id: string; path: string; fullPath: string }\n        error: null\n      }\n    | {\n        data: null\n        error: StorageError\n      }\n  > {\n    return this.uploadOrUpdate('PUT', path, fileBody, fileOptions)\n  }\n\n  /**\n   * Moves an existing file to a new path in the same bucket.\n   *\n   * @param fromPath The original file path, including the current file name. For example `folder/image.png`.\n   * @param toPath The new file path, including the new file name. For example `folder/image-new.png`.\n   * @param options The destination options.\n   */\n  async move(\n    fromPath: string,\n    toPath: string,\n    options?: DestinationOptions\n  ): Promise<\n    | {\n        data: { message: string }\n        error: null\n      }\n    | {\n        data: null\n        error: StorageError\n      }\n  > {\n    try {\n      const data = await post(\n        this.fetch,\n        `${this.url}/object/move`,\n        {\n          bucketId: this.bucketId,\n          sourceKey: fromPath,\n          destinationKey: toPath,\n          destinationBucket: options?.destinationBucket,\n        },\n        { headers: this.headers }\n      )\n      return { data, error: null }\n    } catch (error) {\n      if (isStorageError(error)) {\n        return { data: null, error }\n      }\n\n      throw error\n    }\n  }\n\n  /**\n   * Copies an existing file to a new path in the same bucket.\n   *\n   * @param fromPath The original file path, including the current file name. For example `folder/image.png`.\n   * @param toPath The new file path, including the new file name. For example `folder/image-copy.png`.\n   * @param options The destination options.\n   */\n  async copy(\n    fromPath: string,\n    toPath: string,\n    options?: DestinationOptions\n  ): Promise<\n    | {\n        data: { path: string }\n        error: null\n      }\n    | {\n        data: null\n        error: StorageError\n      }\n  > {\n    try {\n      const data = await post(\n        this.fetch,\n        `${this.url}/object/copy`,\n        {\n          bucketId: this.bucketId,\n          sourceKey: fromPath,\n          destinationKey: toPath,\n          destinationBucket: options?.destinationBucket,\n        },\n        { headers: this.headers }\n      )\n      return { data: { path: data.Key }, error: null }\n    } catch (error) {\n      if (isStorageError(error)) {\n        return { data: null, error }\n      }\n\n      throw error\n    }\n  }\n\n  /**\n   * Creates a signed URL. Use a signed URL to share a file for a fixed amount of time.\n   *\n   * @param path The file path, including the current file name. For example `folder/image.png`.\n   * @param expiresIn The number of seconds until the signed URL expires. For example, `60` for a URL which is valid for one minute.\n   * @param options.download triggers the file as a download if set to true. Set this parameter as the name of the file if you want to trigger the download with a different filename.\n   * @param options.transform Transform the asset before serving it to the client.\n   */\n  async createSignedUrl(\n    path: string,\n    expiresIn: number,\n    options?: { download?: string | boolean; transform?: TransformOptions }\n  ): Promise<\n    | {\n        data: { signedUrl: string }\n        error: null\n      }\n    | {\n        data: null\n        error: StorageError\n      }\n  > {\n    try {\n      let _path = this._getFinalPath(path)\n\n      let data = await post(\n        this.fetch,\n        `${this.url}/object/sign/${_path}`,\n        { expiresIn, ...(options?.transform ? { transform: options.transform } : {}) },\n        { headers: this.headers }\n      )\n      const downloadQueryParam = options?.download\n        ? `&download=${options.download === true ? '' : options.download}`\n        : ''\n      const signedUrl = encodeURI(`${this.url}${data.signedURL}${downloadQueryParam}`)\n      data = { signedUrl }\n      return { data, error: null }\n    } catch (error) {\n      if (isStorageError(error)) {\n        return { data: null, error }\n      }\n\n      throw error\n    }\n  }\n\n  /**\n   * Creates multiple signed URLs. Use a signed URL to share a file for a fixed amount of time.\n   *\n   * @param paths The file paths to be downloaded, including the current file names. For example `['folder/image.png', 'folder2/image2.png']`.\n   * @param expiresIn The number of seconds until the signed URLs expire. For example, `60` for URLs which are valid for one minute.\n   * @param options.download triggers the file as a download if set to true. Set this parameter as the name of the file if you want to trigger the download with a different filename.\n   */\n  async createSignedUrls(\n    paths: string[],\n    expiresIn: number,\n    options?: { download: string | boolean }\n  ): Promise<\n    | {\n        data: { error: string | null; path: string | null; signedUrl: string }[]\n        error: null\n      }\n    | {\n        data: null\n        error: StorageError\n      }\n  > {\n    try {\n      const data = await post(\n        this.fetch,\n        `${this.url}/object/sign/${this.bucketId}`,\n        { expiresIn, paths },\n        { headers: this.headers }\n      )\n\n      const downloadQueryParam = options?.download\n        ? `&download=${options.download === true ? '' : options.download}`\n        : ''\n      return {\n        data: data.map((datum: { signedURL: string }) => ({\n          ...datum,\n          signedUrl: datum.signedURL\n            ? encodeURI(`${this.url}${datum.signedURL}${downloadQueryParam}`)\n            : null,\n        })),\n        error: null,\n      }\n    } catch (error) {\n      if (isStorageError(error)) {\n        return { data: null, error }\n      }\n\n      throw error\n    }\n  }\n\n  /**\n   * Downloads a file from a private bucket. For public buckets, make a request to the URL returned from `getPublicUrl` instead.\n   *\n   * @param path The full path and file name of the file to be downloaded. For example `folder/image.png`.\n   * @param options.transform Transform the asset before serving it to the client.\n   */\n  async download(\n    path: string,\n    options?: { transform?: TransformOptions }\n  ): Promise<\n    | {\n        data: Blob\n        error: null\n      }\n    | {\n        data: null\n        error: StorageError\n      }\n  > {\n    const wantsTransformation = typeof options?.transform !== 'undefined'\n    const renderPath = wantsTransformation ? 'render/image/authenticated' : 'object'\n    const transformationQuery = this.transformOptsToQueryString(options?.transform || {})\n    const queryString = transformationQuery ? `?${transformationQuery}` : ''\n\n    try {\n      const _path = this._getFinalPath(path)\n      const res = await get(this.fetch, `${this.url}/${renderPath}/${_path}${queryString}`, {\n        headers: this.headers,\n        noResolveJson: true,\n      })\n      const data = await res.blob()\n      return { data, error: null }\n    } catch (error) {\n      if (isStorageError(error)) {\n        return { data: null, error }\n      }\n\n      throw error\n    }\n  }\n\n  /**\n   * Retrieves the details of an existing file.\n   * @param path\n   */\n  async info(\n    path: string\n  ): Promise<\n    | {\n        data: Camelize<FileObjectV2>\n        error: null\n      }\n    | {\n        data: null\n        error: StorageError\n      }\n  > {\n    const _path = this._getFinalPath(path)\n\n    try {\n      const data = await get(this.fetch, `${this.url}/object/info/${_path}`, {\n        headers: this.headers,\n      })\n\n      return { data: recursiveToCamel(data) as Camelize<FileObjectV2>, error: null }\n    } catch (error) {\n      if (isStorageError(error)) {\n        return { data: null, error }\n      }\n\n      throw error\n    }\n  }\n\n  /**\n   * Checks the existence of a file.\n   * @param path\n   */\n  async exists(\n    path: string\n  ): Promise<\n    | {\n        data: boolean\n        error: null\n      }\n    | {\n        data: boolean\n        error: StorageError\n      }\n  > {\n    const _path = this._getFinalPath(path)\n\n    try {\n      await head(this.fetch, `${this.url}/object/${_path}`, {\n        headers: this.headers,\n      })\n\n      return { data: true, error: null }\n    } catch (error) {\n      if (isStorageError(error) && error instanceof StorageUnknownError) {\n        const originalError = (error.originalError as unknown) as { status: number }\n\n        if ([400, 404].includes(originalError?.status)) {\n          return { data: false, error }\n        }\n      }\n\n      throw error\n    }\n  }\n\n  /**\n   * A simple convenience function to get the URL for an asset in a public bucket. If you do not want to use this function, you can construct the public URL by concatenating the bucket URL with the path to the asset.\n   * This function does not verify if the bucket is public. If a public URL is created for a bucket which is not public, you will not be able to download the asset.\n   *\n   * @param path The path and name of the file to generate the public URL for. For example `folder/image.png`.\n   * @param options.download Triggers the file as a download if set to true. Set this parameter as the name of the file if you want to trigger the download with a different filename.\n   * @param options.transform Transform the asset before serving it to the client.\n   */\n  getPublicUrl(\n    path: string,\n    options?: { download?: string | boolean; transform?: TransformOptions }\n  ): { data: { publicUrl: string } } {\n    const _path = this._getFinalPath(path)\n    const _queryString = []\n\n    const downloadQueryParam = options?.download\n      ? `download=${options.download === true ? '' : options.download}`\n      : ''\n\n    if (downloadQueryParam !== '') {\n      _queryString.push(downloadQueryParam)\n    }\n\n    const wantsTransformation = typeof options?.transform !== 'undefined'\n    const renderPath = wantsTransformation ? 'render/image' : 'object'\n    const transformationQuery = this.transformOptsToQueryString(options?.transform || {})\n\n    if (transformationQuery !== '') {\n      _queryString.push(transformationQuery)\n    }\n\n    let queryString = _queryString.join('&')\n    if (queryString !== '') {\n      queryString = `?${queryString}`\n    }\n\n    return {\n      data: { publicUrl: encodeURI(`${this.url}/${renderPath}/public/${_path}${queryString}`) },\n    }\n  }\n\n  /**\n   * Deletes files within the same bucket\n   *\n   * @param paths An array of files to delete, including the path and file name. For example [`'folder/image.png'`].\n   */\n  async remove(\n    paths: string[]\n  ): Promise<\n    | {\n        data: FileObject[]\n        error: null\n      }\n    | {\n        data: null\n        error: StorageError\n      }\n  > {\n    try {\n      const data = await remove(\n        this.fetch,\n        `${this.url}/object/${this.bucketId}`,\n        { prefixes: paths },\n        { headers: this.headers }\n      )\n      return { data, error: null }\n    } catch (error) {\n      if (isStorageError(error)) {\n        return { data: null, error }\n      }\n\n      throw error\n    }\n  }\n\n  /**\n   * Get file metadata\n   * @param id the file id to retrieve metadata\n   */\n  // async getMetadata(\n  //   id: string\n  // ): Promise<\n  //   | {\n  //       data: Metadata\n  //       error: null\n  //     }\n  //   | {\n  //       data: null\n  //       error: StorageError\n  //     }\n  // > {\n  //   try {\n  //     const data = await get(this.fetch, `${this.url}/metadata/${id}`, { headers: this.headers })\n  //     return { data, error: null }\n  //   } catch (error) {\n  //     if (isStorageError(error)) {\n  //       return { data: null, error }\n  //     }\n\n  //     throw error\n  //   }\n  // }\n\n  /**\n   * Update file metadata\n   * @param id the file id to update metadata\n   * @param meta the new file metadata\n   */\n  // async updateMetadata(\n  //   id: string,\n  //   meta: Metadata\n  // ): Promise<\n  //   | {\n  //       data: Metadata\n  //       error: null\n  //     }\n  //   | {\n  //       data: null\n  //       error: StorageError\n  //     }\n  // > {\n  //   try {\n  //     const data = await post(\n  //       this.fetch,\n  //       `${this.url}/metadata/${id}`,\n  //       { ...meta },\n  //       { headers: this.headers }\n  //     )\n  //     return { data, error: null }\n  //   } catch (error) {\n  //     if (isStorageError(error)) {\n  //       return { data: null, error }\n  //     }\n\n  //     throw error\n  //   }\n  // }\n\n  /**\n   * Lists all the files within a bucket.\n   * @param path The folder path.\n   */\n  async list(\n    path?: string,\n    options?: SearchOptions,\n    parameters?: FetchParameters\n  ): Promise<\n    | {\n        data: FileObject[]\n        error: null\n      }\n    | {\n        data: null\n        error: StorageError\n      }\n  > {\n    try {\n      const body = { ...DEFAULT_SEARCH_OPTIONS, ...options, prefix: path || '' }\n      const data = await post(\n        this.fetch,\n        `${this.url}/object/list/${this.bucketId}`,\n        body,\n        { headers: this.headers },\n        parameters\n      )\n      return { data, error: null }\n    } catch (error) {\n      if (isStorageError(error)) {\n        return { data: null, error }\n      }\n\n      throw error\n    }\n  }\n\n  protected encodeMetadata(metadata: Record<string, any>) {\n    return JSON.stringify(metadata)\n  }\n\n  toBase64(data: string) {\n    if (typeof Buffer !== 'undefined') {\n      return Buffer.from(data).toString('base64')\n    }\n    return btoa(data)\n  }\n\n  private _getFinalPath(path: string) {\n    return `${this.bucketId}/${path}`\n  }\n\n  private _removeEmptyFolders(path: string) {\n    return path.replace(/^\\/|\\/$/g, '').replace(/\\/+/g, '/')\n  }\n\n  private transformOptsToQueryString(transform: TransformOptions) {\n    const params = []\n    if (transform.width) {\n      params.push(`width=${transform.width}`)\n    }\n\n    if (transform.height) {\n      params.push(`height=${transform.height}`)\n    }\n\n    if (transform.resize) {\n      params.push(`resize=${transform.resize}`)\n    }\n\n    if (transform.format) {\n      params.push(`format=${transform.format}`)\n    }\n\n    if (transform.quality) {\n      params.push(`quality=${transform.quality}`)\n    }\n\n    return params.join('&')\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,SAASA,cAAc,EAAEC,YAAY,EAAEC,mBAAmB,QAAQ,eAAe;AACjF,SAAgBC,GAAG,EAAEC,IAAI,EAAEC,IAAI,EAAEC,MAAM,QAAQ,cAAc;AAC7D,SAASC,gBAAgB,EAAEC,YAAY,QAAQ,gBAAgB;AAY/D,MAAMC,sBAAsB,GAAG;EAC7BC,KAAK,EAAE,GAAG;EACVC,MAAM,EAAE,CAAC;EACTC,MAAM,EAAE;IACNC,MAAM,EAAE,MAAM;IACdC,KAAK,EAAE;;CAEV;AAED,MAAMC,oBAAoB,GAAgB;EACxCC,YAAY,EAAE,MAAM;EACpBC,WAAW,EAAE,0BAA0B;EACvCC,MAAM,EAAE;CACT;AAcD,eAAc,MAAOC,cAAc;EAMjCC,YACEC,GAAW,EACXC,OAAA,GAAqC,EAAE,EACvCC,QAAiB,EACjBC,KAAa;IAEb,IAAI,CAACH,GAAG,GAAGA,GAAG;IACd,IAAI,CAACC,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACC,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACC,KAAK,GAAGhB,YAAY,CAACgB,KAAK,CAAC;EAClC;EAEA;;;;;;;EAOcC,cAAcA,CAC1BC,MAAsB,EACtBC,IAAY,EACZC,QAAkB,EAClBC,WAAyB;;MAWzB,IAAI;QACF,IAAIC,IAAI;QACR,MAAMC,OAAO,GAAAC,MAAA,CAAAC,MAAA,CAAAD,MAAA,CAAAC,MAAA,KAAQlB,oBAAoB,GAAKc,WAAW,CAAE;QAC3D,IAAIP,OAAO,GAAAU,MAAA,CAAAC,MAAA,CAAAD,MAAA,CAAAC,MAAA,KACN,IAAI,CAACX,OAAO,GACXI,MAAM,KAAK,MAAM,IAAI;UAAE,UAAU,EAAEQ,MAAM,CAACH,OAAO,CAACb,MAAiB;QAAC,CAAG,CAC5E;QAED,MAAMiB,QAAQ,GAAGJ,OAAO,CAACI,QAAQ;QAEjC,IAAI,OAAOC,IAAI,KAAK,WAAW,IAAIR,QAAQ,YAAYQ,IAAI,EAAE;UAC3DN,IAAI,GAAG,IAAIO,QAAQ,EAAE;UACrBP,IAAI,CAACQ,MAAM,CAAC,cAAc,EAAEP,OAAO,CAACf,YAAsB,CAAC;UAC3D,IAAImB,QAAQ,EAAE;YACZL,IAAI,CAACQ,MAAM,CAAC,UAAU,EAAE,IAAI,CAACC,cAAc,CAACJ,QAAQ,CAAC,CAAC;;UAExDL,IAAI,CAACQ,MAAM,CAAC,EAAE,EAAEV,QAAQ,CAAC;SAC1B,MAAM,IAAI,OAAOS,QAAQ,KAAK,WAAW,IAAIT,QAAQ,YAAYS,QAAQ,EAAE;UAC1EP,IAAI,GAAGF,QAAQ;UACfE,IAAI,CAACQ,MAAM,CAAC,cAAc,EAAEP,OAAO,CAACf,YAAsB,CAAC;UAC3D,IAAImB,QAAQ,EAAE;YACZL,IAAI,CAACQ,MAAM,CAAC,UAAU,EAAE,IAAI,CAACC,cAAc,CAACJ,QAAQ,CAAC,CAAC;;SAEzD,MAAM;UACLL,IAAI,GAAGF,QAAQ;UACfN,OAAO,CAAC,eAAe,CAAC,GAAG,WAAWS,OAAO,CAACf,YAAY,EAAE;UAC5DM,OAAO,CAAC,cAAc,CAAC,GAAGS,OAAO,CAACd,WAAqB;UAEvD,IAAIkB,QAAQ,EAAE;YACZb,OAAO,CAAC,YAAY,CAAC,GAAG,IAAI,CAACkB,QAAQ,CAAC,IAAI,CAACD,cAAc,CAACJ,QAAQ,CAAC,CAAC;;;QAIxE,IAAIN,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEP,OAAO,EAAE;UACxBA,OAAO,GAAAU,MAAA,CAAAC,MAAA,CAAAD,MAAA,CAAAC,MAAA,KAAQX,OAAO,GAAKO,WAAW,CAACP,OAAO,CAAE;;QAGlD,MAAMmB,SAAS,GAAG,IAAI,CAACC,mBAAmB,CAACf,IAAI,CAAC;QAChD,MAAMgB,KAAK,GAAG,IAAI,CAACC,aAAa,CAACH,SAAS,CAAC;QAC3C,MAAMI,GAAG,GAAG,MAAM,IAAI,CAACrB,KAAK,CAAC,GAAG,IAAI,CAACH,GAAG,WAAWsB,KAAK,EAAE,EAAAX,MAAA,CAAAC,MAAA;UACxDP,MAAM;UACNI,IAAI,EAAEA,IAAgB;UACtBR;QAAO,GACH,CAAAS,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEe,MAAM,IAAG;UAAEA,MAAM,EAAEf,OAAO,CAACe;QAAM,CAAE,GAAG,EAAG,EACtD;QAEF,MAAMC,IAAI,GAAG,MAAMF,GAAG,CAACG,IAAI,EAAE;QAE7B,IAAIH,GAAG,CAACI,EAAE,EAAE;UACV,OAAO;YACLF,IAAI,EAAE;cAAEpB,IAAI,EAAEc,SAAS;cAAES,EAAE,EAAEH,IAAI,CAACI,EAAE;cAAEC,QAAQ,EAAEL,IAAI,CAACM;YAAG,CAAE;YAC1DC,KAAK,EAAE;WACR;SACF,MAAM;UACL,MAAMA,KAAK,GAAGP,IAAI;UAClB,OAAO;YAAEA,IAAI,EAAE,IAAI;YAAEO;UAAK,CAAE;;OAE/B,CAAC,OAAOA,KAAK,EAAE;QACd,IAAItD,cAAc,CAACsD,KAAK,CAAC,EAAE;UACzB,OAAO;YAAEP,IAAI,EAAE,IAAI;YAAEO;UAAK,CAAE;;QAG9B,MAAMA,KAAK;;IAEf,CAAC;;EAED;;;;;;EAMMC,MAAMA,CACV5B,IAAY,EACZC,QAAkB,EAClBC,WAAyB;;MAWzB,OAAO,IAAI,CAACJ,cAAc,CAAC,MAAM,EAAEE,IAAI,EAAEC,QAAQ,EAAEC,WAAW,CAAC;IACjE,CAAC;;EAED;;;;;;EAMM2B,iBAAiBA,CACrB7B,IAAY,EACZ8B,KAAa,EACb7B,QAAkB,EAClBC,WAAyB;;MAEzB,MAAMY,SAAS,GAAG,IAAI,CAACC,mBAAmB,CAACf,IAAI,CAAC;MAChD,MAAMgB,KAAK,GAAG,IAAI,CAACC,aAAa,CAACH,SAAS,CAAC;MAE3C,MAAMpB,GAAG,GAAG,IAAIqC,GAAG,CAAC,IAAI,CAACrC,GAAG,GAAG,uBAAuBsB,KAAK,EAAE,CAAC;MAC9DtB,GAAG,CAACsC,YAAY,CAACC,GAAG,CAAC,OAAO,EAAEH,KAAK,CAAC;MAEpC,IAAI;QACF,IAAI3B,IAAI;QACR,MAAMC,OAAO,GAAAC,MAAA,CAAAC,MAAA;UAAKf,MAAM,EAAEH,oBAAoB,CAACG;QAAM,GAAKW,WAAW,CAAE;QACvE,MAAMP,OAAO,GAAAU,MAAA,CAAAC,MAAA,CAAAD,MAAA,CAAAC,MAAA,KACR,IAAI,CAACX,OAAO,GACZ;UAAE,UAAU,EAAEY,MAAM,CAACH,OAAO,CAACb,MAAiB;QAAC,CAAE,CACrD;QAED,IAAI,OAAOkB,IAAI,KAAK,WAAW,IAAIR,QAAQ,YAAYQ,IAAI,EAAE;UAC3DN,IAAI,GAAG,IAAIO,QAAQ,EAAE;UACrBP,IAAI,CAACQ,MAAM,CAAC,cAAc,EAAEP,OAAO,CAACf,YAAsB,CAAC;UAC3Dc,IAAI,CAACQ,MAAM,CAAC,EAAE,EAAEV,QAAQ,CAAC;SAC1B,MAAM,IAAI,OAAOS,QAAQ,KAAK,WAAW,IAAIT,QAAQ,YAAYS,QAAQ,EAAE;UAC1EP,IAAI,GAAGF,QAAQ;UACfE,IAAI,CAACQ,MAAM,CAAC,cAAc,EAAEP,OAAO,CAACf,YAAsB,CAAC;SAC5D,MAAM;UACLc,IAAI,GAAGF,QAAQ;UACfN,OAAO,CAAC,eAAe,CAAC,GAAG,WAAWS,OAAO,CAACf,YAAY,EAAE;UAC5DM,OAAO,CAAC,cAAc,CAAC,GAAGS,OAAO,CAACd,WAAqB;;QAGzD,MAAM4B,GAAG,GAAG,MAAM,IAAI,CAACrB,KAAK,CAACH,GAAG,CAACwC,QAAQ,EAAE,EAAE;UAC3CnC,MAAM,EAAE,KAAK;UACbI,IAAI,EAAEA,IAAgB;UACtBR;SACD,CAAC;QAEF,MAAMyB,IAAI,GAAG,MAAMF,GAAG,CAACG,IAAI,EAAE;QAE7B,IAAIH,GAAG,CAACI,EAAE,EAAE;UACV,OAAO;YACLF,IAAI,EAAE;cAAEpB,IAAI,EAAEc,SAAS;cAAEW,QAAQ,EAAEL,IAAI,CAACM;YAAG,CAAE;YAC7CC,KAAK,EAAE;WACR;SACF,MAAM;UACL,MAAMA,KAAK,GAAGP,IAAI;UAClB,OAAO;YAAEA,IAAI,EAAE,IAAI;YAAEO;UAAK,CAAE;;OAE/B,CAAC,OAAOA,KAAK,EAAE;QACd,IAAItD,cAAc,CAACsD,KAAK,CAAC,EAAE;UACzB,OAAO;YAAEP,IAAI,EAAE,IAAI;YAAEO;UAAK,CAAE;;QAG9B,MAAMA,KAAK;;IAEf,CAAC;;EAED;;;;;;;EAOMQ,qBAAqBA,CACzBnC,IAAY,EACZI,OAA6B;;MAW7B,IAAI;QACF,IAAIY,KAAK,GAAG,IAAI,CAACC,aAAa,CAACjB,IAAI,CAAC;QAEpC,MAAML,OAAO,GAAAU,MAAA,CAAAC,MAAA,KAAQ,IAAI,CAACX,OAAO,CAAE;QAEnC,IAAIS,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEb,MAAM,EAAE;UACnBI,OAAO,CAAC,UAAU,CAAC,GAAG,MAAM;;QAG9B,MAAMyB,IAAI,GAAG,MAAM1C,IAAI,CACrB,IAAI,CAACmB,KAAK,EACV,GAAG,IAAI,CAACH,GAAG,uBAAuBsB,KAAK,EAAE,EACzC,EAAE,EACF;UAAErB;QAAO,CAAE,CACZ;QAED,MAAMD,GAAG,GAAG,IAAIqC,GAAG,CAAC,IAAI,CAACrC,GAAG,GAAG0B,IAAI,CAAC1B,GAAG,CAAC;QAExC,MAAMoC,KAAK,GAAGpC,GAAG,CAACsC,YAAY,CAACxD,GAAG,CAAC,OAAO,CAAC;QAE3C,IAAI,CAACsD,KAAK,EAAE;UACV,MAAM,IAAIxD,YAAY,CAAC,0BAA0B,CAAC;;QAGpD,OAAO;UAAE8C,IAAI,EAAE;YAAEgB,SAAS,EAAE1C,GAAG,CAACwC,QAAQ,EAAE;YAAElC,IAAI;YAAE8B;UAAK,CAAE;UAAEH,KAAK,EAAE;QAAI,CAAE;OACzE,CAAC,OAAOA,KAAK,EAAE;QACd,IAAItD,cAAc,CAACsD,KAAK,CAAC,EAAE;UACzB,OAAO;YAAEP,IAAI,EAAE,IAAI;YAAEO;UAAK,CAAE;;QAG9B,MAAMA,KAAK;;IAEf,CAAC;;EAED;;;;;;EAMMU,MAAMA,CACVrC,IAAY,EACZC,QAUU,EACVC,WAAyB;;MAWzB,OAAO,IAAI,CAACJ,cAAc,CAAC,KAAK,EAAEE,IAAI,EAAEC,QAAQ,EAAEC,WAAW,CAAC;IAChE,CAAC;;EAED;;;;;;;EAOMoC,IAAIA,CACRC,QAAgB,EAChBC,MAAc,EACdpC,OAA4B;;MAW5B,IAAI;QACF,MAAMgB,IAAI,GAAG,MAAM1C,IAAI,CACrB,IAAI,CAACmB,KAAK,EACV,GAAG,IAAI,CAACH,GAAG,cAAc,EACzB;UACEE,QAAQ,EAAE,IAAI,CAACA,QAAQ;UACvB6C,SAAS,EAAEF,QAAQ;UACnBG,cAAc,EAAEF,MAAM;UACtBG,iBAAiB,EAAEvC,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEuC;SAC7B,EACD;UAAEhD,OAAO,EAAE,IAAI,CAACA;QAAO,CAAE,CAC1B;QACD,OAAO;UAAEyB,IAAI;UAAEO,KAAK,EAAE;QAAI,CAAE;OAC7B,CAAC,OAAOA,KAAK,EAAE;QACd,IAAItD,cAAc,CAACsD,KAAK,CAAC,EAAE;UACzB,OAAO;YAAEP,IAAI,EAAE,IAAI;YAAEO;UAAK,CAAE;;QAG9B,MAAMA,KAAK;;IAEf,CAAC;;EAED;;;;;;;EAOMiB,IAAIA,CACRL,QAAgB,EAChBC,MAAc,EACdpC,OAA4B;;MAW5B,IAAI;QACF,MAAMgB,IAAI,GAAG,MAAM1C,IAAI,CACrB,IAAI,CAACmB,KAAK,EACV,GAAG,IAAI,CAACH,GAAG,cAAc,EACzB;UACEE,QAAQ,EAAE,IAAI,CAACA,QAAQ;UACvB6C,SAAS,EAAEF,QAAQ;UACnBG,cAAc,EAAEF,MAAM;UACtBG,iBAAiB,EAAEvC,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEuC;SAC7B,EACD;UAAEhD,OAAO,EAAE,IAAI,CAACA;QAAO,CAAE,CAC1B;QACD,OAAO;UAAEyB,IAAI,EAAE;YAAEpB,IAAI,EAAEoB,IAAI,CAACM;UAAG,CAAE;UAAEC,KAAK,EAAE;QAAI,CAAE;OACjD,CAAC,OAAOA,KAAK,EAAE;QACd,IAAItD,cAAc,CAACsD,KAAK,CAAC,EAAE;UACzB,OAAO;YAAEP,IAAI,EAAE,IAAI;YAAEO;UAAK,CAAE;;QAG9B,MAAMA,KAAK;;IAEf,CAAC;;EAED;;;;;;;;EAQMkB,eAAeA,CACnB7C,IAAY,EACZ8C,SAAiB,EACjB1C,OAAuE;;MAWvE,IAAI;QACF,IAAIY,KAAK,GAAG,IAAI,CAACC,aAAa,CAACjB,IAAI,CAAC;QAEpC,IAAIoB,IAAI,GAAG,MAAM1C,IAAI,CACnB,IAAI,CAACmB,KAAK,EACV,GAAG,IAAI,CAACH,GAAG,gBAAgBsB,KAAK,EAAE,EAAAX,MAAA,CAAAC,MAAA;UAChCwC;QAAS,GAAM,CAAA1C,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAE2C,SAAS,IAAG;UAAEA,SAAS,EAAE3C,OAAO,CAAC2C;QAAS,CAAE,GAAG,EAAG,GAC5E;UAAEpD,OAAO,EAAE,IAAI,CAACA;QAAO,CAAE,CAC1B;QACD,MAAMqD,kBAAkB,GAAG,CAAA5C,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAE6C,QAAQ,IACxC,aAAa7C,OAAO,CAAC6C,QAAQ,KAAK,IAAI,GAAG,EAAE,GAAG7C,OAAO,CAAC6C,QAAQ,EAAE,GAChE,EAAE;QACN,MAAMb,SAAS,GAAGc,SAAS,CAAC,GAAG,IAAI,CAACxD,GAAG,GAAG0B,IAAI,CAAC+B,SAAS,GAAGH,kBAAkB,EAAE,CAAC;QAChF5B,IAAI,GAAG;UAAEgB;QAAS,CAAE;QACpB,OAAO;UAAEhB,IAAI;UAAEO,KAAK,EAAE;QAAI,CAAE;OAC7B,CAAC,OAAOA,KAAK,EAAE;QACd,IAAItD,cAAc,CAACsD,KAAK,CAAC,EAAE;UACzB,OAAO;YAAEP,IAAI,EAAE,IAAI;YAAEO;UAAK,CAAE;;QAG9B,MAAMA,KAAK;;IAEf,CAAC;;EAED;;;;;;;EAOMyB,gBAAgBA,CACpBC,KAAe,EACfP,SAAiB,EACjB1C,OAAwC;;MAWxC,IAAI;QACF,MAAMgB,IAAI,GAAG,MAAM1C,IAAI,CACrB,IAAI,CAACmB,KAAK,EACV,GAAG,IAAI,CAACH,GAAG,gBAAgB,IAAI,CAACE,QAAQ,EAAE,EAC1C;UAAEkD,SAAS;UAAEO;QAAK,CAAE,EACpB;UAAE1D,OAAO,EAAE,IAAI,CAACA;QAAO,CAAE,CAC1B;QAED,MAAMqD,kBAAkB,GAAG,CAAA5C,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAE6C,QAAQ,IACxC,aAAa7C,OAAO,CAAC6C,QAAQ,KAAK,IAAI,GAAG,EAAE,GAAG7C,OAAO,CAAC6C,QAAQ,EAAE,GAChE,EAAE;QACN,OAAO;UACL7B,IAAI,EAAEA,IAAI,CAACkC,GAAG,CAAEC,KAA4B,IAAKlD,MAAA,CAAAC,MAAA,CAAAD,MAAA,CAAAC,MAAA,KAC5CiD,KAAK;YACRnB,SAAS,EAAEmB,KAAK,CAACJ,SAAS,GACtBD,SAAS,CAAC,GAAG,IAAI,CAACxD,GAAG,GAAG6D,KAAK,CAACJ,SAAS,GAAGH,kBAAkB,EAAE,CAAC,GAC/D;UAAI,EACR,CAAC;UACHrB,KAAK,EAAE;SACR;OACF,CAAC,OAAOA,KAAK,EAAE;QACd,IAAItD,cAAc,CAACsD,KAAK,CAAC,EAAE;UACzB,OAAO;YAAEP,IAAI,EAAE,IAAI;YAAEO;UAAK,CAAE;;QAG9B,MAAMA,KAAK;;IAEf,CAAC;;EAED;;;;;;EAMMsB,QAAQA,CACZjD,IAAY,EACZI,OAA0C;;MAW1C,MAAMoD,mBAAmB,GAAG,QAAOpD,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAE2C,SAAS,MAAK,WAAW;MACrE,MAAMU,UAAU,GAAGD,mBAAmB,GAAG,4BAA4B,GAAG,QAAQ;MAChF,MAAME,mBAAmB,GAAG,IAAI,CAACC,0BAA0B,CAAC,CAAAvD,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAE2C,SAAS,KAAI,EAAE,CAAC;MACrF,MAAMa,WAAW,GAAGF,mBAAmB,GAAG,IAAIA,mBAAmB,EAAE,GAAG,EAAE;MAExE,IAAI;QACF,MAAM1C,KAAK,GAAG,IAAI,CAACC,aAAa,CAACjB,IAAI,CAAC;QACtC,MAAMkB,GAAG,GAAG,MAAM1C,GAAG,CAAC,IAAI,CAACqB,KAAK,EAAE,GAAG,IAAI,CAACH,GAAG,IAAI+D,UAAU,IAAIzC,KAAK,GAAG4C,WAAW,EAAE,EAAE;UACpFjE,OAAO,EAAE,IAAI,CAACA,OAAO;UACrBkE,aAAa,EAAE;SAChB,CAAC;QACF,MAAMzC,IAAI,GAAG,MAAMF,GAAG,CAAC4C,IAAI,EAAE;QAC7B,OAAO;UAAE1C,IAAI;UAAEO,KAAK,EAAE;QAAI,CAAE;OAC7B,CAAC,OAAOA,KAAK,EAAE;QACd,IAAItD,cAAc,CAACsD,KAAK,CAAC,EAAE;UACzB,OAAO;YAAEP,IAAI,EAAE,IAAI;YAAEO;UAAK,CAAE;;QAG9B,MAAMA,KAAK;;IAEf,CAAC;;EAED;;;;EAIMoC,IAAIA,CACR/D,IAAY;;MAWZ,MAAMgB,KAAK,GAAG,IAAI,CAACC,aAAa,CAACjB,IAAI,CAAC;MAEtC,IAAI;QACF,MAAMoB,IAAI,GAAG,MAAM5C,GAAG,CAAC,IAAI,CAACqB,KAAK,EAAE,GAAG,IAAI,CAACH,GAAG,gBAAgBsB,KAAK,EAAE,EAAE;UACrErB,OAAO,EAAE,IAAI,CAACA;SACf,CAAC;QAEF,OAAO;UAAEyB,IAAI,EAAExC,gBAAgB,CAACwC,IAAI,CAA2B;UAAEO,KAAK,EAAE;QAAI,CAAE;OAC/E,CAAC,OAAOA,KAAK,EAAE;QACd,IAAItD,cAAc,CAACsD,KAAK,CAAC,EAAE;UACzB,OAAO;YAAEP,IAAI,EAAE,IAAI;YAAEO;UAAK,CAAE;;QAG9B,MAAMA,KAAK;;IAEf,CAAC;;EAED;;;;EAIMqC,MAAMA,CACVhE,IAAY;;MAWZ,MAAMgB,KAAK,GAAG,IAAI,CAACC,aAAa,CAACjB,IAAI,CAAC;MAEtC,IAAI;QACF,MAAMvB,IAAI,CAAC,IAAI,CAACoB,KAAK,EAAE,GAAG,IAAI,CAACH,GAAG,WAAWsB,KAAK,EAAE,EAAE;UACpDrB,OAAO,EAAE,IAAI,CAACA;SACf,CAAC;QAEF,OAAO;UAAEyB,IAAI,EAAE,IAAI;UAAEO,KAAK,EAAE;QAAI,CAAE;OACnC,CAAC,OAAOA,KAAK,EAAE;QACd,IAAItD,cAAc,CAACsD,KAAK,CAAC,IAAIA,KAAK,YAAYpD,mBAAmB,EAAE;UACjE,MAAM0F,aAAa,GAAItC,KAAK,CAACsC,aAA+C;UAE5E,IAAI,CAAC,GAAG,EAAE,GAAG,CAAC,CAACC,QAAQ,CAACD,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAEE,MAAM,CAAC,EAAE;YAC9C,OAAO;cAAE/C,IAAI,EAAE,KAAK;cAAEO;YAAK,CAAE;;;QAIjC,MAAMA,KAAK;;IAEf,CAAC;;EAED;;;;;;;;EAQAyC,YAAYA,CACVpE,IAAY,EACZI,OAAuE;IAEvE,MAAMY,KAAK,GAAG,IAAI,CAACC,aAAa,CAACjB,IAAI,CAAC;IACtC,MAAMqE,YAAY,GAAG,EAAE;IAEvB,MAAMrB,kBAAkB,GAAG,CAAA5C,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAE6C,QAAQ,IACxC,YAAY7C,OAAO,CAAC6C,QAAQ,KAAK,IAAI,GAAG,EAAE,GAAG7C,OAAO,CAAC6C,QAAQ,EAAE,GAC/D,EAAE;IAEN,IAAID,kBAAkB,KAAK,EAAE,EAAE;MAC7BqB,YAAY,CAACC,IAAI,CAACtB,kBAAkB,CAAC;;IAGvC,MAAMQ,mBAAmB,GAAG,QAAOpD,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAE2C,SAAS,MAAK,WAAW;IACrE,MAAMU,UAAU,GAAGD,mBAAmB,GAAG,cAAc,GAAG,QAAQ;IAClE,MAAME,mBAAmB,GAAG,IAAI,CAACC,0BAA0B,CAAC,CAAAvD,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAE2C,SAAS,KAAI,EAAE,CAAC;IAErF,IAAIW,mBAAmB,KAAK,EAAE,EAAE;MAC9BW,YAAY,CAACC,IAAI,CAACZ,mBAAmB,CAAC;;IAGxC,IAAIE,WAAW,GAAGS,YAAY,CAACE,IAAI,CAAC,GAAG,CAAC;IACxC,IAAIX,WAAW,KAAK,EAAE,EAAE;MACtBA,WAAW,GAAG,IAAIA,WAAW,EAAE;;IAGjC,OAAO;MACLxC,IAAI,EAAE;QAAEoD,SAAS,EAAEtB,SAAS,CAAC,GAAG,IAAI,CAACxD,GAAG,IAAI+D,UAAU,WAAWzC,KAAK,GAAG4C,WAAW,EAAE;MAAC;KACxF;EACH;EAEA;;;;;EAKMjF,MAAMA,CACV0E,KAAe;;MAWf,IAAI;QACF,MAAMjC,IAAI,GAAG,MAAMzC,MAAM,CACvB,IAAI,CAACkB,KAAK,EACV,GAAG,IAAI,CAACH,GAAG,WAAW,IAAI,CAACE,QAAQ,EAAE,EACrC;UAAE6E,QAAQ,EAAEpB;QAAK,CAAE,EACnB;UAAE1D,OAAO,EAAE,IAAI,CAACA;QAAO,CAAE,CAC1B;QACD,OAAO;UAAEyB,IAAI;UAAEO,KAAK,EAAE;QAAI,CAAE;OAC7B,CAAC,OAAOA,KAAK,EAAE;QACd,IAAItD,cAAc,CAACsD,KAAK,CAAC,EAAE;UACzB,OAAO;YAAEP,IAAI,EAAE,IAAI;YAAEO;UAAK,CAAE;;QAG9B,MAAMA,KAAK;;IAEf,CAAC;;EAED;;;;EAIA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EAEA;EACA;EACA;EAEA;;;;;EAKA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EAEA;EACA;EACA;EAEA;;;;EAIM+C,IAAIA,CACR1E,IAAa,EACbI,OAAuB,EACvBuE,UAA4B;;MAW5B,IAAI;QACF,MAAMxE,IAAI,GAAAE,MAAA,CAAAC,MAAA,CAAAD,MAAA,CAAAC,MAAA,CAAAD,MAAA,CAAAC,MAAA,KAAQxB,sBAAsB,GAAKsB,OAAO;UAAEwE,MAAM,EAAE5E,IAAI,IAAI;QAAE,EAAE;QAC1E,MAAMoB,IAAI,GAAG,MAAM1C,IAAI,CACrB,IAAI,CAACmB,KAAK,EACV,GAAG,IAAI,CAACH,GAAG,gBAAgB,IAAI,CAACE,QAAQ,EAAE,EAC1CO,IAAI,EACJ;UAAER,OAAO,EAAE,IAAI,CAACA;QAAO,CAAE,EACzBgF,UAAU,CACX;QACD,OAAO;UAAEvD,IAAI;UAAEO,KAAK,EAAE;QAAI,CAAE;OAC7B,CAAC,OAAOA,KAAK,EAAE;QACd,IAAItD,cAAc,CAACsD,KAAK,CAAC,EAAE;UACzB,OAAO;YAAEP,IAAI,EAAE,IAAI;YAAEO;UAAK,CAAE;;QAG9B,MAAMA,KAAK;;IAEf,CAAC;;EAESf,cAAcA,CAACJ,QAA6B;IACpD,OAAOqE,IAAI,CAACC,SAAS,CAACtE,QAAQ,CAAC;EACjC;EAEAK,QAAQA,CAACO,IAAY;IACnB,IAAI,OAAO2D,MAAM,KAAK,WAAW,EAAE;MACjC,OAAOA,MAAM,CAACC,IAAI,CAAC5D,IAAI,CAAC,CAACc,QAAQ,CAAC,QAAQ,CAAC;;IAE7C,OAAO+C,IAAI,CAAC7D,IAAI,CAAC;EACnB;EAEQH,aAAaA,CAACjB,IAAY;IAChC,OAAO,GAAG,IAAI,CAACJ,QAAQ,IAAII,IAAI,EAAE;EACnC;EAEQe,mBAAmBA,CAACf,IAAY;IACtC,OAAOA,IAAI,CAACkF,OAAO,CAAC,UAAU,EAAE,EAAE,CAAC,CAACA,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC;EAC1D;EAEQvB,0BAA0BA,CAACZ,SAA2B;IAC5D,MAAMoC,MAAM,GAAG,EAAE;IACjB,IAAIpC,SAAS,CAACqC,KAAK,EAAE;MACnBD,MAAM,CAACb,IAAI,CAAC,SAASvB,SAAS,CAACqC,KAAK,EAAE,CAAC;;IAGzC,IAAIrC,SAAS,CAACsC,MAAM,EAAE;MACpBF,MAAM,CAACb,IAAI,CAAC,UAAUvB,SAAS,CAACsC,MAAM,EAAE,CAAC;;IAG3C,IAAItC,SAAS,CAACuC,MAAM,EAAE;MACpBH,MAAM,CAACb,IAAI,CAAC,UAAUvB,SAAS,CAACuC,MAAM,EAAE,CAAC;;IAG3C,IAAIvC,SAAS,CAACwC,MAAM,EAAE;MACpBJ,MAAM,CAACb,IAAI,CAAC,UAAUvB,SAAS,CAACwC,MAAM,EAAE,CAAC;;IAG3C,IAAIxC,SAAS,CAACyC,OAAO,EAAE;MACrBL,MAAM,CAACb,IAAI,CAAC,WAAWvB,SAAS,CAACyC,OAAO,EAAE,CAAC;;IAG7C,OAAOL,MAAM,CAACZ,IAAI,CAAC,GAAG,CAAC;EACzB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}