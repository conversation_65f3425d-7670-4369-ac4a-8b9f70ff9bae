{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\lostandfound\\\\discfinder-app\\\\src\\\\components\\\\ReturnStatusManager.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { discService } from '../lib/supabase';\nimport './ReturnStatusManager.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport const ReturnStatusManager = ({\n  discId,\n  currentStatus,\n  onStatusUpdated,\n  disabled = false\n}) => {\n  _s();\n  const [isOpen, setIsOpen] = useState(false);\n  const [selectedStatus, setSelectedStatus] = useState(currentStatus);\n  const [notes, setNotes] = useState('');\n  const [isUpdating, setIsUpdating] = useState(false);\n  const [error, setError] = useState('');\n  const statusOptions = ['Found', 'Returned to Owner', 'Donated', 'Sold', 'Trashed'];\n  const getStatusColor = status => {\n    switch (status) {\n      case 'Found':\n        return '#10b981';\n      // green\n      case 'Returned to Owner':\n        return '#3b82f6';\n      // blue\n      case 'Donated':\n        return '#8b5cf6';\n      // purple\n      case 'Sold':\n        return '#f59e0b';\n      // amber\n      case 'Trashed':\n        return '#ef4444';\n      // red\n      default:\n        return '#6b7280';\n      // gray\n    }\n  };\n  const handleSubmit = async e => {\n    e.preventDefault();\n    setIsUpdating(true);\n    setError('');\n    try {\n      const {\n        success,\n        error: updateError\n      } = await discService.updateReturnStatus(discId, selectedStatus, notes.trim() || undefined);\n      if (!success) {\n        setError((updateError === null || updateError === void 0 ? void 0 : updateError.message) || 'Failed to update status');\n        return;\n      }\n      onStatusUpdated(selectedStatus);\n      setIsOpen(false);\n      setNotes('');\n    } catch (err) {\n      setError('An unexpected error occurred');\n    } finally {\n      setIsUpdating(false);\n    }\n  };\n  const handleCancel = () => {\n    setSelectedStatus(currentStatus);\n    setNotes('');\n    setError('');\n    setIsOpen(false);\n  };\n  if (!isOpen) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"return-status-display\",\n      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"status-badge\",\n        style: {\n          backgroundColor: getStatusColor(currentStatus)\n        },\n        children: currentStatus\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 80,\n        columnNumber: 9\n      }, this), !disabled && /*#__PURE__*/_jsxDEV(\"button\", {\n        className: \"edit-status-btn\",\n        onClick: () => setIsOpen(true),\n        title: \"Update return status\",\n        children: \"\\u270F\\uFE0F\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 87,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 79,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"return-status-manager\",\n    children: /*#__PURE__*/_jsxDEV(\"form\", {\n      onSubmit: handleSubmit,\n      className: \"status-form\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"form-header\",\n        children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n          children: \"Update Return Status\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 103,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          type: \"button\",\n          className: \"close-btn\",\n          onClick: handleCancel,\n          children: \"\\xD7\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 104,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 102,\n        columnNumber: 9\n      }, this), error && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"error-message\",\n        children: error\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 114,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"form-group\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          htmlFor: \"status-select\",\n          children: \"Return Status:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 120,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n          id: \"status-select\",\n          value: selectedStatus,\n          onChange: e => setSelectedStatus(e.target.value),\n          disabled: isUpdating,\n          required: true,\n          children: statusOptions.map(status => /*#__PURE__*/_jsxDEV(\"option\", {\n            value: status,\n            children: status\n          }, status, false, {\n            fileName: _jsxFileName,\n            lineNumber: 129,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 121,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 119,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"form-group\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          htmlFor: \"notes-input\",\n          children: \"Notes (optional):\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 137,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n          id: \"notes-input\",\n          value: notes,\n          onChange: e => setNotes(e.target.value),\n          placeholder: \"Add any notes about the return...\",\n          rows: 3,\n          disabled: isUpdating\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 138,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 136,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"form-actions\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          type: \"button\",\n          className: \"btn-cancel\",\n          onClick: handleCancel,\n          disabled: isUpdating,\n          children: \"Cancel\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 149,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          type: \"submit\",\n          className: \"btn-save\",\n          disabled: isUpdating,\n          children: isUpdating ? 'Updating...' : 'Update Status'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 157,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 148,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 101,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 100,\n    columnNumber: 5\n  }, this);\n};\n_s(ReturnStatusManager, \"yA5sIHZErLKrVhVGb/Iyc9nOjlY=\");\n_c = ReturnStatusManager;\nvar _c;\n$RefreshReg$(_c, \"ReturnStatusManager\");", "map": {"version": 3, "names": ["React", "useState", "discService", "jsxDEV", "_jsxDEV", "ReturnStatusManager", "discId", "currentStatus", "onStatusUpdated", "disabled", "_s", "isOpen", "setIsOpen", "selectedStatus", "setSelectedStatus", "notes", "setNotes", "isUpdating", "setIsUpdating", "error", "setError", "statusOptions", "getStatusColor", "status", "handleSubmit", "e", "preventDefault", "success", "updateError", "updateReturnStatus", "trim", "undefined", "message", "err", "handleCancel", "className", "children", "style", "backgroundColor", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "title", "onSubmit", "type", "htmlFor", "id", "value", "onChange", "target", "required", "map", "placeholder", "rows", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/lostandfound/discfinder-app/src/components/ReturnStatusManager.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { ReturnStatus, discService } from '../lib/supabase';\nimport './ReturnStatusManager.css';\n\ninterface ReturnStatusManagerProps {\n  discId: string;\n  currentStatus: ReturnStatus;\n  onStatusUpdated: (newStatus: ReturnStatus) => void;\n  disabled?: boolean;\n}\n\nexport const ReturnStatusManager: React.FC<ReturnStatusManagerProps> = ({\n  discId,\n  currentStatus,\n  onStatusUpdated,\n  disabled = false\n}) => {\n  const [isOpen, setIsOpen] = useState(false);\n  const [selectedStatus, setSelectedStatus] = useState<ReturnStatus>(currentStatus);\n  const [notes, setNotes] = useState('');\n  const [isUpdating, setIsUpdating] = useState(false);\n  const [error, setError] = useState('');\n\n  const statusOptions: ReturnStatus[] = [\n    'Found',\n    'Returned to Owner',\n    'Donated',\n    'Sold',\n    'Trashed'\n  ];\n\n  const getStatusColor = (status: ReturnStatus): string => {\n    switch (status) {\n      case 'Found': return '#10b981'; // green\n      case 'Returned to Owner': return '#3b82f6'; // blue\n      case 'Donated': return '#8b5cf6'; // purple\n      case 'Sold': return '#f59e0b'; // amber\n      case 'Trashed': return '#ef4444'; // red\n      default: return '#6b7280'; // gray\n    }\n  };\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n    setIsUpdating(true);\n    setError('');\n\n    try {\n      const { success, error: updateError } = await discService.updateReturnStatus(\n        discId,\n        selectedStatus,\n        notes.trim() || undefined\n      );\n\n      if (!success) {\n        setError(updateError?.message || 'Failed to update status');\n        return;\n      }\n\n      onStatusUpdated(selectedStatus);\n      setIsOpen(false);\n      setNotes('');\n    } catch (err) {\n      setError('An unexpected error occurred');\n    } finally {\n      setIsUpdating(false);\n    }\n  };\n\n  const handleCancel = () => {\n    setSelectedStatus(currentStatus);\n    setNotes('');\n    setError('');\n    setIsOpen(false);\n  };\n\n  if (!isOpen) {\n    return (\n      <div className=\"return-status-display\">\n        <span \n          className=\"status-badge\"\n          style={{ backgroundColor: getStatusColor(currentStatus) }}\n        >\n          {currentStatus}\n        </span>\n        {!disabled && (\n          <button\n            className=\"edit-status-btn\"\n            onClick={() => setIsOpen(true)}\n            title=\"Update return status\"\n          >\n            ✏️\n          </button>\n        )}\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"return-status-manager\">\n      <form onSubmit={handleSubmit} className=\"status-form\">\n        <div className=\"form-header\">\n          <h4>Update Return Status</h4>\n          <button\n            type=\"button\"\n            className=\"close-btn\"\n            onClick={handleCancel}\n          >\n            ×\n          </button>\n        </div>\n\n        {error && (\n          <div className=\"error-message\">\n            {error}\n          </div>\n        )}\n\n        <div className=\"form-group\">\n          <label htmlFor=\"status-select\">Return Status:</label>\n          <select\n            id=\"status-select\"\n            value={selectedStatus}\n            onChange={(e) => setSelectedStatus(e.target.value as ReturnStatus)}\n            disabled={isUpdating}\n            required\n          >\n            {statusOptions.map(status => (\n              <option key={status} value={status}>\n                {status}\n              </option>\n            ))}\n          </select>\n        </div>\n\n        <div className=\"form-group\">\n          <label htmlFor=\"notes-input\">Notes (optional):</label>\n          <textarea\n            id=\"notes-input\"\n            value={notes}\n            onChange={(e) => setNotes(e.target.value)}\n            placeholder=\"Add any notes about the return...\"\n            rows={3}\n            disabled={isUpdating}\n          />\n        </div>\n\n        <div className=\"form-actions\">\n          <button\n            type=\"button\"\n            className=\"btn-cancel\"\n            onClick={handleCancel}\n            disabled={isUpdating}\n          >\n            Cancel\n          </button>\n          <button\n            type=\"submit\"\n            className=\"btn-save\"\n            disabled={isUpdating}\n          >\n            {isUpdating ? 'Updating...' : 'Update Status'}\n          </button>\n        </div>\n      </form>\n    </div>\n  );\n};\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAAuBC,WAAW,QAAQ,iBAAiB;AAC3D,OAAO,2BAA2B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AASnC,OAAO,MAAMC,mBAAuD,GAAGA,CAAC;EACtEC,MAAM;EACNC,aAAa;EACbC,eAAe;EACfC,QAAQ,GAAG;AACb,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAGX,QAAQ,CAAC,KAAK,CAAC;EAC3C,MAAM,CAACY,cAAc,EAAEC,iBAAiB,CAAC,GAAGb,QAAQ,CAAeM,aAAa,CAAC;EACjF,MAAM,CAACQ,KAAK,EAAEC,QAAQ,CAAC,GAAGf,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACgB,UAAU,EAAEC,aAAa,CAAC,GAAGjB,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACkB,KAAK,EAAEC,QAAQ,CAAC,GAAGnB,QAAQ,CAAC,EAAE,CAAC;EAEtC,MAAMoB,aAA6B,GAAG,CACpC,OAAO,EACP,mBAAmB,EACnB,SAAS,EACT,MAAM,EACN,SAAS,CACV;EAED,MAAMC,cAAc,GAAIC,MAAoB,IAAa;IACvD,QAAQA,MAAM;MACZ,KAAK,OAAO;QAAE,OAAO,SAAS;MAAE;MAChC,KAAK,mBAAmB;QAAE,OAAO,SAAS;MAAE;MAC5C,KAAK,SAAS;QAAE,OAAO,SAAS;MAAE;MAClC,KAAK,MAAM;QAAE,OAAO,SAAS;MAAE;MAC/B,KAAK,SAAS;QAAE,OAAO,SAAS;MAAE;MAClC;QAAS,OAAO,SAAS;MAAE;IAC7B;EACF,CAAC;EAED,MAAMC,YAAY,GAAG,MAAOC,CAAkB,IAAK;IACjDA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClBR,aAAa,CAAC,IAAI,CAAC;IACnBE,QAAQ,CAAC,EAAE,CAAC;IAEZ,IAAI;MACF,MAAM;QAAEO,OAAO;QAAER,KAAK,EAAES;MAAY,CAAC,GAAG,MAAM1B,WAAW,CAAC2B,kBAAkB,CAC1EvB,MAAM,EACNO,cAAc,EACdE,KAAK,CAACe,IAAI,CAAC,CAAC,IAAIC,SAClB,CAAC;MAED,IAAI,CAACJ,OAAO,EAAE;QACZP,QAAQ,CAAC,CAAAQ,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEI,OAAO,KAAI,yBAAyB,CAAC;QAC3D;MACF;MAEAxB,eAAe,CAACK,cAAc,CAAC;MAC/BD,SAAS,CAAC,KAAK,CAAC;MAChBI,QAAQ,CAAC,EAAE,CAAC;IACd,CAAC,CAAC,OAAOiB,GAAG,EAAE;MACZb,QAAQ,CAAC,8BAA8B,CAAC;IAC1C,CAAC,SAAS;MACRF,aAAa,CAAC,KAAK,CAAC;IACtB;EACF,CAAC;EAED,MAAMgB,YAAY,GAAGA,CAAA,KAAM;IACzBpB,iBAAiB,CAACP,aAAa,CAAC;IAChCS,QAAQ,CAAC,EAAE,CAAC;IACZI,QAAQ,CAAC,EAAE,CAAC;IACZR,SAAS,CAAC,KAAK,CAAC;EAClB,CAAC;EAED,IAAI,CAACD,MAAM,EAAE;IACX,oBACEP,OAAA;MAAK+B,SAAS,EAAC,uBAAuB;MAAAC,QAAA,gBACpChC,OAAA;QACE+B,SAAS,EAAC,cAAc;QACxBE,KAAK,EAAE;UAAEC,eAAe,EAAEhB,cAAc,CAACf,aAAa;QAAE,CAAE;QAAA6B,QAAA,EAEzD7B;MAAa;QAAAgC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,EACN,CAACjC,QAAQ,iBACRL,OAAA;QACE+B,SAAS,EAAC,iBAAiB;QAC3BQ,OAAO,EAAEA,CAAA,KAAM/B,SAAS,CAAC,IAAI,CAAE;QAC/BgC,KAAK,EAAC,sBAAsB;QAAAR,QAAA,EAC7B;MAED;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CACT;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAEV;EAEA,oBACEtC,OAAA;IAAK+B,SAAS,EAAC,uBAAuB;IAAAC,QAAA,eACpChC,OAAA;MAAMyC,QAAQ,EAAErB,YAAa;MAACW,SAAS,EAAC,aAAa;MAAAC,QAAA,gBACnDhC,OAAA;QAAK+B,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1BhC,OAAA;UAAAgC,QAAA,EAAI;QAAoB;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC7BtC,OAAA;UACE0C,IAAI,EAAC,QAAQ;UACbX,SAAS,EAAC,WAAW;UACrBQ,OAAO,EAAET,YAAa;UAAAE,QAAA,EACvB;QAED;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,EAELvB,KAAK,iBACJf,OAAA;QAAK+B,SAAS,EAAC,eAAe;QAAAC,QAAA,EAC3BjB;MAAK;QAAAoB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,eAEDtC,OAAA;QAAK+B,SAAS,EAAC,YAAY;QAAAC,QAAA,gBACzBhC,OAAA;UAAO2C,OAAO,EAAC,eAAe;UAAAX,QAAA,EAAC;QAAc;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACrDtC,OAAA;UACE4C,EAAE,EAAC,eAAe;UAClBC,KAAK,EAAEpC,cAAe;UACtBqC,QAAQ,EAAGzB,CAAC,IAAKX,iBAAiB,CAACW,CAAC,CAAC0B,MAAM,CAACF,KAAqB,CAAE;UACnExC,QAAQ,EAAEQ,UAAW;UACrBmC,QAAQ;UAAAhB,QAAA,EAEPf,aAAa,CAACgC,GAAG,CAAC9B,MAAM,iBACvBnB,OAAA;YAAqB6C,KAAK,EAAE1B,MAAO;YAAAa,QAAA,EAChCb;UAAM,GADIA,MAAM;YAAAgB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAEX,CACT;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eAENtC,OAAA;QAAK+B,SAAS,EAAC,YAAY;QAAAC,QAAA,gBACzBhC,OAAA;UAAO2C,OAAO,EAAC,aAAa;UAAAX,QAAA,EAAC;QAAiB;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACtDtC,OAAA;UACE4C,EAAE,EAAC,aAAa;UAChBC,KAAK,EAAElC,KAAM;UACbmC,QAAQ,EAAGzB,CAAC,IAAKT,QAAQ,CAACS,CAAC,CAAC0B,MAAM,CAACF,KAAK,CAAE;UAC1CK,WAAW,EAAC,mCAAmC;UAC/CC,IAAI,EAAE,CAAE;UACR9C,QAAQ,EAAEQ;QAAW;UAAAsB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAENtC,OAAA;QAAK+B,SAAS,EAAC,cAAc;QAAAC,QAAA,gBAC3BhC,OAAA;UACE0C,IAAI,EAAC,QAAQ;UACbX,SAAS,EAAC,YAAY;UACtBQ,OAAO,EAAET,YAAa;UACtBzB,QAAQ,EAAEQ,UAAW;UAAAmB,QAAA,EACtB;QAED;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTtC,OAAA;UACE0C,IAAI,EAAC,QAAQ;UACbX,SAAS,EAAC,UAAU;UACpB1B,QAAQ,EAAEQ,UAAW;UAAAmB,QAAA,EAEpBnB,UAAU,GAAG,aAAa,GAAG;QAAe;UAAAsB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEV,CAAC;AAAChC,EAAA,CA5JWL,mBAAuD;AAAAmD,EAAA,GAAvDnD,mBAAuD;AAAA,IAAAmD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}