{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\lostandfound\\\\discfinder-app\\\\src\\\\contexts\\\\AuthContext.tsx\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { createContext, useContext, useEffect, useState } from 'react';\nimport { supabase } from '../lib/supabase';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AuthContext = /*#__PURE__*/createContext(undefined);\nexport const useAuth = () => {\n  _s();\n  const context = useContext(AuthContext);\n  if (context === undefined) {\n    throw new Error('useAuth must be used within an AuthProvider');\n  }\n  return context;\n};\n_s(useAuth, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nexport const AuthProvider = ({\n  children\n}) => {\n  _s2();\n  const [user, setUser] = useState(null);\n  const [profile, setProfile] = useState(null);\n  const [session, setSession] = useState(null);\n  const [userRole, setUserRole] = useState('guest');\n  const [loading, setLoading] = useState(true);\n  useEffect(() => {\n    // Get initial session with timeout\n    const initAuth = async () => {\n      try {\n        var _session$user;\n        console.log('Initializing auth...');\n        const {\n          data: {\n            session\n          },\n          error\n        } = await supabase.auth.getSession();\n        if (error) {\n          console.warn('Auth session error:', error);\n          setUserRole('guest');\n          setLoading(false);\n          return;\n        }\n        setSession(session);\n        setUser((_session$user = session === null || session === void 0 ? void 0 : session.user) !== null && _session$user !== void 0 ? _session$user : null);\n        if (session !== null && session !== void 0 && session.user) {\n          console.log('User found, fetching profile...');\n          await fetchProfile(session.user.id);\n        } else {\n          console.log('No user session, setting as guest');\n          setUserRole('guest');\n          setLoading(false);\n        }\n      } catch (error) {\n        console.warn('Auth initialization failed:', error);\n        setUserRole('guest');\n        setLoading(false);\n      }\n    };\n\n    // Set a timeout to prevent infinite loading\n    const timeoutId = setTimeout(() => {\n      console.warn('Auth initialization timeout, setting as guest');\n      setUserRole('guest');\n      setLoading(false);\n    }, 5000); // 5 second timeout\n\n    initAuth().finally(() => {\n      clearTimeout(timeoutId);\n    });\n\n    // Listen for auth changes\n    const {\n      data: {\n        subscription\n      }\n    } = supabase.auth.onAuthStateChange(async (event, session) => {\n      var _session$user2;\n      setSession(session);\n      setUser((_session$user2 = session === null || session === void 0 ? void 0 : session.user) !== null && _session$user2 !== void 0 ? _session$user2 : null);\n      if (session !== null && session !== void 0 && session.user) {\n        await fetchProfile(session.user.id);\n      } else {\n        setProfile(null);\n        setUserRole('guest');\n        setLoading(false);\n      }\n    });\n    return () => subscription.unsubscribe();\n  }, []);\n  const fetchProfile = async userId => {\n    try {\n      console.log('Fetching profile for user:', userId);\n\n      // Add timeout for profile fetch\n      const profilePromise = supabase.from('profiles').select('*').eq('id', userId).single();\n      const timeoutPromise = new Promise((_, reject) => setTimeout(() => reject(new Error('Profile fetch timeout')), 3000));\n      const {\n        data,\n        error\n      } = await Promise.race([profilePromise, timeoutPromise]);\n      if (error) {\n        console.warn('Profile fetch error:', error);\n\n        // If profile doesn't exist, check for imported profile or create new one\n        if (error.code === 'PGRST116') {\n          console.log('Profile not found, checking for imported profile...');\n          try {\n            const {\n              data: userData\n            } = await supabase.auth.getUser();\n            if (userData.user) {\n              var _userData$user$user_m2;\n              // First, check if there's an imported profile in staging with this email\n              const {\n                data: importedProfile,\n                error: importError\n              } = await supabase.from('imported_profiles_staging').select('*').eq('email', userData.user.email).single();\n              if (importedProfile && !importError) {\n                var _userData$user$user_m;\n                console.log('Found imported profile, creating linked profile...');\n                // Create profile using imported data\n                const newProfile = {\n                  id: userData.user.id,\n                  email: userData.user.email,\n                  full_name: ((_userData$user$user_m = userData.user.user_metadata) === null || _userData$user$user_m === void 0 ? void 0 : _userData$user$user_m.full_name) || importedProfile.full_name,\n                  role: importedProfile.role,\n                  legacy_row_id: importedProfile.legacy_row_id,\n                  pdga_number: importedProfile.pdga_number,\n                  facebook_profile: importedProfile.facebook_profile,\n                  instagram_handle: importedProfile.instagram_handle,\n                  sms_number: importedProfile.sms_number,\n                  phone_number: importedProfile.phone_number,\n                  avatar_url: importedProfile.avatar_url,\n                  created_at: new Date().toISOString(),\n                  updated_at: new Date().toISOString()\n                };\n                const {\n                  data: createdProfile,\n                  error: createError\n                } = await supabase.from('profiles').insert([newProfile]).select().single();\n                if (createError) {\n                  console.error('Error creating linked profile:', createError);\n                } else {\n                  console.log('Successfully created linked profile');\n\n                  // Remove from staging since it's now linked\n                  await supabase.from('imported_profiles_staging').delete().eq('id', importedProfile.id);\n                  setProfile(createdProfile);\n                  setUserRole(createdProfile.role || 'user');\n                  setLoading(false);\n                  return;\n                }\n              }\n\n              // No imported profile found, create a new one\n              console.log('No imported profile found, creating new profile...');\n              const newProfile = {\n                id: userData.user.id,\n                email: userData.user.email,\n                full_name: ((_userData$user$user_m2 = userData.user.user_metadata) === null || _userData$user$user_m2 === void 0 ? void 0 : _userData$user$user_m2.full_name) || '',\n                role: userData.user.email === '<EMAIL>' ? 'admin' : 'user',\n                created_at: new Date().toISOString(),\n                updated_at: new Date().toISOString()\n              };\n              const {\n                data: createdProfile,\n                error: createError\n              } = await supabase.from('profiles').insert([newProfile]).select().single();\n              if (createError) {\n                console.error('Error creating new profile:', createError);\n              } else {\n                console.log('Profile created successfully');\n                setProfile(createdProfile);\n                setUserRole(createdProfile.role || 'user');\n                setLoading(false);\n                return;\n              }\n            }\n          } catch (createError) {\n            console.warn('Failed to create profile:', createError);\n          }\n        }\n\n        // If we can't fetch/create profile, continue as authenticated user without profile\n        console.log('Continuing without profile data');\n        setUserRole('user');\n        setLoading(false);\n        return;\n      }\n      console.log('Profile fetched successfully:', data);\n      setProfile(data);\n      setUserRole(data.role || 'user');\n      setLoading(false);\n    } catch (error) {\n      console.error('Error in fetchProfile:', error);\n      // Don't fail completely, just continue as authenticated user\n      setUserRole('user');\n      setLoading(false);\n    }\n  };\n  const signUp = async (email, password, fullName) => {\n    try {\n      const {\n        data,\n        error\n      } = await supabase.auth.signUp({\n        email,\n        password,\n        options: {\n          data: {\n            full_name: fullName\n          }\n        }\n      });\n\n      // If user creation succeeded, check for imported profile or create new one\n      if (data.user && !error) {\n        try {\n          // Look for an existing imported profile with this email\n          const {\n            data: importedProfile,\n            error: importError\n          } = await supabase.from('profiles').select('*').eq('email', data.user.email).single();\n          if (importedProfile && !importError) {\n            console.log('Found imported profile during signup, linking...');\n            // Update the imported profile to use the new auth user ID\n            await supabase.from('profiles').update({\n              id: data.user.id,\n              full_name: fullName || importedProfile.full_name,\n              updated_at: new Date().toISOString()\n            }).eq('email', data.user.email);\n          } else {\n            // No imported profile, create new one\n            await supabase.from('profiles').insert([{\n              id: data.user.id,\n              email: data.user.email,\n              full_name: fullName,\n              role: 'user',\n              created_at: new Date().toISOString(),\n              updated_at: new Date().toISOString()\n            }]);\n          }\n        } catch (profileError) {\n          console.warn('Profile creation/linking failed, but user was created:', profileError);\n          // Don't fail the signup if profile creation fails\n        }\n      }\n      return {\n        data,\n        error\n      };\n    } catch (error) {\n      return {\n        data: null,\n        error: {\n          message: 'Supabase not configured. This is a demo.'\n        }\n      };\n    }\n  };\n  const signIn = async (email, password) => {\n    try {\n      const {\n        data,\n        error\n      } = await supabase.auth.signInWithPassword({\n        email,\n        password\n      });\n      return {\n        data,\n        error\n      };\n    } catch (error) {\n      return {\n        data: null,\n        error: {\n          message: 'Supabase not configured. This is a demo.'\n        }\n      };\n    }\n  };\n  const signOut = async () => {\n    try {\n      const {\n        error\n      } = await supabase.auth.signOut();\n      if (error) {\n        console.error('Error signing out:', error);\n      }\n    } catch (error) {\n      console.log('Supabase not configured, running in demo mode');\n    }\n  };\n  const updateProfile = async updates => {\n    if (!user) return;\n    try {\n      const {\n        error\n      } = await supabase.from('profiles').update(updates).eq('id', user.id);\n      if (error) {\n        throw error;\n      }\n\n      // Refresh profile\n      await fetchProfile(user.id);\n    } catch (error) {\n      console.error('Error updating profile:', error);\n      throw error;\n    }\n  };\n  const value = {\n    user,\n    profile,\n    session,\n    userRole,\n    loading,\n    signUp,\n    signIn,\n    signOut,\n    updateProfile,\n    isGuest: userRole === 'guest',\n    isUser: userRole === 'user',\n    isAdmin: userRole === 'admin',\n    isRakerDiver: userRole === 'rakerdiver'\n  };\n  return /*#__PURE__*/_jsxDEV(AuthContext.Provider, {\n    value: value,\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 349,\n    columnNumber: 5\n  }, this);\n};\n_s2(AuthProvider, \"clRRS62CUjhjIMIlA9YAjluDkG8=\");\n_c = AuthProvider;\nvar _c;\n$RefreshReg$(_c, \"AuthProvider\");", "map": {"version": 3, "names": ["React", "createContext", "useContext", "useEffect", "useState", "supabase", "jsxDEV", "_jsxDEV", "AuthContext", "undefined", "useAuth", "_s", "context", "Error", "<PERSON>th<PERSON><PERSON><PERSON>", "children", "_s2", "user", "setUser", "profile", "setProfile", "session", "setSession", "userRole", "setUserRole", "loading", "setLoading", "initAuth", "_session$user", "console", "log", "data", "error", "auth", "getSession", "warn", "fetchProfile", "id", "timeoutId", "setTimeout", "finally", "clearTimeout", "subscription", "onAuthStateChange", "event", "_session$user2", "unsubscribe", "userId", "profilePromise", "from", "select", "eq", "single", "timeoutPromise", "Promise", "_", "reject", "race", "code", "userData", "getUser", "_userData$user$user_m2", "importedProfile", "importError", "email", "_userData$user$user_m", "newProfile", "full_name", "user_metadata", "role", "legacy_row_id", "pdga_number", "facebook_profile", "instagram_handle", "sms_number", "phone_number", "avatar_url", "created_at", "Date", "toISOString", "updated_at", "createdProfile", "createError", "insert", "delete", "signUp", "password", "fullName", "options", "update", "profileError", "message", "signIn", "signInWithPassword", "signOut", "updateProfile", "updates", "value", "isGuest", "isUser", "isAdmin", "isRakerDiver", "Provider", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/lostandfound/discfinder-app/src/contexts/AuthContext.tsx"], "sourcesContent": ["import React, { createContext, useContext, useEffect, useState } from 'react';\nimport { User, Session } from '@supabase/supabase-js';\nimport { supabase, Profile } from '../lib/supabase';\n\nexport type UserRole = 'guest' | 'user' | 'admin' | 'rakerdiver';\n\ninterface AuthContextType {\n  user: User | null;\n  profile: Profile | null;\n  session: Session | null;\n  userRole: UserRole;\n  loading: boolean;\n  signUp: (email: string, password: string, fullName: string) => Promise<any>;\n  signIn: (email: string, password: string) => Promise<any>;\n  signOut: () => Promise<void>;\n  updateProfile: (updates: Partial<Profile>) => Promise<void>;\n  isGuest: boolean;\n  isUser: boolean;\n  isAdmin: boolean;\n  isRakerDiver: boolean;\n}\n\nconst AuthContext = createContext<AuthContextType | undefined>(undefined);\n\nexport const useAuth = () => {\n  const context = useContext(AuthContext);\n  if (context === undefined) {\n    throw new Error('useAuth must be used within an AuthProvider');\n  }\n  return context;\n};\n\nexport const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {\n  const [user, setUser] = useState<User | null>(null);\n  const [profile, setProfile] = useState<Profile | null>(null);\n  const [session, setSession] = useState<Session | null>(null);\n  const [userRole, setUserRole] = useState<UserRole>('guest');\n  const [loading, setLoading] = useState(true);\n\n  useEffect(() => {\n    // Get initial session with timeout\n    const initAuth = async () => {\n      try {\n        console.log('Initializing auth...');\n        const { data: { session }, error } = await supabase.auth.getSession();\n\n        if (error) {\n          console.warn('Auth session error:', error);\n          setUserRole('guest');\n          setLoading(false);\n          return;\n        }\n\n        setSession(session);\n        setUser(session?.user ?? null);\n\n        if (session?.user) {\n          console.log('User found, fetching profile...');\n          await fetchProfile(session.user.id);\n        } else {\n          console.log('No user session, setting as guest');\n          setUserRole('guest');\n          setLoading(false);\n        }\n      } catch (error) {\n        console.warn('Auth initialization failed:', error);\n        setUserRole('guest');\n        setLoading(false);\n      }\n    };\n\n    // Set a timeout to prevent infinite loading\n    const timeoutId = setTimeout(() => {\n      console.warn('Auth initialization timeout, setting as guest');\n      setUserRole('guest');\n      setLoading(false);\n    }, 5000); // 5 second timeout\n\n    initAuth().finally(() => {\n      clearTimeout(timeoutId);\n    });\n\n    // Listen for auth changes\n    const {\n      data: { subscription },\n    } = supabase.auth.onAuthStateChange(async (event, session) => {\n      setSession(session);\n      setUser(session?.user ?? null);\n      \n      if (session?.user) {\n        await fetchProfile(session.user.id);\n      } else {\n        setProfile(null);\n        setUserRole('guest');\n        setLoading(false);\n      }\n    });\n\n    return () => subscription.unsubscribe();\n  }, []);\n\n  const fetchProfile = async (userId: string) => {\n    try {\n      console.log('Fetching profile for user:', userId);\n\n      // Add timeout for profile fetch\n      const profilePromise = supabase\n        .from('profiles')\n        .select('*')\n        .eq('id', userId)\n        .single();\n\n      const timeoutPromise = new Promise((_, reject) =>\n        setTimeout(() => reject(new Error('Profile fetch timeout')), 3000)\n      );\n\n      const { data, error } = await Promise.race([profilePromise, timeoutPromise]) as any;\n\n      if (error) {\n        console.warn('Profile fetch error:', error);\n\n        // If profile doesn't exist, check for imported profile or create new one\n        if (error.code === 'PGRST116') {\n          console.log('Profile not found, checking for imported profile...');\n          try {\n            const { data: userData } = await supabase.auth.getUser();\n            if (userData.user) {\n              // First, check if there's an imported profile in staging with this email\n              const { data: importedProfile, error: importError } = await supabase\n                .from('imported_profiles_staging')\n                .select('*')\n                .eq('email', userData.user.email!)\n                .single();\n\n              if (importedProfile && !importError) {\n                console.log('Found imported profile, creating linked profile...');\n                // Create profile using imported data\n                const newProfile = {\n                  id: userData.user.id,\n                  email: userData.user.email!,\n                  full_name: userData.user.user_metadata?.full_name || importedProfile.full_name,\n                  role: importedProfile.role as UserRole,\n                  legacy_row_id: importedProfile.legacy_row_id,\n                  pdga_number: importedProfile.pdga_number,\n                  facebook_profile: importedProfile.facebook_profile,\n                  instagram_handle: importedProfile.instagram_handle,\n                  sms_number: importedProfile.sms_number,\n                  phone_number: importedProfile.phone_number,\n                  avatar_url: importedProfile.avatar_url,\n                  created_at: new Date().toISOString(),\n                  updated_at: new Date().toISOString()\n                };\n\n                const { data: createdProfile, error: createError } = await supabase\n                  .from('profiles')\n                  .insert([newProfile])\n                  .select()\n                  .single();\n\n                if (createError) {\n                  console.error('Error creating linked profile:', createError);\n                } else {\n                  console.log('Successfully created linked profile');\n\n                  // Remove from staging since it's now linked\n                  await supabase\n                    .from('imported_profiles_staging')\n                    .delete()\n                    .eq('id', importedProfile.id);\n\n                  setProfile(createdProfile);\n                  setUserRole(createdProfile.role as UserRole || 'user');\n                  setLoading(false);\n                  return;\n                }\n              }\n\n              // No imported profile found, create a new one\n              console.log('No imported profile found, creating new profile...');\n              const newProfile = {\n                id: userData.user.id,\n                email: userData.user.email!,\n                full_name: userData.user.user_metadata?.full_name || '',\n                role: userData.user.email === '<EMAIL>' ? 'admin' as UserRole : 'user' as UserRole,\n                created_at: new Date().toISOString(),\n                updated_at: new Date().toISOString()\n              };\n\n              const { data: createdProfile, error: createError } = await supabase\n                .from('profiles')\n                .insert([newProfile])\n                .select()\n                .single();\n\n              if (createError) {\n                console.error('Error creating new profile:', createError);\n              } else {\n                console.log('Profile created successfully');\n                setProfile(createdProfile);\n                setUserRole(createdProfile.role as UserRole || 'user');\n                setLoading(false);\n                return;\n              }\n            }\n          } catch (createError) {\n            console.warn('Failed to create profile:', createError);\n          }\n        }\n\n        // If we can't fetch/create profile, continue as authenticated user without profile\n        console.log('Continuing without profile data');\n        setUserRole('user');\n        setLoading(false);\n        return;\n      }\n\n      console.log('Profile fetched successfully:', data);\n      setProfile(data);\n      setUserRole(data.role || 'user');\n      setLoading(false);\n    } catch (error) {\n      console.error('Error in fetchProfile:', error);\n      // Don't fail completely, just continue as authenticated user\n      setUserRole('user');\n      setLoading(false);\n    }\n  };\n\n  const signUp = async (email: string, password: string, fullName: string) => {\n    try {\n      const { data, error } = await supabase.auth.signUp({\n        email,\n        password,\n        options: {\n          data: {\n            full_name: fullName,\n          },\n        },\n      });\n\n      // If user creation succeeded, check for imported profile or create new one\n      if (data.user && !error) {\n        try {\n          // Look for an existing imported profile with this email\n          const { data: importedProfile, error: importError } = await supabase\n            .from('profiles')\n            .select('*')\n            .eq('email', data.user.email!)\n            .single();\n\n          if (importedProfile && !importError) {\n            console.log('Found imported profile during signup, linking...');\n            // Update the imported profile to use the new auth user ID\n            await supabase\n              .from('profiles')\n              .update({\n                id: data.user.id,\n                full_name: fullName || importedProfile.full_name,\n                updated_at: new Date().toISOString()\n              })\n              .eq('email', data.user.email!);\n          } else {\n            // No imported profile, create new one\n            await supabase\n              .from('profiles')\n              .insert([{\n                id: data.user.id,\n                email: data.user.email!,\n                full_name: fullName,\n                role: 'user',\n                created_at: new Date().toISOString(),\n                updated_at: new Date().toISOString()\n              }]);\n          }\n        } catch (profileError) {\n          console.warn('Profile creation/linking failed, but user was created:', profileError);\n          // Don't fail the signup if profile creation fails\n        }\n      }\n\n      return { data, error };\n    } catch (error) {\n      return { data: null, error: { message: 'Supabase not configured. This is a demo.' } };\n    }\n  };\n\n  const signIn = async (email: string, password: string) => {\n    try {\n      const { data, error } = await supabase.auth.signInWithPassword({\n        email,\n        password,\n      });\n\n      return { data, error };\n    } catch (error) {\n      return { data: null, error: { message: 'Supabase not configured. This is a demo.' } };\n    }\n  };\n\n  const signOut = async () => {\n    try {\n      const { error } = await supabase.auth.signOut();\n      if (error) {\n        console.error('Error signing out:', error);\n      }\n    } catch (error) {\n      console.log('Supabase not configured, running in demo mode');\n    }\n  };\n\n  const updateProfile = async (updates: Partial<Profile>) => {\n    if (!user) return;\n\n    try {\n      const { error } = await supabase\n        .from('profiles')\n        .update(updates)\n        .eq('id', user.id);\n\n      if (error) {\n        throw error;\n      }\n\n      // Refresh profile\n      await fetchProfile(user.id);\n    } catch (error) {\n      console.error('Error updating profile:', error);\n      throw error;\n    }\n  };\n\n  const value = {\n    user,\n    profile,\n    session,\n    userRole,\n    loading,\n    signUp,\n    signIn,\n    signOut,\n    updateProfile,\n    isGuest: userRole === 'guest',\n    isUser: userRole === 'user',\n    isAdmin: userRole === 'admin',\n    isRakerDiver: userRole === 'rakerdiver',\n  };\n\n  return (\n    <AuthContext.Provider value={value}>\n      {children}\n    </AuthContext.Provider>\n  );\n};\n"], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,aAAa,EAAEC,UAAU,EAAEC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAE7E,SAASC,QAAQ,QAAiB,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAoBpD,MAAMC,WAAW,gBAAGP,aAAa,CAA8BQ,SAAS,CAAC;AAEzE,OAAO,MAAMC,OAAO,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC3B,MAAMC,OAAO,GAAGV,UAAU,CAACM,WAAW,CAAC;EACvC,IAAII,OAAO,KAAKH,SAAS,EAAE;IACzB,MAAM,IAAII,KAAK,CAAC,6CAA6C,CAAC;EAChE;EACA,OAAOD,OAAO;AAChB,CAAC;AAACD,EAAA,CANWD,OAAO;AAQpB,OAAO,MAAMI,YAAqD,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,GAAA;EACrF,MAAM,CAACC,IAAI,EAAEC,OAAO,CAAC,GAAGd,QAAQ,CAAc,IAAI,CAAC;EACnD,MAAM,CAACe,OAAO,EAAEC,UAAU,CAAC,GAAGhB,QAAQ,CAAiB,IAAI,CAAC;EAC5D,MAAM,CAACiB,OAAO,EAAEC,UAAU,CAAC,GAAGlB,QAAQ,CAAiB,IAAI,CAAC;EAC5D,MAAM,CAACmB,QAAQ,EAAEC,WAAW,CAAC,GAAGpB,QAAQ,CAAW,OAAO,CAAC;EAC3D,MAAM,CAACqB,OAAO,EAAEC,UAAU,CAAC,GAAGtB,QAAQ,CAAC,IAAI,CAAC;EAE5CD,SAAS,CAAC,MAAM;IACd;IACA,MAAMwB,QAAQ,GAAG,MAAAA,CAAA,KAAY;MAC3B,IAAI;QAAA,IAAAC,aAAA;QACFC,OAAO,CAACC,GAAG,CAAC,sBAAsB,CAAC;QACnC,MAAM;UAAEC,IAAI,EAAE;YAAEV;UAAQ,CAAC;UAAEW;QAAM,CAAC,GAAG,MAAM3B,QAAQ,CAAC4B,IAAI,CAACC,UAAU,CAAC,CAAC;QAErE,IAAIF,KAAK,EAAE;UACTH,OAAO,CAACM,IAAI,CAAC,qBAAqB,EAAEH,KAAK,CAAC;UAC1CR,WAAW,CAAC,OAAO,CAAC;UACpBE,UAAU,CAAC,KAAK,CAAC;UACjB;QACF;QAEAJ,UAAU,CAACD,OAAO,CAAC;QACnBH,OAAO,EAAAU,aAAA,GAACP,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEJ,IAAI,cAAAW,aAAA,cAAAA,aAAA,GAAI,IAAI,CAAC;QAE9B,IAAIP,OAAO,aAAPA,OAAO,eAAPA,OAAO,CAAEJ,IAAI,EAAE;UACjBY,OAAO,CAACC,GAAG,CAAC,iCAAiC,CAAC;UAC9C,MAAMM,YAAY,CAACf,OAAO,CAACJ,IAAI,CAACoB,EAAE,CAAC;QACrC,CAAC,MAAM;UACLR,OAAO,CAACC,GAAG,CAAC,mCAAmC,CAAC;UAChDN,WAAW,CAAC,OAAO,CAAC;UACpBE,UAAU,CAAC,KAAK,CAAC;QACnB;MACF,CAAC,CAAC,OAAOM,KAAK,EAAE;QACdH,OAAO,CAACM,IAAI,CAAC,6BAA6B,EAAEH,KAAK,CAAC;QAClDR,WAAW,CAAC,OAAO,CAAC;QACpBE,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;;IAED;IACA,MAAMY,SAAS,GAAGC,UAAU,CAAC,MAAM;MACjCV,OAAO,CAACM,IAAI,CAAC,+CAA+C,CAAC;MAC7DX,WAAW,CAAC,OAAO,CAAC;MACpBE,UAAU,CAAC,KAAK,CAAC;IACnB,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;;IAEVC,QAAQ,CAAC,CAAC,CAACa,OAAO,CAAC,MAAM;MACvBC,YAAY,CAACH,SAAS,CAAC;IACzB,CAAC,CAAC;;IAEF;IACA,MAAM;MACJP,IAAI,EAAE;QAAEW;MAAa;IACvB,CAAC,GAAGrC,QAAQ,CAAC4B,IAAI,CAACU,iBAAiB,CAAC,OAAOC,KAAK,EAAEvB,OAAO,KAAK;MAAA,IAAAwB,cAAA;MAC5DvB,UAAU,CAACD,OAAO,CAAC;MACnBH,OAAO,EAAA2B,cAAA,GAACxB,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEJ,IAAI,cAAA4B,cAAA,cAAAA,cAAA,GAAI,IAAI,CAAC;MAE9B,IAAIxB,OAAO,aAAPA,OAAO,eAAPA,OAAO,CAAEJ,IAAI,EAAE;QACjB,MAAMmB,YAAY,CAACf,OAAO,CAACJ,IAAI,CAACoB,EAAE,CAAC;MACrC,CAAC,MAAM;QACLjB,UAAU,CAAC,IAAI,CAAC;QAChBI,WAAW,CAAC,OAAO,CAAC;QACpBE,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC,CAAC;IAEF,OAAO,MAAMgB,YAAY,CAACI,WAAW,CAAC,CAAC;EACzC,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMV,YAAY,GAAG,MAAOW,MAAc,IAAK;IAC7C,IAAI;MACFlB,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAEiB,MAAM,CAAC;;MAEjD;MACA,MAAMC,cAAc,GAAG3C,QAAQ,CAC5B4C,IAAI,CAAC,UAAU,CAAC,CAChBC,MAAM,CAAC,GAAG,CAAC,CACXC,EAAE,CAAC,IAAI,EAAEJ,MAAM,CAAC,CAChBK,MAAM,CAAC,CAAC;MAEX,MAAMC,cAAc,GAAG,IAAIC,OAAO,CAAC,CAACC,CAAC,EAAEC,MAAM,KAC3CjB,UAAU,CAAC,MAAMiB,MAAM,CAAC,IAAI3C,KAAK,CAAC,uBAAuB,CAAC,CAAC,EAAE,IAAI,CACnE,CAAC;MAED,MAAM;QAAEkB,IAAI;QAAEC;MAAM,CAAC,GAAG,MAAMsB,OAAO,CAACG,IAAI,CAAC,CAACT,cAAc,EAAEK,cAAc,CAAC,CAAQ;MAEnF,IAAIrB,KAAK,EAAE;QACTH,OAAO,CAACM,IAAI,CAAC,sBAAsB,EAAEH,KAAK,CAAC;;QAE3C;QACA,IAAIA,KAAK,CAAC0B,IAAI,KAAK,UAAU,EAAE;UAC7B7B,OAAO,CAACC,GAAG,CAAC,qDAAqD,CAAC;UAClE,IAAI;YACF,MAAM;cAAEC,IAAI,EAAE4B;YAAS,CAAC,GAAG,MAAMtD,QAAQ,CAAC4B,IAAI,CAAC2B,OAAO,CAAC,CAAC;YACxD,IAAID,QAAQ,CAAC1C,IAAI,EAAE;cAAA,IAAA4C,sBAAA;cACjB;cACA,MAAM;gBAAE9B,IAAI,EAAE+B,eAAe;gBAAE9B,KAAK,EAAE+B;cAAY,CAAC,GAAG,MAAM1D,QAAQ,CACjE4C,IAAI,CAAC,2BAA2B,CAAC,CACjCC,MAAM,CAAC,GAAG,CAAC,CACXC,EAAE,CAAC,OAAO,EAAEQ,QAAQ,CAAC1C,IAAI,CAAC+C,KAAM,CAAC,CACjCZ,MAAM,CAAC,CAAC;cAEX,IAAIU,eAAe,IAAI,CAACC,WAAW,EAAE;gBAAA,IAAAE,qBAAA;gBACnCpC,OAAO,CAACC,GAAG,CAAC,oDAAoD,CAAC;gBACjE;gBACA,MAAMoC,UAAU,GAAG;kBACjB7B,EAAE,EAAEsB,QAAQ,CAAC1C,IAAI,CAACoB,EAAE;kBACpB2B,KAAK,EAAEL,QAAQ,CAAC1C,IAAI,CAAC+C,KAAM;kBAC3BG,SAAS,EAAE,EAAAF,qBAAA,GAAAN,QAAQ,CAAC1C,IAAI,CAACmD,aAAa,cAAAH,qBAAA,uBAA3BA,qBAAA,CAA6BE,SAAS,KAAIL,eAAe,CAACK,SAAS;kBAC9EE,IAAI,EAAEP,eAAe,CAACO,IAAgB;kBACtCC,aAAa,EAAER,eAAe,CAACQ,aAAa;kBAC5CC,WAAW,EAAET,eAAe,CAACS,WAAW;kBACxCC,gBAAgB,EAAEV,eAAe,CAACU,gBAAgB;kBAClDC,gBAAgB,EAAEX,eAAe,CAACW,gBAAgB;kBAClDC,UAAU,EAAEZ,eAAe,CAACY,UAAU;kBACtCC,YAAY,EAAEb,eAAe,CAACa,YAAY;kBAC1CC,UAAU,EAAEd,eAAe,CAACc,UAAU;kBACtCC,UAAU,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;kBACpCC,UAAU,EAAE,IAAIF,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;gBACrC,CAAC;gBAED,MAAM;kBAAEhD,IAAI,EAAEkD,cAAc;kBAAEjD,KAAK,EAAEkD;gBAAY,CAAC,GAAG,MAAM7E,QAAQ,CAChE4C,IAAI,CAAC,UAAU,CAAC,CAChBkC,MAAM,CAAC,CAACjB,UAAU,CAAC,CAAC,CACpBhB,MAAM,CAAC,CAAC,CACRE,MAAM,CAAC,CAAC;gBAEX,IAAI8B,WAAW,EAAE;kBACfrD,OAAO,CAACG,KAAK,CAAC,gCAAgC,EAAEkD,WAAW,CAAC;gBAC9D,CAAC,MAAM;kBACLrD,OAAO,CAACC,GAAG,CAAC,qCAAqC,CAAC;;kBAElD;kBACA,MAAMzB,QAAQ,CACX4C,IAAI,CAAC,2BAA2B,CAAC,CACjCmC,MAAM,CAAC,CAAC,CACRjC,EAAE,CAAC,IAAI,EAAEW,eAAe,CAACzB,EAAE,CAAC;kBAE/BjB,UAAU,CAAC6D,cAAc,CAAC;kBAC1BzD,WAAW,CAACyD,cAAc,CAACZ,IAAI,IAAgB,MAAM,CAAC;kBACtD3C,UAAU,CAAC,KAAK,CAAC;kBACjB;gBACF;cACF;;cAEA;cACAG,OAAO,CAACC,GAAG,CAAC,oDAAoD,CAAC;cACjE,MAAMoC,UAAU,GAAG;gBACjB7B,EAAE,EAAEsB,QAAQ,CAAC1C,IAAI,CAACoB,EAAE;gBACpB2B,KAAK,EAAEL,QAAQ,CAAC1C,IAAI,CAAC+C,KAAM;gBAC3BG,SAAS,EAAE,EAAAN,sBAAA,GAAAF,QAAQ,CAAC1C,IAAI,CAACmD,aAAa,cAAAP,sBAAA,uBAA3BA,sBAAA,CAA6BM,SAAS,KAAI,EAAE;gBACvDE,IAAI,EAAEV,QAAQ,CAAC1C,IAAI,CAAC+C,KAAK,KAAK,mCAAmC,GAAG,OAAO,GAAe,MAAkB;gBAC5Ga,UAAU,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;gBACpCC,UAAU,EAAE,IAAIF,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;cACrC,CAAC;cAED,MAAM;gBAAEhD,IAAI,EAAEkD,cAAc;gBAAEjD,KAAK,EAAEkD;cAAY,CAAC,GAAG,MAAM7E,QAAQ,CAChE4C,IAAI,CAAC,UAAU,CAAC,CAChBkC,MAAM,CAAC,CAACjB,UAAU,CAAC,CAAC,CACpBhB,MAAM,CAAC,CAAC,CACRE,MAAM,CAAC,CAAC;cAEX,IAAI8B,WAAW,EAAE;gBACfrD,OAAO,CAACG,KAAK,CAAC,6BAA6B,EAAEkD,WAAW,CAAC;cAC3D,CAAC,MAAM;gBACLrD,OAAO,CAACC,GAAG,CAAC,8BAA8B,CAAC;gBAC3CV,UAAU,CAAC6D,cAAc,CAAC;gBAC1BzD,WAAW,CAACyD,cAAc,CAACZ,IAAI,IAAgB,MAAM,CAAC;gBACtD3C,UAAU,CAAC,KAAK,CAAC;gBACjB;cACF;YACF;UACF,CAAC,CAAC,OAAOwD,WAAW,EAAE;YACpBrD,OAAO,CAACM,IAAI,CAAC,2BAA2B,EAAE+C,WAAW,CAAC;UACxD;QACF;;QAEA;QACArD,OAAO,CAACC,GAAG,CAAC,iCAAiC,CAAC;QAC9CN,WAAW,CAAC,MAAM,CAAC;QACnBE,UAAU,CAAC,KAAK,CAAC;QACjB;MACF;MAEAG,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAEC,IAAI,CAAC;MAClDX,UAAU,CAACW,IAAI,CAAC;MAChBP,WAAW,CAACO,IAAI,CAACsC,IAAI,IAAI,MAAM,CAAC;MAChC3C,UAAU,CAAC,KAAK,CAAC;IACnB,CAAC,CAAC,OAAOM,KAAK,EAAE;MACdH,OAAO,CAACG,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;MAC9C;MACAR,WAAW,CAAC,MAAM,CAAC;MACnBE,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAM2D,MAAM,GAAG,MAAAA,CAAOrB,KAAa,EAAEsB,QAAgB,EAAEC,QAAgB,KAAK;IAC1E,IAAI;MACF,MAAM;QAAExD,IAAI;QAAEC;MAAM,CAAC,GAAG,MAAM3B,QAAQ,CAAC4B,IAAI,CAACoD,MAAM,CAAC;QACjDrB,KAAK;QACLsB,QAAQ;QACRE,OAAO,EAAE;UACPzD,IAAI,EAAE;YACJoC,SAAS,EAAEoB;UACb;QACF;MACF,CAAC,CAAC;;MAEF;MACA,IAAIxD,IAAI,CAACd,IAAI,IAAI,CAACe,KAAK,EAAE;QACvB,IAAI;UACF;UACA,MAAM;YAAED,IAAI,EAAE+B,eAAe;YAAE9B,KAAK,EAAE+B;UAAY,CAAC,GAAG,MAAM1D,QAAQ,CACjE4C,IAAI,CAAC,UAAU,CAAC,CAChBC,MAAM,CAAC,GAAG,CAAC,CACXC,EAAE,CAAC,OAAO,EAAEpB,IAAI,CAACd,IAAI,CAAC+C,KAAM,CAAC,CAC7BZ,MAAM,CAAC,CAAC;UAEX,IAAIU,eAAe,IAAI,CAACC,WAAW,EAAE;YACnClC,OAAO,CAACC,GAAG,CAAC,kDAAkD,CAAC;YAC/D;YACA,MAAMzB,QAAQ,CACX4C,IAAI,CAAC,UAAU,CAAC,CAChBwC,MAAM,CAAC;cACNpD,EAAE,EAAEN,IAAI,CAACd,IAAI,CAACoB,EAAE;cAChB8B,SAAS,EAAEoB,QAAQ,IAAIzB,eAAe,CAACK,SAAS;cAChDa,UAAU,EAAE,IAAIF,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;YACrC,CAAC,CAAC,CACD5B,EAAE,CAAC,OAAO,EAAEpB,IAAI,CAACd,IAAI,CAAC+C,KAAM,CAAC;UAClC,CAAC,MAAM;YACL;YACA,MAAM3D,QAAQ,CACX4C,IAAI,CAAC,UAAU,CAAC,CAChBkC,MAAM,CAAC,CAAC;cACP9C,EAAE,EAAEN,IAAI,CAACd,IAAI,CAACoB,EAAE;cAChB2B,KAAK,EAAEjC,IAAI,CAACd,IAAI,CAAC+C,KAAM;cACvBG,SAAS,EAAEoB,QAAQ;cACnBlB,IAAI,EAAE,MAAM;cACZQ,UAAU,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;cACpCC,UAAU,EAAE,IAAIF,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;YACrC,CAAC,CAAC,CAAC;UACP;QACF,CAAC,CAAC,OAAOW,YAAY,EAAE;UACrB7D,OAAO,CAACM,IAAI,CAAC,wDAAwD,EAAEuD,YAAY,CAAC;UACpF;QACF;MACF;MAEA,OAAO;QAAE3D,IAAI;QAAEC;MAAM,CAAC;IACxB,CAAC,CAAC,OAAOA,KAAK,EAAE;MACd,OAAO;QAAED,IAAI,EAAE,IAAI;QAAEC,KAAK,EAAE;UAAE2D,OAAO,EAAE;QAA2C;MAAE,CAAC;IACvF;EACF,CAAC;EAED,MAAMC,MAAM,GAAG,MAAAA,CAAO5B,KAAa,EAAEsB,QAAgB,KAAK;IACxD,IAAI;MACF,MAAM;QAAEvD,IAAI;QAAEC;MAAM,CAAC,GAAG,MAAM3B,QAAQ,CAAC4B,IAAI,CAAC4D,kBAAkB,CAAC;QAC7D7B,KAAK;QACLsB;MACF,CAAC,CAAC;MAEF,OAAO;QAAEvD,IAAI;QAAEC;MAAM,CAAC;IACxB,CAAC,CAAC,OAAOA,KAAK,EAAE;MACd,OAAO;QAAED,IAAI,EAAE,IAAI;QAAEC,KAAK,EAAE;UAAE2D,OAAO,EAAE;QAA2C;MAAE,CAAC;IACvF;EACF,CAAC;EAED,MAAMG,OAAO,GAAG,MAAAA,CAAA,KAAY;IAC1B,IAAI;MACF,MAAM;QAAE9D;MAAM,CAAC,GAAG,MAAM3B,QAAQ,CAAC4B,IAAI,CAAC6D,OAAO,CAAC,CAAC;MAC/C,IAAI9D,KAAK,EAAE;QACTH,OAAO,CAACG,KAAK,CAAC,oBAAoB,EAAEA,KAAK,CAAC;MAC5C;IACF,CAAC,CAAC,OAAOA,KAAK,EAAE;MACdH,OAAO,CAACC,GAAG,CAAC,+CAA+C,CAAC;IAC9D;EACF,CAAC;EAED,MAAMiE,aAAa,GAAG,MAAOC,OAAyB,IAAK;IACzD,IAAI,CAAC/E,IAAI,EAAE;IAEX,IAAI;MACF,MAAM;QAAEe;MAAM,CAAC,GAAG,MAAM3B,QAAQ,CAC7B4C,IAAI,CAAC,UAAU,CAAC,CAChBwC,MAAM,CAACO,OAAO,CAAC,CACf7C,EAAE,CAAC,IAAI,EAAElC,IAAI,CAACoB,EAAE,CAAC;MAEpB,IAAIL,KAAK,EAAE;QACT,MAAMA,KAAK;MACb;;MAEA;MACA,MAAMI,YAAY,CAACnB,IAAI,CAACoB,EAAE,CAAC;IAC7B,CAAC,CAAC,OAAOL,KAAK,EAAE;MACdH,OAAO,CAACG,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MAC/C,MAAMA,KAAK;IACb;EACF,CAAC;EAED,MAAMiE,KAAK,GAAG;IACZhF,IAAI;IACJE,OAAO;IACPE,OAAO;IACPE,QAAQ;IACRE,OAAO;IACP4D,MAAM;IACNO,MAAM;IACNE,OAAO;IACPC,aAAa;IACbG,OAAO,EAAE3E,QAAQ,KAAK,OAAO;IAC7B4E,MAAM,EAAE5E,QAAQ,KAAK,MAAM;IAC3B6E,OAAO,EAAE7E,QAAQ,KAAK,OAAO;IAC7B8E,YAAY,EAAE9E,QAAQ,KAAK;EAC7B,CAAC;EAED,oBACEhB,OAAA,CAACC,WAAW,CAAC8F,QAAQ;IAACL,KAAK,EAAEA,KAAM;IAAAlF,QAAA,EAChCA;EAAQ;IAAAwF,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACW,CAAC;AAE3B,CAAC;AAAC1F,GAAA,CAhUWF,YAAqD;AAAA6F,EAAA,GAArD7F,YAAqD;AAAA,IAAA6F,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}