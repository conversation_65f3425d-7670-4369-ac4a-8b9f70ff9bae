{"ast": null, "code": "import { defaultLocale } from \"./_lib/defaultLocale.js\";\nimport { longFormatters } from \"./_lib/format/longFormatters.js\";\nimport { isProtectedDayOfYearToken, isProtectedWeekYearToken, warnOrThrowProtectedError } from \"./_lib/protectedTokens.js\";\nimport { constructFrom } from \"./constructFrom.js\";\nimport { getDefaultOptions } from \"./getDefaultOptions.js\";\nimport { toDate } from \"./toDate.js\";\nimport { DateTimezoneSetter } from \"./parse/_lib/Setter.js\";\nimport { parsers } from \"./parse/_lib/parsers.js\";\n\n// Rexports of internal for libraries to use.\n// See: https://github.com/date-fns/date-fns/issues/3638#issuecomment-1877082874\nexport { longFormatters, parsers };\n\n/**\n * The {@link parse} function options.\n */\n\n// This RegExp consists of three parts separated by `|`:\n// - [yYQqMLwIdDecihHKkms]o matches any available ordinal number token\n//   (one of the certain letters followed by `o`)\n// - (\\w)\\1* matches any sequences of the same letter\n// - '' matches two quote characters in a row\n// - '(''|[^'])+('|$) matches anything surrounded by two quote characters ('),\n//   except a single quote symbol, which ends the sequence.\n//   Two quote characters do not end the sequence.\n//   If there is no matching single quote\n//   then the sequence will continue until the end of the string.\n// - . matches any single character unmatched by previous parts of the RegExps\nconst formattingTokensRegExp = /[yYQqMLwIdDecihHKkms]o|(\\w)\\1*|''|'(''|[^'])+('|$)|./g;\n\n// This RegExp catches symbols escaped by quotes, and also\n// sequences of symbols P, p, and the combinations like `PPPPPPPppppp`\nconst longFormattingTokensRegExp = /P+p+|P+|p+|''|'(''|[^'])+('|$)|./g;\nconst escapedStringRegExp = /^'([^]*?)'?$/;\nconst doubleQuoteRegExp = /''/g;\nconst notWhitespaceRegExp = /\\S/;\nconst unescapedLatinCharacterRegExp = /[a-zA-Z]/;\n\n/**\n * @name parse\n * @category Common Helpers\n * @summary Parse the date.\n *\n * @description\n * Return the date parsed from string using the given format string.\n *\n * > ⚠️ Please note that the `format` tokens differ from Moment.js and other libraries.\n * > See: https://github.com/date-fns/date-fns/blob/master/docs/unicodeTokens.md\n *\n * The characters in the format string wrapped between two single quotes characters (') are escaped.\n * Two single quotes in a row, whether inside or outside a quoted sequence, represent a 'real' single quote.\n *\n * Format of the format string is based on Unicode Technical Standard #35:\n * https://www.unicode.org/reports/tr35/tr35-dates.html#Date_Field_Symbol_Table\n * with a few additions (see note 5 below the table).\n *\n * Not all tokens are compatible. Combinations that don't make sense or could lead to bugs are prohibited\n * and will throw `RangeError`. For example usage of 24-hour format token with AM/PM token will throw an exception:\n *\n * ```javascript\n * parse('23 AM', 'HH a', new Date())\n * //=> RangeError: The format string mustn't contain `HH` and `a` at the same time\n * ```\n *\n * See the compatibility table: https://docs.google.com/spreadsheets/d/e/2PACX-1vQOPU3xUhplll6dyoMmVUXHKl_8CRDs6_ueLmex3SoqwhuolkuN3O05l4rqx5h1dKX8eb46Ul-CCSrq/pubhtml?gid=0&single=true\n *\n * Accepted format string patterns:\n * | Unit                            |Prior| Pattern | Result examples                   | Notes |\n * |---------------------------------|-----|---------|-----------------------------------|-------|\n * | Era                             | 140 | G..GGG  | AD, BC                            |       |\n * |                                 |     | GGGG    | Anno Domini, Before Christ        | 2     |\n * |                                 |     | GGGGG   | A, B                              |       |\n * | Calendar year                   | 130 | y       | 44, 1, 1900, 2017, 9999           | 4     |\n * |                                 |     | yo      | 44th, 1st, 1900th, 9999999th      | 4,5   |\n * |                                 |     | yy      | 44, 01, 00, 17                    | 4     |\n * |                                 |     | yyy     | 044, 001, 123, 999                | 4     |\n * |                                 |     | yyyy    | 0044, 0001, 1900, 2017            | 4     |\n * |                                 |     | yyyyy   | ...                               | 2,4   |\n * | Local week-numbering year       | 130 | Y       | 44, 1, 1900, 2017, 9000           | 4     |\n * |                                 |     | Yo      | 44th, 1st, 1900th, 9999999th      | 4,5   |\n * |                                 |     | YY      | 44, 01, 00, 17                    | 4,6   |\n * |                                 |     | YYY     | 044, 001, 123, 999                | 4     |\n * |                                 |     | YYYY    | 0044, 0001, 1900, 2017            | 4,6   |\n * |                                 |     | YYYYY   | ...                               | 2,4   |\n * | ISO week-numbering year         | 130 | R       | -43, 1, 1900, 2017, 9999, -9999   | 4,5   |\n * |                                 |     | RR      | -43, 01, 00, 17                   | 4,5   |\n * |                                 |     | RRR     | -043, 001, 123, 999, -999         | 4,5   |\n * |                                 |     | RRRR    | -0043, 0001, 2017, 9999, -9999    | 4,5   |\n * |                                 |     | RRRRR   | ...                               | 2,4,5 |\n * | Extended year                   | 130 | u       | -43, 1, 1900, 2017, 9999, -999    | 4     |\n * |                                 |     | uu      | -43, 01, 99, -99                  | 4     |\n * |                                 |     | uuu     | -043, 001, 123, 999, -999         | 4     |\n * |                                 |     | uuuu    | -0043, 0001, 2017, 9999, -9999    | 4     |\n * |                                 |     | uuuuu   | ...                               | 2,4   |\n * | Quarter (formatting)            | 120 | Q       | 1, 2, 3, 4                        |       |\n * |                                 |     | Qo      | 1st, 2nd, 3rd, 4th                | 5     |\n * |                                 |     | QQ      | 01, 02, 03, 04                    |       |\n * |                                 |     | QQQ     | Q1, Q2, Q3, Q4                    |       |\n * |                                 |     | QQQQ    | 1st quarter, 2nd quarter, ...     | 2     |\n * |                                 |     | QQQQQ   | 1, 2, 3, 4                        | 4     |\n * | Quarter (stand-alone)           | 120 | q       | 1, 2, 3, 4                        |       |\n * |                                 |     | qo      | 1st, 2nd, 3rd, 4th                | 5     |\n * |                                 |     | qq      | 01, 02, 03, 04                    |       |\n * |                                 |     | qqq     | Q1, Q2, Q3, Q4                    |       |\n * |                                 |     | qqqq    | 1st quarter, 2nd quarter, ...     | 2     |\n * |                                 |     | qqqqq   | 1, 2, 3, 4                        | 3     |\n * | Month (formatting)              | 110 | M       | 1, 2, ..., 12                     |       |\n * |                                 |     | Mo      | 1st, 2nd, ..., 12th               | 5     |\n * |                                 |     | MM      | 01, 02, ..., 12                   |       |\n * |                                 |     | MMM     | Jan, Feb, ..., Dec                |       |\n * |                                 |     | MMMM    | January, February, ..., December  | 2     |\n * |                                 |     | MMMMM   | J, F, ..., D                      |       |\n * | Month (stand-alone)             | 110 | L       | 1, 2, ..., 12                     |       |\n * |                                 |     | Lo      | 1st, 2nd, ..., 12th               | 5     |\n * |                                 |     | LL      | 01, 02, ..., 12                   |       |\n * |                                 |     | LLL     | Jan, Feb, ..., Dec                |       |\n * |                                 |     | LLLL    | January, February, ..., December  | 2     |\n * |                                 |     | LLLLL   | J, F, ..., D                      |       |\n * | Local week of year              | 100 | w       | 1, 2, ..., 53                     |       |\n * |                                 |     | wo      | 1st, 2nd, ..., 53th               | 5     |\n * |                                 |     | ww      | 01, 02, ..., 53                   |       |\n * | ISO week of year                | 100 | I       | 1, 2, ..., 53                     | 5     |\n * |                                 |     | Io      | 1st, 2nd, ..., 53th               | 5     |\n * |                                 |     | II      | 01, 02, ..., 53                   | 5     |\n * | Day of month                    |  90 | d       | 1, 2, ..., 31                     |       |\n * |                                 |     | do      | 1st, 2nd, ..., 31st               | 5     |\n * |                                 |     | dd      | 01, 02, ..., 31                   |       |\n * | Day of year                     |  90 | D       | 1, 2, ..., 365, 366               | 7     |\n * |                                 |     | Do      | 1st, 2nd, ..., 365th, 366th       | 5     |\n * |                                 |     | DD      | 01, 02, ..., 365, 366             | 7     |\n * |                                 |     | DDD     | 001, 002, ..., 365, 366           |       |\n * |                                 |     | DDDD    | ...                               | 2     |\n * | Day of week (formatting)        |  90 | E..EEE  | Mon, Tue, Wed, ..., Sun           |       |\n * |                                 |     | EEEE    | Monday, Tuesday, ..., Sunday      | 2     |\n * |                                 |     | EEEEE   | M, T, W, T, F, S, S               |       |\n * |                                 |     | EEEEEE  | Mo, Tu, We, Th, Fr, Sa, Su        |       |\n * | ISO day of week (formatting)    |  90 | i       | 1, 2, 3, ..., 7                   | 5     |\n * |                                 |     | io      | 1st, 2nd, ..., 7th                | 5     |\n * |                                 |     | ii      | 01, 02, ..., 07                   | 5     |\n * |                                 |     | iii     | Mon, Tue, Wed, ..., Sun           | 5     |\n * |                                 |     | iiii    | Monday, Tuesday, ..., Sunday      | 2,5   |\n * |                                 |     | iiiii   | M, T, W, T, F, S, S               | 5     |\n * |                                 |     | iiiiii  | Mo, Tu, We, Th, Fr, Sa, Su        | 5     |\n * | Local day of week (formatting)  |  90 | e       | 2, 3, 4, ..., 1                   |       |\n * |                                 |     | eo      | 2nd, 3rd, ..., 1st                | 5     |\n * |                                 |     | ee      | 02, 03, ..., 01                   |       |\n * |                                 |     | eee     | Mon, Tue, Wed, ..., Sun           |       |\n * |                                 |     | eeee    | Monday, Tuesday, ..., Sunday      | 2     |\n * |                                 |     | eeeee   | M, T, W, T, F, S, S               |       |\n * |                                 |     | eeeeee  | Mo, Tu, We, Th, Fr, Sa, Su        |       |\n * | Local day of week (stand-alone) |  90 | c       | 2, 3, 4, ..., 1                   |       |\n * |                                 |     | co      | 2nd, 3rd, ..., 1st                | 5     |\n * |                                 |     | cc      | 02, 03, ..., 01                   |       |\n * |                                 |     | ccc     | Mon, Tue, Wed, ..., Sun           |       |\n * |                                 |     | cccc    | Monday, Tuesday, ..., Sunday      | 2     |\n * |                                 |     | ccccc   | M, T, W, T, F, S, S               |       |\n * |                                 |     | cccccc  | Mo, Tu, We, Th, Fr, Sa, Su        |       |\n * | AM, PM                          |  80 | a..aaa  | AM, PM                            |       |\n * |                                 |     | aaaa    | a.m., p.m.                        | 2     |\n * |                                 |     | aaaaa   | a, p                              |       |\n * | AM, PM, noon, midnight          |  80 | b..bbb  | AM, PM, noon, midnight            |       |\n * |                                 |     | bbbb    | a.m., p.m., noon, midnight        | 2     |\n * |                                 |     | bbbbb   | a, p, n, mi                       |       |\n * | Flexible day period             |  80 | B..BBB  | at night, in the morning, ...     |       |\n * |                                 |     | BBBB    | at night, in the morning, ...     | 2     |\n * |                                 |     | BBBBB   | at night, in the morning, ...     |       |\n * | Hour [1-12]                     |  70 | h       | 1, 2, ..., 11, 12                 |       |\n * |                                 |     | ho      | 1st, 2nd, ..., 11th, 12th         | 5     |\n * |                                 |     | hh      | 01, 02, ..., 11, 12               |       |\n * | Hour [0-23]                     |  70 | H       | 0, 1, 2, ..., 23                  |       |\n * |                                 |     | Ho      | 0th, 1st, 2nd, ..., 23rd          | 5     |\n * |                                 |     | HH      | 00, 01, 02, ..., 23               |       |\n * | Hour [0-11]                     |  70 | K       | 1, 2, ..., 11, 0                  |       |\n * |                                 |     | Ko      | 1st, 2nd, ..., 11th, 0th          | 5     |\n * |                                 |     | KK      | 01, 02, ..., 11, 00               |       |\n * | Hour [1-24]                     |  70 | k       | 24, 1, 2, ..., 23                 |       |\n * |                                 |     | ko      | 24th, 1st, 2nd, ..., 23rd         | 5     |\n * |                                 |     | kk      | 24, 01, 02, ..., 23               |       |\n * | Minute                          |  60 | m       | 0, 1, ..., 59                     |       |\n * |                                 |     | mo      | 0th, 1st, ..., 59th               | 5     |\n * |                                 |     | mm      | 00, 01, ..., 59                   |       |\n * | Second                          |  50 | s       | 0, 1, ..., 59                     |       |\n * |                                 |     | so      | 0th, 1st, ..., 59th               | 5     |\n * |                                 |     | ss      | 00, 01, ..., 59                   |       |\n * | Seconds timestamp               |  40 | t       | 512969520                         |       |\n * |                                 |     | tt      | ...                               | 2     |\n * | Fraction of second              |  30 | S       | 0, 1, ..., 9                      |       |\n * |                                 |     | SS      | 00, 01, ..., 99                   |       |\n * |                                 |     | SSS     | 000, 001, ..., 999                |       |\n * |                                 |     | SSSS    | ...                               | 2     |\n * | Milliseconds timestamp          |  20 | T       | 512969520900                      |       |\n * |                                 |     | TT      | ...                               | 2     |\n * | Timezone (ISO-8601 w/ Z)        |  10 | X       | -08, +0530, Z                     |       |\n * |                                 |     | XX      | -0800, +0530, Z                   |       |\n * |                                 |     | XXX     | -08:00, +05:30, Z                 |       |\n * |                                 |     | XXXX    | -0800, +0530, Z, +123456          | 2     |\n * |                                 |     | XXXXX   | -08:00, +05:30, Z, +12:34:56      |       |\n * | Timezone (ISO-8601 w/o Z)       |  10 | x       | -08, +0530, +00                   |       |\n * |                                 |     | xx      | -0800, +0530, +0000               |       |\n * |                                 |     | xxx     | -08:00, +05:30, +00:00            | 2     |\n * |                                 |     | xxxx    | -0800, +0530, +0000, +123456      |       |\n * |                                 |     | xxxxx   | -08:00, +05:30, +00:00, +12:34:56 |       |\n * | Long localized date             |  NA | P       | 05/29/1453                        | 5,8   |\n * |                                 |     | PP      | May 29, 1453                      |       |\n * |                                 |     | PPP     | May 29th, 1453                    |       |\n * |                                 |     | PPPP    | Sunday, May 29th, 1453            | 2,5,8 |\n * | Long localized time             |  NA | p       | 12:00 AM                          | 5,8   |\n * |                                 |     | pp      | 12:00:00 AM                       |       |\n * | Combination of date and time    |  NA | Pp      | 05/29/1453, 12:00 AM              |       |\n * |                                 |     | PPpp    | May 29, 1453, 12:00:00 AM         |       |\n * |                                 |     | PPPpp   | May 29th, 1453 at ...             |       |\n * |                                 |     | PPPPpp  | Sunday, May 29th, 1453 at ...     | 2,5,8 |\n * Notes:\n * 1. \"Formatting\" units (e.g. formatting quarter) in the default en-US locale\n *    are the same as \"stand-alone\" units, but are different in some languages.\n *    \"Formatting\" units are declined according to the rules of the language\n *    in the context of a date. \"Stand-alone\" units are always nominative singular.\n *    In `format` function, they will produce different result:\n *\n *    `format(new Date(2017, 10, 6), 'do LLLL', {locale: cs}) //=> '6. listopad'`\n *\n *    `format(new Date(2017, 10, 6), 'do MMMM', {locale: cs}) //=> '6. listopadu'`\n *\n *    `parse` will try to match both formatting and stand-alone units interchangeably.\n *\n * 2. Any sequence of the identical letters is a pattern, unless it is escaped by\n *    the single quote characters (see below).\n *    If the sequence is longer than listed in table:\n *    - for numerical units (`yyyyyyyy`) `parse` will try to match a number\n *      as wide as the sequence\n *    - for text units (`MMMMMMMM`) `parse` will try to match the widest variation of the unit.\n *      These variations are marked with \"2\" in the last column of the table.\n *\n * 3. `QQQQQ` and `qqqqq` could be not strictly numerical in some locales.\n *    These tokens represent the shortest form of the quarter.\n *\n * 4. The main difference between `y` and `u` patterns are B.C. years:\n *\n *    | Year | `y` | `u` |\n *    |------|-----|-----|\n *    | AC 1 |   1 |   1 |\n *    | BC 1 |   1 |   0 |\n *    | BC 2 |   2 |  -1 |\n *\n *    Also `yy` will try to guess the century of two digit year by proximity with `referenceDate`:\n *\n *    `parse('50', 'yy', new Date(2018, 0, 1)) //=> Sat Jan 01 2050 00:00:00`\n *\n *    `parse('75', 'yy', new Date(2018, 0, 1)) //=> Wed Jan 01 1975 00:00:00`\n *\n *    while `uu` will just assign the year as is:\n *\n *    `parse('50', 'uu', new Date(2018, 0, 1)) //=> Sat Jan 01 0050 00:00:00`\n *\n *    `parse('75', 'uu', new Date(2018, 0, 1)) //=> Tue Jan 01 0075 00:00:00`\n *\n *    The same difference is true for local and ISO week-numbering years (`Y` and `R`),\n *    except local week-numbering years are dependent on `options.weekStartsOn`\n *    and `options.firstWeekContainsDate` (compare [setISOWeekYear](https://date-fns.org/docs/setISOWeekYear)\n *    and [setWeekYear](https://date-fns.org/docs/setWeekYear)).\n *\n * 5. These patterns are not in the Unicode Technical Standard #35:\n *    - `i`: ISO day of week\n *    - `I`: ISO week of year\n *    - `R`: ISO week-numbering year\n *    - `o`: ordinal number modifier\n *    - `P`: long localized date\n *    - `p`: long localized time\n *\n * 6. `YY` and `YYYY` tokens represent week-numbering years but they are often confused with years.\n *    You should enable `options.useAdditionalWeekYearTokens` to use them. See: https://github.com/date-fns/date-fns/blob/master/docs/unicodeTokens.md\n *\n * 7. `D` and `DD` tokens represent days of the year but they are often confused with days of the month.\n *    You should enable `options.useAdditionalDayOfYearTokens` to use them. See: https://github.com/date-fns/date-fns/blob/master/docs/unicodeTokens.md\n *\n * 8. `P+` tokens do not have a defined priority since they are merely aliases to other tokens based\n *    on the given locale.\n *\n *    using `en-US` locale: `P` => `MM/dd/yyyy`\n *    using `en-US` locale: `p` => `hh:mm a`\n *    using `pt-BR` locale: `P` => `dd/MM/yyyy`\n *    using `pt-BR` locale: `p` => `HH:mm`\n *\n * Values will be assigned to the date in the descending order of its unit's priority.\n * Units of an equal priority overwrite each other in the order of appearance.\n *\n * If no values of higher priority are parsed (e.g. when parsing string 'January 1st' without a year),\n * the values will be taken from 3rd argument `referenceDate` which works as a context of parsing.\n *\n * `referenceDate` must be passed for correct work of the function.\n * If you're not sure which `referenceDate` to supply, create a new instance of Date:\n * `parse('02/11/2014', 'MM/dd/yyyy', new Date())`\n * In this case parsing will be done in the context of the current date.\n * If `referenceDate` is `Invalid Date` or a value not convertible to valid `Date`,\n * then `Invalid Date` will be returned.\n *\n * The result may vary by locale.\n *\n * If `formatString` matches with `dateString` but does not provides tokens, `referenceDate` will be returned.\n *\n * If parsing failed, `Invalid Date` will be returned.\n * Invalid Date is a Date, whose time value is NaN.\n * Time value of Date: http://es5.github.io/#x15.9.1.1\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n * @typeParam ResultDate - The result `Date` type, it is the type returned from the context function if it is passed, or inferred from the arguments.\n *\n * @param dateStr - The string to parse\n * @param formatStr - The string of tokens\n * @param referenceDate - defines values missing from the parsed dateString\n * @param options - An object with options.\n *   see: https://github.com/date-fns/date-fns/blob/master/docs/unicodeTokens.md\n *   see: https://github.com/date-fns/date-fns/blob/master/docs/unicodeTokens.md\n *\n * @returns The parsed date\n *\n * @throws `options.locale` must contain `match` property\n * @throws use `yyyy` instead of `YYYY` for formatting years using [format provided] to the input [input provided]; see: https://github.com/date-fns/date-fns/blob/master/docs/unicodeTokens.md\n * @throws use `yy` instead of `YY` for formatting years using [format provided] to the input [input provided]; see: https://github.com/date-fns/date-fns/blob/master/docs/unicodeTokens.md\n * @throws use `d` instead of `D` for formatting days of the month using [format provided] to the input [input provided]; see: https://github.com/date-fns/date-fns/blob/master/docs/unicodeTokens.md\n * @throws use `dd` instead of `DD` for formatting days of the month using [format provided] to the input [input provided]; see: https://github.com/date-fns/date-fns/blob/master/docs/unicodeTokens.md\n * @throws format string contains an unescaped latin alphabet character\n *\n * @example\n * // Parse 11 February 2014 from middle-endian format:\n * var result = parse('02/11/2014', 'MM/dd/yyyy', new Date())\n * //=> Tue Feb 11 2014 00:00:00\n *\n * @example\n * // Parse 28th of February in Esperanto locale in the context of 2010 year:\n * import eo from 'date-fns/locale/eo'\n * var result = parse('28-a de februaro', \"do 'de' MMMM\", new Date(2010, 0, 1), {\n *   locale: eo\n * })\n * //=> Sun Feb 28 2010 00:00:00\n */\nexport function parse(dateStr, formatStr, referenceDate, options) {\n  const invalidDate = () => constructFrom(options?.in || referenceDate, NaN);\n  const defaultOptions = getDefaultOptions();\n  const locale = options?.locale ?? defaultOptions.locale ?? defaultLocale;\n  const firstWeekContainsDate = options?.firstWeekContainsDate ?? options?.locale?.options?.firstWeekContainsDate ?? defaultOptions.firstWeekContainsDate ?? defaultOptions.locale?.options?.firstWeekContainsDate ?? 1;\n  const weekStartsOn = options?.weekStartsOn ?? options?.locale?.options?.weekStartsOn ?? defaultOptions.weekStartsOn ?? defaultOptions.locale?.options?.weekStartsOn ?? 0;\n  if (!formatStr) return dateStr ? invalidDate() : toDate(referenceDate, options?.in);\n  const subFnOptions = {\n    firstWeekContainsDate,\n    weekStartsOn,\n    locale\n  };\n\n  // If timezone isn't specified, it will try to use the context or\n  // the reference date and fallback to the system time zone.\n  const setters = [new DateTimezoneSetter(options?.in, referenceDate)];\n  const tokens = formatStr.match(longFormattingTokensRegExp).map(substring => {\n    const firstCharacter = substring[0];\n    if (firstCharacter in longFormatters) {\n      const longFormatter = longFormatters[firstCharacter];\n      return longFormatter(substring, locale.formatLong);\n    }\n    return substring;\n  }).join(\"\").match(formattingTokensRegExp);\n  const usedTokens = [];\n  for (let token of tokens) {\n    if (!options?.useAdditionalWeekYearTokens && isProtectedWeekYearToken(token)) {\n      warnOrThrowProtectedError(token, formatStr, dateStr);\n    }\n    if (!options?.useAdditionalDayOfYearTokens && isProtectedDayOfYearToken(token)) {\n      warnOrThrowProtectedError(token, formatStr, dateStr);\n    }\n    const firstCharacter = token[0];\n    const parser = parsers[firstCharacter];\n    if (parser) {\n      const {\n        incompatibleTokens\n      } = parser;\n      if (Array.isArray(incompatibleTokens)) {\n        const incompatibleToken = usedTokens.find(usedToken => incompatibleTokens.includes(usedToken.token) || usedToken.token === firstCharacter);\n        if (incompatibleToken) {\n          throw new RangeError(`The format string mustn't contain \\`${incompatibleToken.fullToken}\\` and \\`${token}\\` at the same time`);\n        }\n      } else if (parser.incompatibleTokens === \"*\" && usedTokens.length > 0) {\n        throw new RangeError(`The format string mustn't contain \\`${token}\\` and any other token at the same time`);\n      }\n      usedTokens.push({\n        token: firstCharacter,\n        fullToken: token\n      });\n      const parseResult = parser.run(dateStr, token, locale.match, subFnOptions);\n      if (!parseResult) {\n        return invalidDate();\n      }\n      setters.push(parseResult.setter);\n      dateStr = parseResult.rest;\n    } else {\n      if (firstCharacter.match(unescapedLatinCharacterRegExp)) {\n        throw new RangeError(\"Format string contains an unescaped latin alphabet character `\" + firstCharacter + \"`\");\n      }\n\n      // Replace two single quote characters with one single quote character\n      if (token === \"''\") {\n        token = \"'\";\n      } else if (firstCharacter === \"'\") {\n        token = cleanEscapedString(token);\n      }\n\n      // Cut token from string, or, if string doesn't match the token, return Invalid Date\n      if (dateStr.indexOf(token) === 0) {\n        dateStr = dateStr.slice(token.length);\n      } else {\n        return invalidDate();\n      }\n    }\n  }\n\n  // Check if the remaining input contains something other than whitespace\n  if (dateStr.length > 0 && notWhitespaceRegExp.test(dateStr)) {\n    return invalidDate();\n  }\n  const uniquePrioritySetters = setters.map(setter => setter.priority).sort((a, b) => b - a).filter((priority, index, array) => array.indexOf(priority) === index).map(priority => setters.filter(setter => setter.priority === priority).sort((a, b) => b.subPriority - a.subPriority)).map(setterArray => setterArray[0]);\n  let date = toDate(referenceDate, options?.in);\n  if (isNaN(+date)) return invalidDate();\n  const flags = {};\n  for (const setter of uniquePrioritySetters) {\n    if (!setter.validate(date, subFnOptions)) {\n      return invalidDate();\n    }\n    const result = setter.set(date, flags, subFnOptions);\n    // Result is tuple (date, flags)\n    if (Array.isArray(result)) {\n      date = result[0];\n      Object.assign(flags, result[1]);\n      // Result is date\n    } else {\n      date = result;\n    }\n  }\n  return date;\n}\nfunction cleanEscapedString(input) {\n  return input.match(escapedStringRegExp)[1].replace(doubleQuoteRegExp, \"'\");\n}\n\n// Fallback for modularized imports:\nexport default parse;", "map": {"version": 3, "names": ["defaultLocale", "longFormatters", "isProtectedDayOfYearToken", "isProtectedWeekYearToken", "warnOrThrowProtectedError", "constructFrom", "getDefaultOptions", "toDate", "DateTimezoneSetter", "parsers", "formattingTokensRegExp", "longFormattingTokensRegExp", "escapedStringRegExp", "doubleQuoteRegExp", "notWhitespaceRegExp", "unescapedLatinCharacterRegExp", "parse", "dateStr", "formatStr", "referenceDate", "options", "invalidDate", "in", "NaN", "defaultOptions", "locale", "firstWeekContainsDate", "weekStartsOn", "subFnOptions", "setters", "tokens", "match", "map", "substring", "firstCharacter", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "formatLong", "join", "usedTokens", "token", "useAdditionalWeekYearTokens", "useAdditionalDayOfYearTokens", "parser", "incompatibleTokens", "Array", "isArray", "incompatibleToken", "find", "usedToken", "includes", "RangeError", "fullToken", "length", "push", "parseResult", "run", "setter", "rest", "cleanEscapedString", "indexOf", "slice", "test", "uniquePrioritySetters", "priority", "sort", "a", "b", "filter", "index", "array", "subPriority", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "date", "isNaN", "flags", "validate", "result", "set", "Object", "assign", "input", "replace"], "sources": ["C:/Users/<USER>/node_modules/date-fns/parse.js"], "sourcesContent": ["import { defaultLocale } from \"./_lib/defaultLocale.js\";\nimport { longFormatters } from \"./_lib/format/longFormatters.js\";\nimport {\n  isProtectedDayOfYearToken,\n  isProtectedWeekYearToken,\n  warnOrThrowProtectedError,\n} from \"./_lib/protectedTokens.js\";\nimport { constructFrom } from \"./constructFrom.js\";\nimport { getDefaultOptions } from \"./getDefaultOptions.js\";\nimport { toDate } from \"./toDate.js\";\n\nimport { DateTimezoneSetter } from \"./parse/_lib/Setter.js\";\nimport { parsers } from \"./parse/_lib/parsers.js\";\n\n// Rexports of internal for libraries to use.\n// See: https://github.com/date-fns/date-fns/issues/3638#issuecomment-1877082874\nexport { longFormatters, parsers };\n\n/**\n * The {@link parse} function options.\n */\n\n// This RegExp consists of three parts separated by `|`:\n// - [yYQqMLwIdDecihHKkms]o matches any available ordinal number token\n//   (one of the certain letters followed by `o`)\n// - (\\w)\\1* matches any sequences of the same letter\n// - '' matches two quote characters in a row\n// - '(''|[^'])+('|$) matches anything surrounded by two quote characters ('),\n//   except a single quote symbol, which ends the sequence.\n//   Two quote characters do not end the sequence.\n//   If there is no matching single quote\n//   then the sequence will continue until the end of the string.\n// - . matches any single character unmatched by previous parts of the RegExps\nconst formattingTokensRegExp =\n  /[yYQqMLwIdDecihHKkms]o|(\\w)\\1*|''|'(''|[^'])+('|$)|./g;\n\n// This RegExp catches symbols escaped by quotes, and also\n// sequences of symbols P, p, and the combinations like `PPPPPPPppppp`\nconst longFormattingTokensRegExp = /P+p+|P+|p+|''|'(''|[^'])+('|$)|./g;\n\nconst escapedStringRegExp = /^'([^]*?)'?$/;\nconst doubleQuoteRegExp = /''/g;\n\nconst notWhitespaceRegExp = /\\S/;\nconst unescapedLatinCharacterRegExp = /[a-zA-Z]/;\n\n/**\n * @name parse\n * @category Common Helpers\n * @summary Parse the date.\n *\n * @description\n * Return the date parsed from string using the given format string.\n *\n * > ⚠️ Please note that the `format` tokens differ from Moment.js and other libraries.\n * > See: https://github.com/date-fns/date-fns/blob/master/docs/unicodeTokens.md\n *\n * The characters in the format string wrapped between two single quotes characters (') are escaped.\n * Two single quotes in a row, whether inside or outside a quoted sequence, represent a 'real' single quote.\n *\n * Format of the format string is based on Unicode Technical Standard #35:\n * https://www.unicode.org/reports/tr35/tr35-dates.html#Date_Field_Symbol_Table\n * with a few additions (see note 5 below the table).\n *\n * Not all tokens are compatible. Combinations that don't make sense or could lead to bugs are prohibited\n * and will throw `RangeError`. For example usage of 24-hour format token with AM/PM token will throw an exception:\n *\n * ```javascript\n * parse('23 AM', 'HH a', new Date())\n * //=> RangeError: The format string mustn't contain `HH` and `a` at the same time\n * ```\n *\n * See the compatibility table: https://docs.google.com/spreadsheets/d/e/2PACX-1vQOPU3xUhplll6dyoMmVUXHKl_8CRDs6_ueLmex3SoqwhuolkuN3O05l4rqx5h1dKX8eb46Ul-CCSrq/pubhtml?gid=0&single=true\n *\n * Accepted format string patterns:\n * | Unit                            |Prior| Pattern | Result examples                   | Notes |\n * |---------------------------------|-----|---------|-----------------------------------|-------|\n * | Era                             | 140 | G..GGG  | AD, BC                            |       |\n * |                                 |     | GGGG    | Anno Domini, Before Christ        | 2     |\n * |                                 |     | GGGGG   | A, B                              |       |\n * | Calendar year                   | 130 | y       | 44, 1, 1900, 2017, 9999           | 4     |\n * |                                 |     | yo      | 44th, 1st, 1900th, 9999999th      | 4,5   |\n * |                                 |     | yy      | 44, 01, 00, 17                    | 4     |\n * |                                 |     | yyy     | 044, 001, 123, 999                | 4     |\n * |                                 |     | yyyy    | 0044, 0001, 1900, 2017            | 4     |\n * |                                 |     | yyyyy   | ...                               | 2,4   |\n * | Local week-numbering year       | 130 | Y       | 44, 1, 1900, 2017, 9000           | 4     |\n * |                                 |     | Yo      | 44th, 1st, 1900th, 9999999th      | 4,5   |\n * |                                 |     | YY      | 44, 01, 00, 17                    | 4,6   |\n * |                                 |     | YYY     | 044, 001, 123, 999                | 4     |\n * |                                 |     | YYYY    | 0044, 0001, 1900, 2017            | 4,6   |\n * |                                 |     | YYYYY   | ...                               | 2,4   |\n * | ISO week-numbering year         | 130 | R       | -43, 1, 1900, 2017, 9999, -9999   | 4,5   |\n * |                                 |     | RR      | -43, 01, 00, 17                   | 4,5   |\n * |                                 |     | RRR     | -043, 001, 123, 999, -999         | 4,5   |\n * |                                 |     | RRRR    | -0043, 0001, 2017, 9999, -9999    | 4,5   |\n * |                                 |     | RRRRR   | ...                               | 2,4,5 |\n * | Extended year                   | 130 | u       | -43, 1, 1900, 2017, 9999, -999    | 4     |\n * |                                 |     | uu      | -43, 01, 99, -99                  | 4     |\n * |                                 |     | uuu     | -043, 001, 123, 999, -999         | 4     |\n * |                                 |     | uuuu    | -0043, 0001, 2017, 9999, -9999    | 4     |\n * |                                 |     | uuuuu   | ...                               | 2,4   |\n * | Quarter (formatting)            | 120 | Q       | 1, 2, 3, 4                        |       |\n * |                                 |     | Qo      | 1st, 2nd, 3rd, 4th                | 5     |\n * |                                 |     | QQ      | 01, 02, 03, 04                    |       |\n * |                                 |     | QQQ     | Q1, Q2, Q3, Q4                    |       |\n * |                                 |     | QQQQ    | 1st quarter, 2nd quarter, ...     | 2     |\n * |                                 |     | QQQQQ   | 1, 2, 3, 4                        | 4     |\n * | Quarter (stand-alone)           | 120 | q       | 1, 2, 3, 4                        |       |\n * |                                 |     | qo      | 1st, 2nd, 3rd, 4th                | 5     |\n * |                                 |     | qq      | 01, 02, 03, 04                    |       |\n * |                                 |     | qqq     | Q1, Q2, Q3, Q4                    |       |\n * |                                 |     | qqqq    | 1st quarter, 2nd quarter, ...     | 2     |\n * |                                 |     | qqqqq   | 1, 2, 3, 4                        | 3     |\n * | Month (formatting)              | 110 | M       | 1, 2, ..., 12                     |       |\n * |                                 |     | Mo      | 1st, 2nd, ..., 12th               | 5     |\n * |                                 |     | MM      | 01, 02, ..., 12                   |       |\n * |                                 |     | MMM     | Jan, Feb, ..., Dec                |       |\n * |                                 |     | MMMM    | January, February, ..., December  | 2     |\n * |                                 |     | MMMMM   | J, F, ..., D                      |       |\n * | Month (stand-alone)             | 110 | L       | 1, 2, ..., 12                     |       |\n * |                                 |     | Lo      | 1st, 2nd, ..., 12th               | 5     |\n * |                                 |     | LL      | 01, 02, ..., 12                   |       |\n * |                                 |     | LLL     | Jan, Feb, ..., Dec                |       |\n * |                                 |     | LLLL    | January, February, ..., December  | 2     |\n * |                                 |     | LLLLL   | J, F, ..., D                      |       |\n * | Local week of year              | 100 | w       | 1, 2, ..., 53                     |       |\n * |                                 |     | wo      | 1st, 2nd, ..., 53th               | 5     |\n * |                                 |     | ww      | 01, 02, ..., 53                   |       |\n * | ISO week of year                | 100 | I       | 1, 2, ..., 53                     | 5     |\n * |                                 |     | Io      | 1st, 2nd, ..., 53th               | 5     |\n * |                                 |     | II      | 01, 02, ..., 53                   | 5     |\n * | Day of month                    |  90 | d       | 1, 2, ..., 31                     |       |\n * |                                 |     | do      | 1st, 2nd, ..., 31st               | 5     |\n * |                                 |     | dd      | 01, 02, ..., 31                   |       |\n * | Day of year                     |  90 | D       | 1, 2, ..., 365, 366               | 7     |\n * |                                 |     | Do      | 1st, 2nd, ..., 365th, 366th       | 5     |\n * |                                 |     | DD      | 01, 02, ..., 365, 366             | 7     |\n * |                                 |     | DDD     | 001, 002, ..., 365, 366           |       |\n * |                                 |     | DDDD    | ...                               | 2     |\n * | Day of week (formatting)        |  90 | E..EEE  | Mon, Tue, Wed, ..., Sun           |       |\n * |                                 |     | EEEE    | Monday, Tuesday, ..., Sunday      | 2     |\n * |                                 |     | EEEEE   | M, T, W, T, F, S, S               |       |\n * |                                 |     | EEEEEE  | Mo, Tu, We, Th, Fr, Sa, Su        |       |\n * | ISO day of week (formatting)    |  90 | i       | 1, 2, 3, ..., 7                   | 5     |\n * |                                 |     | io      | 1st, 2nd, ..., 7th                | 5     |\n * |                                 |     | ii      | 01, 02, ..., 07                   | 5     |\n * |                                 |     | iii     | Mon, Tue, Wed, ..., Sun           | 5     |\n * |                                 |     | iiii    | Monday, Tuesday, ..., Sunday      | 2,5   |\n * |                                 |     | iiiii   | M, T, W, T, F, S, S               | 5     |\n * |                                 |     | iiiiii  | Mo, Tu, We, Th, Fr, Sa, Su        | 5     |\n * | Local day of week (formatting)  |  90 | e       | 2, 3, 4, ..., 1                   |       |\n * |                                 |     | eo      | 2nd, 3rd, ..., 1st                | 5     |\n * |                                 |     | ee      | 02, 03, ..., 01                   |       |\n * |                                 |     | eee     | Mon, Tue, Wed, ..., Sun           |       |\n * |                                 |     | eeee    | Monday, Tuesday, ..., Sunday      | 2     |\n * |                                 |     | eeeee   | M, T, W, T, F, S, S               |       |\n * |                                 |     | eeeeee  | Mo, Tu, We, Th, Fr, Sa, Su        |       |\n * | Local day of week (stand-alone) |  90 | c       | 2, 3, 4, ..., 1                   |       |\n * |                                 |     | co      | 2nd, 3rd, ..., 1st                | 5     |\n * |                                 |     | cc      | 02, 03, ..., 01                   |       |\n * |                                 |     | ccc     | Mon, Tue, Wed, ..., Sun           |       |\n * |                                 |     | cccc    | Monday, Tuesday, ..., Sunday      | 2     |\n * |                                 |     | ccccc   | M, T, W, T, F, S, S               |       |\n * |                                 |     | cccccc  | Mo, Tu, We, Th, Fr, Sa, Su        |       |\n * | AM, PM                          |  80 | a..aaa  | AM, PM                            |       |\n * |                                 |     | aaaa    | a.m., p.m.                        | 2     |\n * |                                 |     | aaaaa   | a, p                              |       |\n * | AM, PM, noon, midnight          |  80 | b..bbb  | AM, PM, noon, midnight            |       |\n * |                                 |     | bbbb    | a.m., p.m., noon, midnight        | 2     |\n * |                                 |     | bbbbb   | a, p, n, mi                       |       |\n * | Flexible day period             |  80 | B..BBB  | at night, in the morning, ...     |       |\n * |                                 |     | BBBB    | at night, in the morning, ...     | 2     |\n * |                                 |     | BBBBB   | at night, in the morning, ...     |       |\n * | Hour [1-12]                     |  70 | h       | 1, 2, ..., 11, 12                 |       |\n * |                                 |     | ho      | 1st, 2nd, ..., 11th, 12th         | 5     |\n * |                                 |     | hh      | 01, 02, ..., 11, 12               |       |\n * | Hour [0-23]                     |  70 | H       | 0, 1, 2, ..., 23                  |       |\n * |                                 |     | Ho      | 0th, 1st, 2nd, ..., 23rd          | 5     |\n * |                                 |     | HH      | 00, 01, 02, ..., 23               |       |\n * | Hour [0-11]                     |  70 | K       | 1, 2, ..., 11, 0                  |       |\n * |                                 |     | Ko      | 1st, 2nd, ..., 11th, 0th          | 5     |\n * |                                 |     | KK      | 01, 02, ..., 11, 00               |       |\n * | Hour [1-24]                     |  70 | k       | 24, 1, 2, ..., 23                 |       |\n * |                                 |     | ko      | 24th, 1st, 2nd, ..., 23rd         | 5     |\n * |                                 |     | kk      | 24, 01, 02, ..., 23               |       |\n * | Minute                          |  60 | m       | 0, 1, ..., 59                     |       |\n * |                                 |     | mo      | 0th, 1st, ..., 59th               | 5     |\n * |                                 |     | mm      | 00, 01, ..., 59                   |       |\n * | Second                          |  50 | s       | 0, 1, ..., 59                     |       |\n * |                                 |     | so      | 0th, 1st, ..., 59th               | 5     |\n * |                                 |     | ss      | 00, 01, ..., 59                   |       |\n * | Seconds timestamp               |  40 | t       | 512969520                         |       |\n * |                                 |     | tt      | ...                               | 2     |\n * | Fraction of second              |  30 | S       | 0, 1, ..., 9                      |       |\n * |                                 |     | SS      | 00, 01, ..., 99                   |       |\n * |                                 |     | SSS     | 000, 001, ..., 999                |       |\n * |                                 |     | SSSS    | ...                               | 2     |\n * | Milliseconds timestamp          |  20 | T       | 512969520900                      |       |\n * |                                 |     | TT      | ...                               | 2     |\n * | Timezone (ISO-8601 w/ Z)        |  10 | X       | -08, +0530, Z                     |       |\n * |                                 |     | XX      | -0800, +0530, Z                   |       |\n * |                                 |     | XXX     | -08:00, +05:30, Z                 |       |\n * |                                 |     | XXXX    | -0800, +0530, Z, +123456          | 2     |\n * |                                 |     | XXXXX   | -08:00, +05:30, Z, +12:34:56      |       |\n * | Timezone (ISO-8601 w/o Z)       |  10 | x       | -08, +0530, +00                   |       |\n * |                                 |     | xx      | -0800, +0530, +0000               |       |\n * |                                 |     | xxx     | -08:00, +05:30, +00:00            | 2     |\n * |                                 |     | xxxx    | -0800, +0530, +0000, +123456      |       |\n * |                                 |     | xxxxx   | -08:00, +05:30, +00:00, +12:34:56 |       |\n * | Long localized date             |  NA | P       | 05/29/1453                        | 5,8   |\n * |                                 |     | PP      | May 29, 1453                      |       |\n * |                                 |     | PPP     | May 29th, 1453                    |       |\n * |                                 |     | PPPP    | Sunday, May 29th, 1453            | 2,5,8 |\n * | Long localized time             |  NA | p       | 12:00 AM                          | 5,8   |\n * |                                 |     | pp      | 12:00:00 AM                       |       |\n * | Combination of date and time    |  NA | Pp      | 05/29/1453, 12:00 AM              |       |\n * |                                 |     | PPpp    | May 29, 1453, 12:00:00 AM         |       |\n * |                                 |     | PPPpp   | May 29th, 1453 at ...             |       |\n * |                                 |     | PPPPpp  | Sunday, May 29th, 1453 at ...     | 2,5,8 |\n * Notes:\n * 1. \"Formatting\" units (e.g. formatting quarter) in the default en-US locale\n *    are the same as \"stand-alone\" units, but are different in some languages.\n *    \"Formatting\" units are declined according to the rules of the language\n *    in the context of a date. \"Stand-alone\" units are always nominative singular.\n *    In `format` function, they will produce different result:\n *\n *    `format(new Date(2017, 10, 6), 'do LLLL', {locale: cs}) //=> '6. listopad'`\n *\n *    `format(new Date(2017, 10, 6), 'do MMMM', {locale: cs}) //=> '6. listopadu'`\n *\n *    `parse` will try to match both formatting and stand-alone units interchangeably.\n *\n * 2. Any sequence of the identical letters is a pattern, unless it is escaped by\n *    the single quote characters (see below).\n *    If the sequence is longer than listed in table:\n *    - for numerical units (`yyyyyyyy`) `parse` will try to match a number\n *      as wide as the sequence\n *    - for text units (`MMMMMMMM`) `parse` will try to match the widest variation of the unit.\n *      These variations are marked with \"2\" in the last column of the table.\n *\n * 3. `QQQQQ` and `qqqqq` could be not strictly numerical in some locales.\n *    These tokens represent the shortest form of the quarter.\n *\n * 4. The main difference between `y` and `u` patterns are B.C. years:\n *\n *    | Year | `y` | `u` |\n *    |------|-----|-----|\n *    | AC 1 |   1 |   1 |\n *    | BC 1 |   1 |   0 |\n *    | BC 2 |   2 |  -1 |\n *\n *    Also `yy` will try to guess the century of two digit year by proximity with `referenceDate`:\n *\n *    `parse('50', 'yy', new Date(2018, 0, 1)) //=> Sat Jan 01 2050 00:00:00`\n *\n *    `parse('75', 'yy', new Date(2018, 0, 1)) //=> Wed Jan 01 1975 00:00:00`\n *\n *    while `uu` will just assign the year as is:\n *\n *    `parse('50', 'uu', new Date(2018, 0, 1)) //=> Sat Jan 01 0050 00:00:00`\n *\n *    `parse('75', 'uu', new Date(2018, 0, 1)) //=> Tue Jan 01 0075 00:00:00`\n *\n *    The same difference is true for local and ISO week-numbering years (`Y` and `R`),\n *    except local week-numbering years are dependent on `options.weekStartsOn`\n *    and `options.firstWeekContainsDate` (compare [setISOWeekYear](https://date-fns.org/docs/setISOWeekYear)\n *    and [setWeekYear](https://date-fns.org/docs/setWeekYear)).\n *\n * 5. These patterns are not in the Unicode Technical Standard #35:\n *    - `i`: ISO day of week\n *    - `I`: ISO week of year\n *    - `R`: ISO week-numbering year\n *    - `o`: ordinal number modifier\n *    - `P`: long localized date\n *    - `p`: long localized time\n *\n * 6. `YY` and `YYYY` tokens represent week-numbering years but they are often confused with years.\n *    You should enable `options.useAdditionalWeekYearTokens` to use them. See: https://github.com/date-fns/date-fns/blob/master/docs/unicodeTokens.md\n *\n * 7. `D` and `DD` tokens represent days of the year but they are often confused with days of the month.\n *    You should enable `options.useAdditionalDayOfYearTokens` to use them. See: https://github.com/date-fns/date-fns/blob/master/docs/unicodeTokens.md\n *\n * 8. `P+` tokens do not have a defined priority since they are merely aliases to other tokens based\n *    on the given locale.\n *\n *    using `en-US` locale: `P` => `MM/dd/yyyy`\n *    using `en-US` locale: `p` => `hh:mm a`\n *    using `pt-BR` locale: `P` => `dd/MM/yyyy`\n *    using `pt-BR` locale: `p` => `HH:mm`\n *\n * Values will be assigned to the date in the descending order of its unit's priority.\n * Units of an equal priority overwrite each other in the order of appearance.\n *\n * If no values of higher priority are parsed (e.g. when parsing string 'January 1st' without a year),\n * the values will be taken from 3rd argument `referenceDate` which works as a context of parsing.\n *\n * `referenceDate` must be passed for correct work of the function.\n * If you're not sure which `referenceDate` to supply, create a new instance of Date:\n * `parse('02/11/2014', 'MM/dd/yyyy', new Date())`\n * In this case parsing will be done in the context of the current date.\n * If `referenceDate` is `Invalid Date` or a value not convertible to valid `Date`,\n * then `Invalid Date` will be returned.\n *\n * The result may vary by locale.\n *\n * If `formatString` matches with `dateString` but does not provides tokens, `referenceDate` will be returned.\n *\n * If parsing failed, `Invalid Date` will be returned.\n * Invalid Date is a Date, whose time value is NaN.\n * Time value of Date: http://es5.github.io/#x15.9.1.1\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n * @typeParam ResultDate - The result `Date` type, it is the type returned from the context function if it is passed, or inferred from the arguments.\n *\n * @param dateStr - The string to parse\n * @param formatStr - The string of tokens\n * @param referenceDate - defines values missing from the parsed dateString\n * @param options - An object with options.\n *   see: https://github.com/date-fns/date-fns/blob/master/docs/unicodeTokens.md\n *   see: https://github.com/date-fns/date-fns/blob/master/docs/unicodeTokens.md\n *\n * @returns The parsed date\n *\n * @throws `options.locale` must contain `match` property\n * @throws use `yyyy` instead of `YYYY` for formatting years using [format provided] to the input [input provided]; see: https://github.com/date-fns/date-fns/blob/master/docs/unicodeTokens.md\n * @throws use `yy` instead of `YY` for formatting years using [format provided] to the input [input provided]; see: https://github.com/date-fns/date-fns/blob/master/docs/unicodeTokens.md\n * @throws use `d` instead of `D` for formatting days of the month using [format provided] to the input [input provided]; see: https://github.com/date-fns/date-fns/blob/master/docs/unicodeTokens.md\n * @throws use `dd` instead of `DD` for formatting days of the month using [format provided] to the input [input provided]; see: https://github.com/date-fns/date-fns/blob/master/docs/unicodeTokens.md\n * @throws format string contains an unescaped latin alphabet character\n *\n * @example\n * // Parse 11 February 2014 from middle-endian format:\n * var result = parse('02/11/2014', 'MM/dd/yyyy', new Date())\n * //=> Tue Feb 11 2014 00:00:00\n *\n * @example\n * // Parse 28th of February in Esperanto locale in the context of 2010 year:\n * import eo from 'date-fns/locale/eo'\n * var result = parse('28-a de februaro', \"do 'de' MMMM\", new Date(2010, 0, 1), {\n *   locale: eo\n * })\n * //=> Sun Feb 28 2010 00:00:00\n */\nexport function parse(dateStr, formatStr, referenceDate, options) {\n  const invalidDate = () => constructFrom(options?.in || referenceDate, NaN);\n  const defaultOptions = getDefaultOptions();\n  const locale = options?.locale ?? defaultOptions.locale ?? defaultLocale;\n\n  const firstWeekContainsDate =\n    options?.firstWeekContainsDate ??\n    options?.locale?.options?.firstWeekContainsDate ??\n    defaultOptions.firstWeekContainsDate ??\n    defaultOptions.locale?.options?.firstWeekContainsDate ??\n    1;\n\n  const weekStartsOn =\n    options?.weekStartsOn ??\n    options?.locale?.options?.weekStartsOn ??\n    defaultOptions.weekStartsOn ??\n    defaultOptions.locale?.options?.weekStartsOn ??\n    0;\n\n  if (!formatStr)\n    return dateStr ? invalidDate() : toDate(referenceDate, options?.in);\n\n  const subFnOptions = {\n    firstWeekContainsDate,\n    weekStartsOn,\n    locale,\n  };\n\n  // If timezone isn't specified, it will try to use the context or\n  // the reference date and fallback to the system time zone.\n  const setters = [new DateTimezoneSetter(options?.in, referenceDate)];\n\n  const tokens = formatStr\n    .match(longFormattingTokensRegExp)\n    .map((substring) => {\n      const firstCharacter = substring[0];\n      if (firstCharacter in longFormatters) {\n        const longFormatter = longFormatters[firstCharacter];\n        return longFormatter(substring, locale.formatLong);\n      }\n      return substring;\n    })\n    .join(\"\")\n    .match(formattingTokensRegExp);\n\n  const usedTokens = [];\n\n  for (let token of tokens) {\n    if (\n      !options?.useAdditionalWeekYearTokens &&\n      isProtectedWeekYearToken(token)\n    ) {\n      warnOrThrowProtectedError(token, formatStr, dateStr);\n    }\n    if (\n      !options?.useAdditionalDayOfYearTokens &&\n      isProtectedDayOfYearToken(token)\n    ) {\n      warnOrThrowProtectedError(token, formatStr, dateStr);\n    }\n\n    const firstCharacter = token[0];\n    const parser = parsers[firstCharacter];\n    if (parser) {\n      const { incompatibleTokens } = parser;\n      if (Array.isArray(incompatibleTokens)) {\n        const incompatibleToken = usedTokens.find(\n          (usedToken) =>\n            incompatibleTokens.includes(usedToken.token) ||\n            usedToken.token === firstCharacter,\n        );\n        if (incompatibleToken) {\n          throw new RangeError(\n            `The format string mustn't contain \\`${incompatibleToken.fullToken}\\` and \\`${token}\\` at the same time`,\n          );\n        }\n      } else if (parser.incompatibleTokens === \"*\" && usedTokens.length > 0) {\n        throw new RangeError(\n          `The format string mustn't contain \\`${token}\\` and any other token at the same time`,\n        );\n      }\n\n      usedTokens.push({ token: firstCharacter, fullToken: token });\n\n      const parseResult = parser.run(\n        dateStr,\n        token,\n        locale.match,\n        subFnOptions,\n      );\n\n      if (!parseResult) {\n        return invalidDate();\n      }\n\n      setters.push(parseResult.setter);\n\n      dateStr = parseResult.rest;\n    } else {\n      if (firstCharacter.match(unescapedLatinCharacterRegExp)) {\n        throw new RangeError(\n          \"Format string contains an unescaped latin alphabet character `\" +\n            firstCharacter +\n            \"`\",\n        );\n      }\n\n      // Replace two single quote characters with one single quote character\n      if (token === \"''\") {\n        token = \"'\";\n      } else if (firstCharacter === \"'\") {\n        token = cleanEscapedString(token);\n      }\n\n      // Cut token from string, or, if string doesn't match the token, return Invalid Date\n      if (dateStr.indexOf(token) === 0) {\n        dateStr = dateStr.slice(token.length);\n      } else {\n        return invalidDate();\n      }\n    }\n  }\n\n  // Check if the remaining input contains something other than whitespace\n  if (dateStr.length > 0 && notWhitespaceRegExp.test(dateStr)) {\n    return invalidDate();\n  }\n\n  const uniquePrioritySetters = setters\n    .map((setter) => setter.priority)\n    .sort((a, b) => b - a)\n    .filter((priority, index, array) => array.indexOf(priority) === index)\n    .map((priority) =>\n      setters\n        .filter((setter) => setter.priority === priority)\n        .sort((a, b) => b.subPriority - a.subPriority),\n    )\n    .map((setterArray) => setterArray[0]);\n\n  let date = toDate(referenceDate, options?.in);\n\n  if (isNaN(+date)) return invalidDate();\n\n  const flags = {};\n  for (const setter of uniquePrioritySetters) {\n    if (!setter.validate(date, subFnOptions)) {\n      return invalidDate();\n    }\n\n    const result = setter.set(date, flags, subFnOptions);\n    // Result is tuple (date, flags)\n    if (Array.isArray(result)) {\n      date = result[0];\n      Object.assign(flags, result[1]);\n      // Result is date\n    } else {\n      date = result;\n    }\n  }\n\n  return date;\n}\n\nfunction cleanEscapedString(input) {\n  return input.match(escapedStringRegExp)[1].replace(doubleQuoteRegExp, \"'\");\n}\n\n// Fallback for modularized imports:\nexport default parse;\n"], "mappings": "AAAA,SAASA,aAAa,QAAQ,yBAAyB;AACvD,SAASC,cAAc,QAAQ,iCAAiC;AAChE,SACEC,yBAAyB,EACzBC,wBAAwB,EACxBC,yBAAyB,QACpB,2BAA2B;AAClC,SAASC,aAAa,QAAQ,oBAAoB;AAClD,SAASC,iBAAiB,QAAQ,wBAAwB;AAC1D,SAASC,MAAM,QAAQ,aAAa;AAEpC,SAASC,kBAAkB,QAAQ,wBAAwB;AAC3D,SAASC,OAAO,QAAQ,yBAAyB;;AAEjD;AACA;AACA,SAASR,cAAc,EAAEQ,OAAO;;AAEhC;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,sBAAsB,GAC1B,uDAAuD;;AAEzD;AACA;AACA,MAAMC,0BAA0B,GAAG,mCAAmC;AAEtE,MAAMC,mBAAmB,GAAG,cAAc;AAC1C,MAAMC,iBAAiB,GAAG,KAAK;AAE/B,MAAMC,mBAAmB,GAAG,IAAI;AAChC,MAAMC,6BAA6B,GAAG,UAAU;;AAEhD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,KAAKA,CAACC,OAAO,EAAEC,SAAS,EAAEC,aAAa,EAAEC,OAAO,EAAE;EAChE,MAAMC,WAAW,GAAGA,CAAA,KAAMhB,aAAa,CAACe,OAAO,EAAEE,EAAE,IAAIH,aAAa,EAAEI,GAAG,CAAC;EAC1E,MAAMC,cAAc,GAAGlB,iBAAiB,CAAC,CAAC;EAC1C,MAAMmB,MAAM,GAAGL,OAAO,EAAEK,MAAM,IAAID,cAAc,CAACC,MAAM,IAAIzB,aAAa;EAExE,MAAM0B,qBAAqB,GACzBN,OAAO,EAAEM,qBAAqB,IAC9BN,OAAO,EAAEK,MAAM,EAAEL,OAAO,EAAEM,qBAAqB,IAC/CF,cAAc,CAACE,qBAAqB,IACpCF,cAAc,CAACC,MAAM,EAAEL,OAAO,EAAEM,qBAAqB,IACrD,CAAC;EAEH,MAAMC,YAAY,GAChBP,OAAO,EAAEO,YAAY,IACrBP,OAAO,EAAEK,MAAM,EAAEL,OAAO,EAAEO,YAAY,IACtCH,cAAc,CAACG,YAAY,IAC3BH,cAAc,CAACC,MAAM,EAAEL,OAAO,EAAEO,YAAY,IAC5C,CAAC;EAEH,IAAI,CAACT,SAAS,EACZ,OAAOD,OAAO,GAAGI,WAAW,CAAC,CAAC,GAAGd,MAAM,CAACY,aAAa,EAAEC,OAAO,EAAEE,EAAE,CAAC;EAErE,MAAMM,YAAY,GAAG;IACnBF,qBAAqB;IACrBC,YAAY;IACZF;EACF,CAAC;;EAED;EACA;EACA,MAAMI,OAAO,GAAG,CAAC,IAAIrB,kBAAkB,CAACY,OAAO,EAAEE,EAAE,EAAEH,aAAa,CAAC,CAAC;EAEpE,MAAMW,MAAM,GAAGZ,SAAS,CACrBa,KAAK,CAACpB,0BAA0B,CAAC,CACjCqB,GAAG,CAAEC,SAAS,IAAK;IAClB,MAAMC,cAAc,GAAGD,SAAS,CAAC,CAAC,CAAC;IACnC,IAAIC,cAAc,IAAIjC,cAAc,EAAE;MACpC,MAAMkC,aAAa,GAAGlC,cAAc,CAACiC,cAAc,CAAC;MACpD,OAAOC,aAAa,CAACF,SAAS,EAAER,MAAM,CAACW,UAAU,CAAC;IACpD;IACA,OAAOH,SAAS;EAClB,CAAC,CAAC,CACDI,IAAI,CAAC,EAAE,CAAC,CACRN,KAAK,CAACrB,sBAAsB,CAAC;EAEhC,MAAM4B,UAAU,GAAG,EAAE;EAErB,KAAK,IAAIC,KAAK,IAAIT,MAAM,EAAE;IACxB,IACE,CAACV,OAAO,EAAEoB,2BAA2B,IACrCrC,wBAAwB,CAACoC,KAAK,CAAC,EAC/B;MACAnC,yBAAyB,CAACmC,KAAK,EAAErB,SAAS,EAAED,OAAO,CAAC;IACtD;IACA,IACE,CAACG,OAAO,EAAEqB,4BAA4B,IACtCvC,yBAAyB,CAACqC,KAAK,CAAC,EAChC;MACAnC,yBAAyB,CAACmC,KAAK,EAAErB,SAAS,EAAED,OAAO,CAAC;IACtD;IAEA,MAAMiB,cAAc,GAAGK,KAAK,CAAC,CAAC,CAAC;IAC/B,MAAMG,MAAM,GAAGjC,OAAO,CAACyB,cAAc,CAAC;IACtC,IAAIQ,MAAM,EAAE;MACV,MAAM;QAAEC;MAAmB,CAAC,GAAGD,MAAM;MACrC,IAAIE,KAAK,CAACC,OAAO,CAACF,kBAAkB,CAAC,EAAE;QACrC,MAAMG,iBAAiB,GAAGR,UAAU,CAACS,IAAI,CACtCC,SAAS,IACRL,kBAAkB,CAACM,QAAQ,CAACD,SAAS,CAACT,KAAK,CAAC,IAC5CS,SAAS,CAACT,KAAK,KAAKL,cACxB,CAAC;QACD,IAAIY,iBAAiB,EAAE;UACrB,MAAM,IAAII,UAAU,CAClB,uCAAuCJ,iBAAiB,CAACK,SAAS,YAAYZ,KAAK,qBACrF,CAAC;QACH;MACF,CAAC,MAAM,IAAIG,MAAM,CAACC,kBAAkB,KAAK,GAAG,IAAIL,UAAU,CAACc,MAAM,GAAG,CAAC,EAAE;QACrE,MAAM,IAAIF,UAAU,CAClB,uCAAuCX,KAAK,yCAC9C,CAAC;MACH;MAEAD,UAAU,CAACe,IAAI,CAAC;QAAEd,KAAK,EAAEL,cAAc;QAAEiB,SAAS,EAAEZ;MAAM,CAAC,CAAC;MAE5D,MAAMe,WAAW,GAAGZ,MAAM,CAACa,GAAG,CAC5BtC,OAAO,EACPsB,KAAK,EACLd,MAAM,CAACM,KAAK,EACZH,YACF,CAAC;MAED,IAAI,CAAC0B,WAAW,EAAE;QAChB,OAAOjC,WAAW,CAAC,CAAC;MACtB;MAEAQ,OAAO,CAACwB,IAAI,CAACC,WAAW,CAACE,MAAM,CAAC;MAEhCvC,OAAO,GAAGqC,WAAW,CAACG,IAAI;IAC5B,CAAC,MAAM;MACL,IAAIvB,cAAc,CAACH,KAAK,CAAChB,6BAA6B,CAAC,EAAE;QACvD,MAAM,IAAImC,UAAU,CAClB,gEAAgE,GAC9DhB,cAAc,GACd,GACJ,CAAC;MACH;;MAEA;MACA,IAAIK,KAAK,KAAK,IAAI,EAAE;QAClBA,KAAK,GAAG,GAAG;MACb,CAAC,MAAM,IAAIL,cAAc,KAAK,GAAG,EAAE;QACjCK,KAAK,GAAGmB,kBAAkB,CAACnB,KAAK,CAAC;MACnC;;MAEA;MACA,IAAItB,OAAO,CAAC0C,OAAO,CAACpB,KAAK,CAAC,KAAK,CAAC,EAAE;QAChCtB,OAAO,GAAGA,OAAO,CAAC2C,KAAK,CAACrB,KAAK,CAACa,MAAM,CAAC;MACvC,CAAC,MAAM;QACL,OAAO/B,WAAW,CAAC,CAAC;MACtB;IACF;EACF;;EAEA;EACA,IAAIJ,OAAO,CAACmC,MAAM,GAAG,CAAC,IAAItC,mBAAmB,CAAC+C,IAAI,CAAC5C,OAAO,CAAC,EAAE;IAC3D,OAAOI,WAAW,CAAC,CAAC;EACtB;EAEA,MAAMyC,qBAAqB,GAAGjC,OAAO,CAClCG,GAAG,CAAEwB,MAAM,IAAKA,MAAM,CAACO,QAAQ,CAAC,CAChCC,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKA,CAAC,GAAGD,CAAC,CAAC,CACrBE,MAAM,CAAC,CAACJ,QAAQ,EAAEK,KAAK,EAAEC,KAAK,KAAKA,KAAK,CAACV,OAAO,CAACI,QAAQ,CAAC,KAAKK,KAAK,CAAC,CACrEpC,GAAG,CAAE+B,QAAQ,IACZlC,OAAO,CACJsC,MAAM,CAAEX,MAAM,IAAKA,MAAM,CAACO,QAAQ,KAAKA,QAAQ,CAAC,CAChDC,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKA,CAAC,CAACI,WAAW,GAAGL,CAAC,CAACK,WAAW,CACjD,CAAC,CACAtC,GAAG,CAAEuC,WAAW,IAAKA,WAAW,CAAC,CAAC,CAAC,CAAC;EAEvC,IAAIC,IAAI,GAAGjE,MAAM,CAACY,aAAa,EAAEC,OAAO,EAAEE,EAAE,CAAC;EAE7C,IAAImD,KAAK,CAAC,CAACD,IAAI,CAAC,EAAE,OAAOnD,WAAW,CAAC,CAAC;EAEtC,MAAMqD,KAAK,GAAG,CAAC,CAAC;EAChB,KAAK,MAAMlB,MAAM,IAAIM,qBAAqB,EAAE;IAC1C,IAAI,CAACN,MAAM,CAACmB,QAAQ,CAACH,IAAI,EAAE5C,YAAY,CAAC,EAAE;MACxC,OAAOP,WAAW,CAAC,CAAC;IACtB;IAEA,MAAMuD,MAAM,GAAGpB,MAAM,CAACqB,GAAG,CAACL,IAAI,EAAEE,KAAK,EAAE9C,YAAY,CAAC;IACpD;IACA,IAAIgB,KAAK,CAACC,OAAO,CAAC+B,MAAM,CAAC,EAAE;MACzBJ,IAAI,GAAGI,MAAM,CAAC,CAAC,CAAC;MAChBE,MAAM,CAACC,MAAM,CAACL,KAAK,EAAEE,MAAM,CAAC,CAAC,CAAC,CAAC;MAC/B;IACF,CAAC,MAAM;MACLJ,IAAI,GAAGI,MAAM;IACf;EACF;EAEA,OAAOJ,IAAI;AACb;AAEA,SAASd,kBAAkBA,CAACsB,KAAK,EAAE;EACjC,OAAOA,KAAK,CAACjD,KAAK,CAACnB,mBAAmB,CAAC,CAAC,CAAC,CAAC,CAACqE,OAAO,CAACpE,iBAAiB,EAAE,GAAG,CAAC;AAC5E;;AAEA;AACA,eAAeG,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}