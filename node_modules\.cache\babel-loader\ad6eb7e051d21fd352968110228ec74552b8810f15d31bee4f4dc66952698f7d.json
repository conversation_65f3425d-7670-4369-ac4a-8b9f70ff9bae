{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\lostandfound\\\\discfinder-app\\\\src\\\\components\\\\ProfileManager.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { supabase } from '../lib/supabase';\nimport AvatarUpload from './AvatarUpload';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ProfileManager = ({\n  userId,\n  onProfileUpdate\n}) => {\n  _s();\n  const [profile, setProfile] = useState(null);\n  const [isLoading, setIsLoading] = useState(true);\n  const [isSaving, setIsSaving] = useState(false);\n  const [error, setError] = useState(null);\n  const [successMessage, setSuccessMessage] = useState(null);\n\n  // Form state\n  const [formData, setFormData] = useState({\n    full_name: '',\n    phone: '',\n    pdga_number: '',\n    facebook_profile: '',\n    instagram_handle: '',\n    sms_number: '',\n    phone_number: ''\n  });\n  useEffect(() => {\n    loadProfile();\n  }, [userId]); // eslint-disable-line react-hooks/exhaustive-deps\n\n  const loadProfile = async () => {\n    try {\n      var _data$pdga_number;\n      setIsLoading(true);\n      const {\n        data,\n        error\n      } = await supabase.from('profiles').select('*').eq('id', userId).single();\n      if (error) {\n        throw error;\n      }\n      setProfile(data);\n      setFormData({\n        full_name: data.full_name || '',\n        phone: data.phone || '',\n        pdga_number: ((_data$pdga_number = data.pdga_number) === null || _data$pdga_number === void 0 ? void 0 : _data$pdga_number.toString()) || '',\n        facebook_profile: data.facebook_profile || '',\n        instagram_handle: data.instagram_handle || '',\n        sms_number: data.sms_number || '',\n        phone_number: data.phone_number || ''\n      });\n    } catch (err) {\n      console.error('Error loading profile:', err);\n      setError(err instanceof Error ? err.message : 'Failed to load profile');\n    } finally {\n      setIsLoading(false);\n    }\n  };\n  const handleInputChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: value\n    }));\n  };\n  const handleSubmit = async e => {\n    e.preventDefault();\n    setIsSaving(true);\n    setError(null);\n    setSuccessMessage(null);\n    try {\n      const updateData = {\n        full_name: formData.full_name,\n        phone: formData.phone,\n        pdga_number: formData.pdga_number ? parseInt(formData.pdga_number) : null,\n        facebook_profile: formData.facebook_profile,\n        instagram_handle: formData.instagram_handle,\n        sms_number: formData.sms_number,\n        phone_number: formData.phone_number,\n        updated_at: new Date().toISOString()\n      };\n      const {\n        data,\n        error\n      } = await supabase.from('profiles').update(updateData).eq('id', userId).select().single();\n      if (error) {\n        throw error;\n      }\n      setProfile(data);\n      setSuccessMessage('Profile updated successfully!');\n      onProfileUpdate === null || onProfileUpdate === void 0 ? void 0 : onProfileUpdate(data);\n\n      // Clear success message after 3 seconds\n      setTimeout(() => setSuccessMessage(null), 3000);\n    } catch (err) {\n      console.error('Error updating profile:', err);\n      setError(err instanceof Error ? err.message : 'Failed to update profile');\n    } finally {\n      setIsSaving(false);\n    }\n  };\n  const handleAvatarUpdate = newAvatarUrl => {\n    if (profile) {\n      const updatedProfile = {\n        ...profile,\n        avatar_url: newAvatarUrl\n      };\n      setProfile(updatedProfile);\n      onProfileUpdate === null || onProfileUpdate === void 0 ? void 0 : onProfileUpdate(updatedProfile);\n    }\n  };\n  if (isLoading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex justify-center items-center p-8\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 122,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 121,\n      columnNumber: 7\n    }, this);\n  }\n  if (!profile) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"text-center p-8\",\n      children: /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-red-600\",\n        children: \"Failed to load profile\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 130,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 129,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"max-w-2xl mx-auto p-6\",\n    children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n      className: \"text-2xl font-bold text-gray-900 mb-6\",\n      children: \"Profile Settings\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 137,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white shadow rounded-lg p-6 mb-6\",\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        className: \"text-lg font-medium text-gray-900 mb-4\",\n        children: \"Profile Photo\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 141,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(AvatarUpload, {\n        currentAvatarUrl: profile.avatar_url,\n        userId: userId,\n        onAvatarUpdate: handleAvatarUpdate\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 142,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 140,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white shadow rounded-lg p-6\",\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        className: \"text-lg font-medium text-gray-900 mb-4\",\n        children: \"Profile Information\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 151,\n        columnNumber: 9\n      }, this), error && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mb-4 p-4 bg-red-50 border border-red-200 rounded-md\",\n        children: /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-red-600 text-sm\",\n          children: error\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 155,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 154,\n        columnNumber: 11\n      }, this), successMessage && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mb-4 p-4 bg-green-50 border border-green-200 rounded-md\",\n        children: /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-green-600 text-sm\",\n          children: successMessage\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 161,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 160,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n        onSubmit: handleSubmit,\n        className: \"space-y-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"full_name\",\n              className: \"block text-sm font-medium text-gray-700 mb-1\",\n              children: \"Full Name\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 169,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              id: \"full_name\",\n              name: \"full_name\",\n              value: formData.full_name,\n              onChange: handleInputChange,\n              className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 172,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 168,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"pdga_number\",\n              className: \"block text-sm font-medium text-gray-700 mb-1\",\n              children: \"PDGA Number\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 183,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"number\",\n              id: \"pdga_number\",\n              name: \"pdga_number\",\n              value: formData.pdga_number,\n              onChange: handleInputChange,\n              placeholder: \"12345\",\n              className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 186,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 182,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 167,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"phone_number\",\n              className: \"block text-sm font-medium text-gray-700 mb-1\",\n              children: \"Phone Number\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 201,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"tel\",\n              id: \"phone_number\",\n              name: \"phone_number\",\n              value: formData.phone_number,\n              onChange: handleInputChange,\n              placeholder: \"+****************\",\n              className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 204,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 200,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"sms_number\",\n              className: \"block text-sm font-medium text-gray-700 mb-1\",\n              children: \"SMS Number\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 216,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"tel\",\n              id: \"sms_number\",\n              name: \"sms_number\",\n              value: formData.sms_number,\n              onChange: handleInputChange,\n              placeholder: \"+****************\",\n              className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 219,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 215,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 199,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            htmlFor: \"phone\",\n            className: \"block text-sm font-medium text-gray-700 mb-1\",\n            children: \"Phone (Legacy)\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 233,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"tel\",\n            id: \"phone\",\n            name: \"phone\",\n            value: formData.phone,\n            onChange: handleInputChange,\n            className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 236,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 232,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"facebook_profile\",\n              className: \"block text-sm font-medium text-gray-700 mb-1\",\n              children: \"Facebook Profile\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 249,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              id: \"facebook_profile\",\n              name: \"facebook_profile\",\n              value: formData.facebook_profile,\n              onChange: handleInputChange,\n              placeholder: \"facebook.com/username or username\",\n              className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 252,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 248,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"instagram_handle\",\n              className: \"block text-sm font-medium text-gray-700 mb-1\",\n              children: \"Instagram Handle\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 264,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              id: \"instagram_handle\",\n              name: \"instagram_handle\",\n              value: formData.instagram_handle,\n              onChange: handleInputChange,\n              placeholder: \"username (without @)\",\n              className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 267,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 263,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 247,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"pt-4\",\n          children: /*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"submit\",\n            disabled: isSaving,\n            className: \"w-full bg-blue-600 hover:bg-blue-700 disabled:bg-blue-300 text-white py-2 px-4 rounded-md font-medium\",\n            children: isSaving ? 'Saving...' : 'Save Profile'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 281,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 280,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 165,\n        columnNumber: 9\n      }, this), profile.legacy_row_id && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mt-6 pt-6 border-t border-gray-200\",\n        children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n          className: \"text-sm font-medium text-gray-700 mb-2\",\n          children: \"Import Information\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 294,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-sm text-gray-500\",\n          children: [\"Legacy ID: \", profile.legacy_row_id]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 295,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-sm text-gray-500\",\n          children: [\"Role: \", profile.role]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 298,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 293,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 150,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 136,\n    columnNumber: 5\n  }, this);\n};\n_s(ProfileManager, \"T3JKyiGzhfuFYhDH+DU0HgsUCFI=\");\n_c = ProfileManager;\nexport default ProfileManager;\nvar _c;\n$RefreshReg$(_c, \"ProfileManager\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "supabase", "AvatarUpload", "jsxDEV", "_jsxDEV", "ProfileManager", "userId", "onProfileUpdate", "_s", "profile", "setProfile", "isLoading", "setIsLoading", "isSaving", "setIsSaving", "error", "setError", "successMessage", "setSuccessMessage", "formData", "setFormData", "full_name", "phone", "pdga_number", "facebook_profile", "instagram_handle", "sms_number", "phone_number", "loadProfile", "_data$pdga_number", "data", "from", "select", "eq", "single", "toString", "err", "console", "Error", "message", "handleInputChange", "e", "name", "value", "target", "prev", "handleSubmit", "preventDefault", "updateData", "parseInt", "updated_at", "Date", "toISOString", "update", "setTimeout", "handleAvatarUpdate", "newAvatarUrl", "updatedProfile", "avatar_url", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "currentAvatarUrl", "onAvatarUpdate", "onSubmit", "htmlFor", "type", "id", "onChange", "placeholder", "disabled", "legacy_row_id", "role", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/lostandfound/discfinder-app/src/components/ProfileManager.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { supabase, Profile } from '../lib/supabase';\nimport AvatarUpload from './AvatarUpload';\n\ninterface ProfileManagerProps {\n  userId: string;\n  onProfileUpdate?: (profile: Profile) => void;\n}\n\nconst ProfileManager: React.FC<ProfileManagerProps> = ({ userId, onProfileUpdate }) => {\n  const [profile, setProfile] = useState<Profile | null>(null);\n  const [isLoading, setIsLoading] = useState(true);\n  const [isSaving, setIsSaving] = useState(false);\n  const [error, setError] = useState<string | null>(null);\n  const [successMessage, setSuccessMessage] = useState<string | null>(null);\n\n  // Form state\n  const [formData, setFormData] = useState({\n    full_name: '',\n    phone: '',\n    pdga_number: '',\n    facebook_profile: '',\n    instagram_handle: '',\n    sms_number: '',\n    phone_number: '',\n  });\n\n  useEffect(() => {\n    loadProfile();\n  }, [userId]); // eslint-disable-line react-hooks/exhaustive-deps\n\n  const loadProfile = async () => {\n    try {\n      setIsLoading(true);\n      const { data, error } = await supabase\n        .from('profiles')\n        .select('*')\n        .eq('id', userId)\n        .single();\n\n      if (error) {\n        throw error;\n      }\n\n      setProfile(data);\n      setFormData({\n        full_name: data.full_name || '',\n        phone: data.phone || '',\n        pdga_number: data.pdga_number?.toString() || '',\n        facebook_profile: data.facebook_profile || '',\n        instagram_handle: data.instagram_handle || '',\n        sms_number: data.sms_number || '',\n        phone_number: data.phone_number || '',\n      });\n    } catch (err) {\n      console.error('Error loading profile:', err);\n      setError(err instanceof Error ? err.message : 'Failed to load profile');\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {\n    const { name, value } = e.target;\n    setFormData(prev => ({ ...prev, [name]: value }));\n  };\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n    setIsSaving(true);\n    setError(null);\n    setSuccessMessage(null);\n\n    try {\n      const updateData = {\n        full_name: formData.full_name,\n        phone: formData.phone,\n        pdga_number: formData.pdga_number ? parseInt(formData.pdga_number) : null,\n        facebook_profile: formData.facebook_profile,\n        instagram_handle: formData.instagram_handle,\n        sms_number: formData.sms_number,\n        phone_number: formData.phone_number,\n        updated_at: new Date().toISOString(),\n      };\n\n      const { data, error } = await supabase\n        .from('profiles')\n        .update(updateData)\n        .eq('id', userId)\n        .select()\n        .single();\n\n      if (error) {\n        throw error;\n      }\n\n      setProfile(data);\n      setSuccessMessage('Profile updated successfully!');\n      onProfileUpdate?.(data);\n\n      // Clear success message after 3 seconds\n      setTimeout(() => setSuccessMessage(null), 3000);\n    } catch (err) {\n      console.error('Error updating profile:', err);\n      setError(err instanceof Error ? err.message : 'Failed to update profile');\n    } finally {\n      setIsSaving(false);\n    }\n  };\n\n  const handleAvatarUpdate = (newAvatarUrl: string) => {\n    if (profile) {\n      const updatedProfile = { ...profile, avatar_url: newAvatarUrl };\n      setProfile(updatedProfile);\n      onProfileUpdate?.(updatedProfile);\n    }\n  };\n\n  if (isLoading) {\n    return (\n      <div className=\"flex justify-center items-center p-8\">\n        <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600\"></div>\n      </div>\n    );\n  }\n\n  if (!profile) {\n    return (\n      <div className=\"text-center p-8\">\n        <p className=\"text-red-600\">Failed to load profile</p>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"max-w-2xl mx-auto p-6\">\n      <h2 className=\"text-2xl font-bold text-gray-900 mb-6\">Profile Settings</h2>\n\n      {/* Avatar Section */}\n      <div className=\"bg-white shadow rounded-lg p-6 mb-6\">\n        <h3 className=\"text-lg font-medium text-gray-900 mb-4\">Profile Photo</h3>\n        <AvatarUpload\n          currentAvatarUrl={profile.avatar_url}\n          userId={userId}\n          onAvatarUpdate={handleAvatarUpdate}\n        />\n      </div>\n\n      {/* Profile Form */}\n      <div className=\"bg-white shadow rounded-lg p-6\">\n        <h3 className=\"text-lg font-medium text-gray-900 mb-4\">Profile Information</h3>\n        \n        {error && (\n          <div className=\"mb-4 p-4 bg-red-50 border border-red-200 rounded-md\">\n            <p className=\"text-red-600 text-sm\">{error}</p>\n          </div>\n        )}\n\n        {successMessage && (\n          <div className=\"mb-4 p-4 bg-green-50 border border-green-200 rounded-md\">\n            <p className=\"text-green-600 text-sm\">{successMessage}</p>\n          </div>\n        )}\n\n        <form onSubmit={handleSubmit} className=\"space-y-4\">\n          {/* Basic Information */}\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n            <div>\n              <label htmlFor=\"full_name\" className=\"block text-sm font-medium text-gray-700 mb-1\">\n                Full Name\n              </label>\n              <input\n                type=\"text\"\n                id=\"full_name\"\n                name=\"full_name\"\n                value={formData.full_name}\n                onChange={handleInputChange}\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n              />\n            </div>\n\n            <div>\n              <label htmlFor=\"pdga_number\" className=\"block text-sm font-medium text-gray-700 mb-1\">\n                PDGA Number\n              </label>\n              <input\n                type=\"number\"\n                id=\"pdga_number\"\n                name=\"pdga_number\"\n                value={formData.pdga_number}\n                onChange={handleInputChange}\n                placeholder=\"12345\"\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n              />\n            </div>\n          </div>\n\n          {/* Contact Information */}\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n            <div>\n              <label htmlFor=\"phone_number\" className=\"block text-sm font-medium text-gray-700 mb-1\">\n                Phone Number\n              </label>\n              <input\n                type=\"tel\"\n                id=\"phone_number\"\n                name=\"phone_number\"\n                value={formData.phone_number}\n                onChange={handleInputChange}\n                placeholder=\"+****************\"\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n              />\n            </div>\n\n            <div>\n              <label htmlFor=\"sms_number\" className=\"block text-sm font-medium text-gray-700 mb-1\">\n                SMS Number\n              </label>\n              <input\n                type=\"tel\"\n                id=\"sms_number\"\n                name=\"sms_number\"\n                value={formData.sms_number}\n                onChange={handleInputChange}\n                placeholder=\"+****************\"\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n              />\n            </div>\n          </div>\n\n          {/* Legacy phone field (for backward compatibility) */}\n          <div>\n            <label htmlFor=\"phone\" className=\"block text-sm font-medium text-gray-700 mb-1\">\n              Phone (Legacy)\n            </label>\n            <input\n              type=\"tel\"\n              id=\"phone\"\n              name=\"phone\"\n              value={formData.phone}\n              onChange={handleInputChange}\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n            />\n          </div>\n\n          {/* Social Media */}\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n            <div>\n              <label htmlFor=\"facebook_profile\" className=\"block text-sm font-medium text-gray-700 mb-1\">\n                Facebook Profile\n              </label>\n              <input\n                type=\"text\"\n                id=\"facebook_profile\"\n                name=\"facebook_profile\"\n                value={formData.facebook_profile}\n                onChange={handleInputChange}\n                placeholder=\"facebook.com/username or username\"\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n              />\n            </div>\n\n            <div>\n              <label htmlFor=\"instagram_handle\" className=\"block text-sm font-medium text-gray-700 mb-1\">\n                Instagram Handle\n              </label>\n              <input\n                type=\"text\"\n                id=\"instagram_handle\"\n                name=\"instagram_handle\"\n                value={formData.instagram_handle}\n                onChange={handleInputChange}\n                placeholder=\"username (without @)\"\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n              />\n            </div>\n          </div>\n\n          {/* Submit Button */}\n          <div className=\"pt-4\">\n            <button\n              type=\"submit\"\n              disabled={isSaving}\n              className=\"w-full bg-blue-600 hover:bg-blue-700 disabled:bg-blue-300 text-white py-2 px-4 rounded-md font-medium\"\n            >\n              {isSaving ? 'Saving...' : 'Save Profile'}\n            </button>\n          </div>\n        </form>\n\n        {/* Profile Info */}\n        {profile.legacy_row_id && (\n          <div className=\"mt-6 pt-6 border-t border-gray-200\">\n            <h4 className=\"text-sm font-medium text-gray-700 mb-2\">Import Information</h4>\n            <p className=\"text-sm text-gray-500\">\n              Legacy ID: {profile.legacy_row_id}\n            </p>\n            <p className=\"text-sm text-gray-500\">\n              Role: {profile.role}\n            </p>\n          </div>\n        )}\n      </div>\n    </div>\n  );\n};\n\nexport default ProfileManager;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,QAAQ,QAAiB,iBAAiB;AACnD,OAAOC,YAAY,MAAM,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAO1C,MAAMC,cAA6C,GAAGA,CAAC;EAAEC,MAAM;EAAEC;AAAgB,CAAC,KAAK;EAAAC,EAAA;EACrF,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGX,QAAQ,CAAiB,IAAI,CAAC;EAC5D,MAAM,CAACY,SAAS,EAAEC,YAAY,CAAC,GAAGb,QAAQ,CAAC,IAAI,CAAC;EAChD,MAAM,CAACc,QAAQ,EAAEC,WAAW,CAAC,GAAGf,QAAQ,CAAC,KAAK,CAAC;EAC/C,MAAM,CAACgB,KAAK,EAAEC,QAAQ,CAAC,GAAGjB,QAAQ,CAAgB,IAAI,CAAC;EACvD,MAAM,CAACkB,cAAc,EAAEC,iBAAiB,CAAC,GAAGnB,QAAQ,CAAgB,IAAI,CAAC;;EAEzE;EACA,MAAM,CAACoB,QAAQ,EAAEC,WAAW,CAAC,GAAGrB,QAAQ,CAAC;IACvCsB,SAAS,EAAE,EAAE;IACbC,KAAK,EAAE,EAAE;IACTC,WAAW,EAAE,EAAE;IACfC,gBAAgB,EAAE,EAAE;IACpBC,gBAAgB,EAAE,EAAE;IACpBC,UAAU,EAAE,EAAE;IACdC,YAAY,EAAE;EAChB,CAAC,CAAC;EAEF3B,SAAS,CAAC,MAAM;IACd4B,WAAW,CAAC,CAAC;EACf,CAAC,EAAE,CAACtB,MAAM,CAAC,CAAC,CAAC,CAAC;;EAEd,MAAMsB,WAAW,GAAG,MAAAA,CAAA,KAAY;IAC9B,IAAI;MAAA,IAAAC,iBAAA;MACFjB,YAAY,CAAC,IAAI,CAAC;MAClB,MAAM;QAAEkB,IAAI;QAAEf;MAAM,CAAC,GAAG,MAAMd,QAAQ,CACnC8B,IAAI,CAAC,UAAU,CAAC,CAChBC,MAAM,CAAC,GAAG,CAAC,CACXC,EAAE,CAAC,IAAI,EAAE3B,MAAM,CAAC,CAChB4B,MAAM,CAAC,CAAC;MAEX,IAAInB,KAAK,EAAE;QACT,MAAMA,KAAK;MACb;MAEAL,UAAU,CAACoB,IAAI,CAAC;MAChBV,WAAW,CAAC;QACVC,SAAS,EAAES,IAAI,CAACT,SAAS,IAAI,EAAE;QAC/BC,KAAK,EAAEQ,IAAI,CAACR,KAAK,IAAI,EAAE;QACvBC,WAAW,EAAE,EAAAM,iBAAA,GAAAC,IAAI,CAACP,WAAW,cAAAM,iBAAA,uBAAhBA,iBAAA,CAAkBM,QAAQ,CAAC,CAAC,KAAI,EAAE;QAC/CX,gBAAgB,EAAEM,IAAI,CAACN,gBAAgB,IAAI,EAAE;QAC7CC,gBAAgB,EAAEK,IAAI,CAACL,gBAAgB,IAAI,EAAE;QAC7CC,UAAU,EAAEI,IAAI,CAACJ,UAAU,IAAI,EAAE;QACjCC,YAAY,EAAEG,IAAI,CAACH,YAAY,IAAI;MACrC,CAAC,CAAC;IACJ,CAAC,CAAC,OAAOS,GAAG,EAAE;MACZC,OAAO,CAACtB,KAAK,CAAC,wBAAwB,EAAEqB,GAAG,CAAC;MAC5CpB,QAAQ,CAACoB,GAAG,YAAYE,KAAK,GAAGF,GAAG,CAACG,OAAO,GAAG,wBAAwB,CAAC;IACzE,CAAC,SAAS;MACR3B,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC;EAED,MAAM4B,iBAAiB,GAAIC,CAAsC,IAAK;IACpE,MAAM;MAAEC,IAAI;MAAEC;IAAM,CAAC,GAAGF,CAAC,CAACG,MAAM;IAChCxB,WAAW,CAACyB,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAE,CAACH,IAAI,GAAGC;IAAM,CAAC,CAAC,CAAC;EACnD,CAAC;EAED,MAAMG,YAAY,GAAG,MAAOL,CAAkB,IAAK;IACjDA,CAAC,CAACM,cAAc,CAAC,CAAC;IAClBjC,WAAW,CAAC,IAAI,CAAC;IACjBE,QAAQ,CAAC,IAAI,CAAC;IACdE,iBAAiB,CAAC,IAAI,CAAC;IAEvB,IAAI;MACF,MAAM8B,UAAU,GAAG;QACjB3B,SAAS,EAAEF,QAAQ,CAACE,SAAS;QAC7BC,KAAK,EAAEH,QAAQ,CAACG,KAAK;QACrBC,WAAW,EAAEJ,QAAQ,CAACI,WAAW,GAAG0B,QAAQ,CAAC9B,QAAQ,CAACI,WAAW,CAAC,GAAG,IAAI;QACzEC,gBAAgB,EAAEL,QAAQ,CAACK,gBAAgB;QAC3CC,gBAAgB,EAAEN,QAAQ,CAACM,gBAAgB;QAC3CC,UAAU,EAAEP,QAAQ,CAACO,UAAU;QAC/BC,YAAY,EAAER,QAAQ,CAACQ,YAAY;QACnCuB,UAAU,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;MACrC,CAAC;MAED,MAAM;QAAEtB,IAAI;QAAEf;MAAM,CAAC,GAAG,MAAMd,QAAQ,CACnC8B,IAAI,CAAC,UAAU,CAAC,CAChBsB,MAAM,CAACL,UAAU,CAAC,CAClBf,EAAE,CAAC,IAAI,EAAE3B,MAAM,CAAC,CAChB0B,MAAM,CAAC,CAAC,CACRE,MAAM,CAAC,CAAC;MAEX,IAAInB,KAAK,EAAE;QACT,MAAMA,KAAK;MACb;MAEAL,UAAU,CAACoB,IAAI,CAAC;MAChBZ,iBAAiB,CAAC,+BAA+B,CAAC;MAClDX,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAGuB,IAAI,CAAC;;MAEvB;MACAwB,UAAU,CAAC,MAAMpC,iBAAiB,CAAC,IAAI,CAAC,EAAE,IAAI,CAAC;IACjD,CAAC,CAAC,OAAOkB,GAAG,EAAE;MACZC,OAAO,CAACtB,KAAK,CAAC,yBAAyB,EAAEqB,GAAG,CAAC;MAC7CpB,QAAQ,CAACoB,GAAG,YAAYE,KAAK,GAAGF,GAAG,CAACG,OAAO,GAAG,0BAA0B,CAAC;IAC3E,CAAC,SAAS;MACRzB,WAAW,CAAC,KAAK,CAAC;IACpB;EACF,CAAC;EAED,MAAMyC,kBAAkB,GAAIC,YAAoB,IAAK;IACnD,IAAI/C,OAAO,EAAE;MACX,MAAMgD,cAAc,GAAG;QAAE,GAAGhD,OAAO;QAAEiD,UAAU,EAAEF;MAAa,CAAC;MAC/D9C,UAAU,CAAC+C,cAAc,CAAC;MAC1BlD,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAGkD,cAAc,CAAC;IACnC;EACF,CAAC;EAED,IAAI9C,SAAS,EAAE;IACb,oBACEP,OAAA;MAAKuD,SAAS,EAAC,sCAAsC;MAAAC,QAAA,eACnDxD,OAAA;QAAKuD,SAAS,EAAC;MAA8D;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjF,CAAC;EAEV;EAEA,IAAI,CAACvD,OAAO,EAAE;IACZ,oBACEL,OAAA;MAAKuD,SAAS,EAAC,iBAAiB;MAAAC,QAAA,eAC9BxD,OAAA;QAAGuD,SAAS,EAAC,cAAc;QAAAC,QAAA,EAAC;MAAsB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACnD,CAAC;EAEV;EAEA,oBACE5D,OAAA;IAAKuD,SAAS,EAAC,uBAAuB;IAAAC,QAAA,gBACpCxD,OAAA;MAAIuD,SAAS,EAAC,uCAAuC;MAAAC,QAAA,EAAC;IAAgB;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eAG3E5D,OAAA;MAAKuD,SAAS,EAAC,qCAAqC;MAAAC,QAAA,gBAClDxD,OAAA;QAAIuD,SAAS,EAAC,wCAAwC;QAAAC,QAAA,EAAC;MAAa;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACzE5D,OAAA,CAACF,YAAY;QACX+D,gBAAgB,EAAExD,OAAO,CAACiD,UAAW;QACrCpD,MAAM,EAAEA,MAAO;QACf4D,cAAc,EAAEX;MAAmB;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGN5D,OAAA;MAAKuD,SAAS,EAAC,gCAAgC;MAAAC,QAAA,gBAC7CxD,OAAA;QAAIuD,SAAS,EAAC,wCAAwC;QAAAC,QAAA,EAAC;MAAmB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,EAE9EjD,KAAK,iBACJX,OAAA;QAAKuD,SAAS,EAAC,qDAAqD;QAAAC,QAAA,eAClExD,OAAA;UAAGuD,SAAS,EAAC,sBAAsB;UAAAC,QAAA,EAAE7C;QAAK;UAAA8C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5C,CACN,EAEA/C,cAAc,iBACbb,OAAA;QAAKuD,SAAS,EAAC,yDAAyD;QAAAC,QAAA,eACtExD,OAAA;UAAGuD,SAAS,EAAC,wBAAwB;UAAAC,QAAA,EAAE3C;QAAc;UAAA4C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvD,CACN,eAED5D,OAAA;QAAM+D,QAAQ,EAAErB,YAAa;QAACa,SAAS,EAAC,WAAW;QAAAC,QAAA,gBAEjDxD,OAAA;UAAKuD,SAAS,EAAC,uCAAuC;UAAAC,QAAA,gBACpDxD,OAAA;YAAAwD,QAAA,gBACExD,OAAA;cAAOgE,OAAO,EAAC,WAAW;cAACT,SAAS,EAAC,8CAA8C;cAAAC,QAAA,EAAC;YAEpF;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACR5D,OAAA;cACEiE,IAAI,EAAC,MAAM;cACXC,EAAE,EAAC,WAAW;cACd5B,IAAI,EAAC,WAAW;cAChBC,KAAK,EAAExB,QAAQ,CAACE,SAAU;cAC1BkD,QAAQ,EAAE/B,iBAAkB;cAC5BmB,SAAS,EAAC;YAAwG;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAEN5D,OAAA;YAAAwD,QAAA,gBACExD,OAAA;cAAOgE,OAAO,EAAC,aAAa;cAACT,SAAS,EAAC,8CAA8C;cAAAC,QAAA,EAAC;YAEtF;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACR5D,OAAA;cACEiE,IAAI,EAAC,QAAQ;cACbC,EAAE,EAAC,aAAa;cAChB5B,IAAI,EAAC,aAAa;cAClBC,KAAK,EAAExB,QAAQ,CAACI,WAAY;cAC5BgD,QAAQ,EAAE/B,iBAAkB;cAC5BgC,WAAW,EAAC,OAAO;cACnBb,SAAS,EAAC;YAAwG;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGN5D,OAAA;UAAKuD,SAAS,EAAC,uCAAuC;UAAAC,QAAA,gBACpDxD,OAAA;YAAAwD,QAAA,gBACExD,OAAA;cAAOgE,OAAO,EAAC,cAAc;cAACT,SAAS,EAAC,8CAA8C;cAAAC,QAAA,EAAC;YAEvF;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACR5D,OAAA;cACEiE,IAAI,EAAC,KAAK;cACVC,EAAE,EAAC,cAAc;cACjB5B,IAAI,EAAC,cAAc;cACnBC,KAAK,EAAExB,QAAQ,CAACQ,YAAa;cAC7B4C,QAAQ,EAAE/B,iBAAkB;cAC5BgC,WAAW,EAAC,mBAAmB;cAC/Bb,SAAS,EAAC;YAAwG;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAEN5D,OAAA;YAAAwD,QAAA,gBACExD,OAAA;cAAOgE,OAAO,EAAC,YAAY;cAACT,SAAS,EAAC,8CAA8C;cAAAC,QAAA,EAAC;YAErF;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACR5D,OAAA;cACEiE,IAAI,EAAC,KAAK;cACVC,EAAE,EAAC,YAAY;cACf5B,IAAI,EAAC,YAAY;cACjBC,KAAK,EAAExB,QAAQ,CAACO,UAAW;cAC3B6C,QAAQ,EAAE/B,iBAAkB;cAC5BgC,WAAW,EAAC,mBAAmB;cAC/Bb,SAAS,EAAC;YAAwG;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGN5D,OAAA;UAAAwD,QAAA,gBACExD,OAAA;YAAOgE,OAAO,EAAC,OAAO;YAACT,SAAS,EAAC,8CAA8C;YAAAC,QAAA,EAAC;UAEhF;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACR5D,OAAA;YACEiE,IAAI,EAAC,KAAK;YACVC,EAAE,EAAC,OAAO;YACV5B,IAAI,EAAC,OAAO;YACZC,KAAK,EAAExB,QAAQ,CAACG,KAAM;YACtBiD,QAAQ,EAAE/B,iBAAkB;YAC5BmB,SAAS,EAAC;UAAwG;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAGN5D,OAAA;UAAKuD,SAAS,EAAC,uCAAuC;UAAAC,QAAA,gBACpDxD,OAAA;YAAAwD,QAAA,gBACExD,OAAA;cAAOgE,OAAO,EAAC,kBAAkB;cAACT,SAAS,EAAC,8CAA8C;cAAAC,QAAA,EAAC;YAE3F;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACR5D,OAAA;cACEiE,IAAI,EAAC,MAAM;cACXC,EAAE,EAAC,kBAAkB;cACrB5B,IAAI,EAAC,kBAAkB;cACvBC,KAAK,EAAExB,QAAQ,CAACK,gBAAiB;cACjC+C,QAAQ,EAAE/B,iBAAkB;cAC5BgC,WAAW,EAAC,mCAAmC;cAC/Cb,SAAS,EAAC;YAAwG;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAEN5D,OAAA;YAAAwD,QAAA,gBACExD,OAAA;cAAOgE,OAAO,EAAC,kBAAkB;cAACT,SAAS,EAAC,8CAA8C;cAAAC,QAAA,EAAC;YAE3F;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACR5D,OAAA;cACEiE,IAAI,EAAC,MAAM;cACXC,EAAE,EAAC,kBAAkB;cACrB5B,IAAI,EAAC,kBAAkB;cACvBC,KAAK,EAAExB,QAAQ,CAACM,gBAAiB;cACjC8C,QAAQ,EAAE/B,iBAAkB;cAC5BgC,WAAW,EAAC,sBAAsB;cAClCb,SAAS,EAAC;YAAwG;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGN5D,OAAA;UAAKuD,SAAS,EAAC,MAAM;UAAAC,QAAA,eACnBxD,OAAA;YACEiE,IAAI,EAAC,QAAQ;YACbI,QAAQ,EAAE5D,QAAS;YACnB8C,SAAS,EAAC,uGAAuG;YAAAC,QAAA,EAEhH/C,QAAQ,GAAG,WAAW,GAAG;UAAc;YAAAgD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,EAGNvD,OAAO,CAACiE,aAAa,iBACpBtE,OAAA;QAAKuD,SAAS,EAAC,oCAAoC;QAAAC,QAAA,gBACjDxD,OAAA;UAAIuD,SAAS,EAAC,wCAAwC;UAAAC,QAAA,EAAC;QAAkB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC9E5D,OAAA;UAAGuD,SAAS,EAAC,uBAAuB;UAAAC,QAAA,GAAC,aACxB,EAACnD,OAAO,CAACiE,aAAa;QAAA;UAAAb,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChC,CAAC,eACJ5D,OAAA;UAAGuD,SAAS,EAAC,uBAAuB;UAAAC,QAAA,GAAC,QAC7B,EAACnD,OAAO,CAACkE,IAAI;QAAA;UAAAd,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACxD,EAAA,CAxSIH,cAA6C;AAAAuE,EAAA,GAA7CvE,cAA6C;AA0SnD,eAAeA,cAAc;AAAC,IAAAuE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}