{"ast": null, "code": "import { createClient } from '@supabase/supabase-js';\n\n// Supabase configuration\nconst supabaseUrl = process.env.REACT_APP_SUPABASE_URL || 'https://demo.supabase.co';\nconst supabaseAnonKey = process.env.REACT_APP_SUPABASE_ANON_KEY || 'demo-key';\nexport const supabase = createClient(supabaseUrl, supabaseAnonKey);\n\n// Database types\n\n// Helper functions for database operations\nexport const discService = {\n  // Ensure demo user exists\n  async ensureDemoUser() {\n    const demoUserId = '00000000-0000-0000-0000-000000000000';\n    try {\n      // Check if demo user exists\n      const {\n        data: existingUser\n      } = await supabase.from('profiles').select('id').eq('id', demoUserId).single();\n      if (!existingUser) {\n        // Create demo user\n        const {\n          error\n        } = await supabase.from('profiles').insert([{\n          id: demoUserId,\n          email: '<EMAIL>',\n          full_name: 'Demo User'\n        }]);\n        if (error) {\n          console.warn('Could not create demo user:', error);\n        }\n      }\n      return demoUserId;\n    } catch (error) {\n      console.warn('Demo user setup failed:', error);\n      return demoUserId;\n    }\n  },\n  // Create a new found disc report\n  async createFoundDisc(discData) {\n    try {\n      const {\n        data,\n        error\n      } = await supabase.from('found_discs').insert([{\n        ...discData,\n        status: 'active'\n      }]).select().single();\n      if (error) throw error;\n      return {\n        data,\n        error: null\n      };\n    } catch (error) {\n      console.error('Error creating found disc:', error);\n      return {\n        data: null,\n        error\n      };\n    }\n  },\n  // Get all active found discs (uses public view for role-based filtering)\n  async getFoundDiscs() {\n    try {\n      // Try the public view first, fallback to main table\n      let {\n        data,\n        error\n      } = await supabase.from('public_found_discs').select('*').order('created_at', {\n        ascending: false\n      });\n\n      // If public view doesn't exist or doesn't have image_urls, use main table\n      if (error || data && data.length > 0 && !data[0].hasOwnProperty('image_urls')) {\n        console.log('Using main table instead of view');\n        const result = await supabase.from('found_discs').select('*').eq('status', 'active').order('created_at', {\n          ascending: false\n        });\n        data = result.data;\n        error = result.error;\n      }\n      if (error) throw error;\n      return {\n        data,\n        error: null\n      };\n    } catch (error) {\n      console.error('Error fetching found discs:', error);\n      return {\n        data: null,\n        error\n      };\n    }\n  },\n  // Search found discs by criteria (uses public view for role-based filtering)\n  async searchFoundDiscs(searchCriteria) {\n    try {\n      // Try the public view first, fallback to main table\n      let query = supabase.from('public_found_discs').select('*');\n      if (searchCriteria.brand) {\n        query = query.ilike('brand', `%${searchCriteria.brand}%`);\n      }\n      if (searchCriteria.mold) {\n        query = query.ilike('mold', `%${searchCriteria.mold}%`);\n      }\n      if (searchCriteria.color) {\n        query = query.ilike('color', `%${searchCriteria.color}%`);\n      }\n      if (searchCriteria.discType) {\n        query = query.eq('disc_type', searchCriteria.discType);\n      }\n      if (searchCriteria.locationFound) {\n        query = query.ilike('location_found', `%${searchCriteria.locationFound}%`);\n      }\n      if (searchCriteria.rackId) {\n        const rackIdNum = parseInt(searchCriteria.rackId);\n        if (!isNaN(rackIdNum)) {\n          query = query.eq('rack_id', rackIdNum);\n        }\n      }\n      let {\n        data,\n        error\n      } = await query.order('created_at', {\n        ascending: false\n      });\n\n      // If public view doesn't exist or doesn't have image_urls, use main table\n      if (error || data && data.length > 0 && !data[0].hasOwnProperty('image_urls')) {\n        console.log('Using main table for search instead of view');\n        let mainQuery = supabase.from('found_discs').select('*').eq('status', 'active');\n        if (searchCriteria.brand) {\n          mainQuery = mainQuery.ilike('brand', `%${searchCriteria.brand}%`);\n        }\n        if (searchCriteria.mold) {\n          mainQuery = mainQuery.ilike('mold', `%${searchCriteria.mold}%`);\n        }\n        if (searchCriteria.color) {\n          mainQuery = mainQuery.ilike('color', `%${searchCriteria.color}%`);\n        }\n        if (searchCriteria.discType) {\n          mainQuery = mainQuery.eq('disc_type', searchCriteria.discType);\n        }\n        if (searchCriteria.locationFound) {\n          mainQuery = mainQuery.ilike('location_found', `%${searchCriteria.locationFound}%`);\n        }\n        if (searchCriteria.rackId) {\n          const rackIdNum = parseInt(searchCriteria.rackId);\n          if (!isNaN(rackIdNum)) {\n            mainQuery = mainQuery.eq('rack_id', rackIdNum);\n          }\n        }\n        const result = await mainQuery.order('created_at', {\n          ascending: false\n        });\n        data = result.data;\n        error = result.error;\n      }\n      if (error) throw error;\n      return {\n        data,\n        error: null\n      };\n    } catch (error) {\n      console.error('Error searching found discs:', error);\n      return {\n        data: null,\n        error\n      };\n    }\n  },\n  // Search found discs with a single query string across all fields\n  async searchFoundDiscsWithQuery(searchQuery) {\n    try {\n      if (!searchQuery || searchQuery.trim() === '') {\n        // If no search query, return all discs\n        return this.getFoundDiscs();\n      }\n\n      // Split search query into individual terms\n      const searchTerms = searchQuery.trim().toLowerCase().split(/\\s+/);\n\n      // Get all discs first, then filter in JavaScript for complex multi-term search\n      const {\n        data: allDiscs,\n        error: fetchError\n      } = await this.getFoundDiscs();\n      if (fetchError) {\n        throw fetchError;\n      }\n      if (!allDiscs || allDiscs.length === 0) {\n        return {\n          data: [],\n          error: null\n        };\n      }\n\n      // Filter discs that match all search terms\n      const filteredDiscs = allDiscs.filter(disc => {\n        // For each search term, check if it matches any field in the disc\n        return searchTerms.every(term => {\n          const lowerTerm = term.toLowerCase();\n\n          // Check text fields\n          const textFields = [disc.brand, disc.mold, disc.color, disc.location_found, disc.description, disc.stamp_text, disc.phone_number, disc.name_on_disc, disc.plastic_type, disc.disc_type];\n\n          // Check if any text field contains the term\n          const matchesText = textFields.some(field => field && field.toLowerCase().includes(lowerTerm));\n\n          // Check rack_id if term is a number\n          const rackIdNum = parseInt(term);\n          const matchesRackId = !isNaN(rackIdNum) && disc.rack_id === rackIdNum;\n          return matchesText || matchesRackId;\n        });\n      });\n      return {\n        data: filteredDiscs,\n        error: null\n      };\n    } catch (error) {\n      console.error('Error searching found discs with query:', error);\n      return {\n        data: null,\n        error\n      };\n    }\n  },\n  // Test connection to Supabase\n  async testConnection() {\n    try {\n      const {\n        error\n      } = await supabase.from('found_discs').select('count').limit(1);\n      return {\n        connected: !error,\n        error\n      };\n    } catch (error) {\n      return {\n        connected: false,\n        error\n      };\n    }\n  },\n  // Update return status of a found disc (admin only)\n  async updateReturnStatus(discId, returnStatus, notes) {\n    try {\n      const {\n        error\n      } = await supabase.rpc('update_disc_return_status', {\n        disc_id: discId,\n        new_status: returnStatus,\n        notes: notes || null\n      });\n      if (error) throw error;\n      return {\n        success: true,\n        error: null\n      };\n    } catch (error) {\n      console.error('Error updating return status:', error);\n      return {\n        success: false,\n        error\n      };\n    }\n  },\n  // Get all found discs for admin (includes all return statuses)\n  async getAdminFoundDiscs() {\n    try {\n      const {\n        data,\n        error\n      } = await supabase.from('admin_found_discs').select('*');\n      if (error) throw error;\n      return {\n        data,\n        error: null\n      };\n    } catch (error) {\n      console.error('Error fetching admin found discs:', error);\n      return {\n        data: null,\n        error\n      };\n    }\n  },\n  // Bulk Turn-In Functions\n\n  // Create a new bulk turn-in record (rakerdiver only)\n  async createBulkTurnin(turninData) {\n    try {\n      const {\n        data,\n        error\n      } = await supabase.rpc('create_bulk_turnin', {\n        p_location_collected: turninData.location_collected,\n        p_collection_date: turninData.collection_date,\n        p_disc_count: turninData.disc_count,\n        p_turnin_location: turninData.turnin_location,\n        p_turnin_date: turninData.turnin_date,\n        p_collection_time: turninData.collection_time || null,\n        p_turnin_time: turninData.turnin_time || null,\n        p_notes: turninData.notes || null\n      });\n      if (error) throw error;\n      return {\n        data,\n        error: null\n      };\n    } catch (error) {\n      console.error('Error creating bulk turn-in:', error);\n      return {\n        data: null,\n        error\n      };\n    }\n  },\n  // Get bulk turn-ins for current rakerdiver\n  async getRakerdiverTurnins() {\n    try {\n      const {\n        data,\n        error\n      } = await supabase.rpc('get_rakerdiver_turnins');\n      if (error) throw error;\n      return {\n        data,\n        error: null\n      };\n    } catch (error) {\n      console.error('Error fetching rakerdiver turn-ins:', error);\n      return {\n        data: null,\n        error\n      };\n    }\n  },\n  // Verify a bulk turn-in (admin only)\n  async verifyBulkTurnin(turninId, verificationNotes) {\n    try {\n      const {\n        error\n      } = await supabase.rpc('verify_bulk_turnin', {\n        p_turnin_id: turninId,\n        p_verification_notes: verificationNotes || null\n      });\n      if (error) throw error;\n      return {\n        success: true,\n        error: null\n      };\n    } catch (error) {\n      console.error('Error verifying bulk turn-in:', error);\n      return {\n        success: false,\n        error\n      };\n    }\n  },\n  // Create a payment record (admin only)\n  async createBulkTurninPayment(paymentData) {\n    try {\n      const {\n        data,\n        error\n      } = await supabase.rpc('create_bulk_turnin_payment', {\n        p_bulk_turnin_id: paymentData.bulk_turnin_id,\n        p_amount: paymentData.amount,\n        p_payment_method: paymentData.payment_method || null,\n        p_payment_date: paymentData.payment_date || null,\n        p_payment_notes: paymentData.payment_notes || null\n      });\n      if (error) throw error;\n      return {\n        data,\n        error: null\n      };\n    } catch (error) {\n      console.error('Error creating payment:', error);\n      return {\n        data: null,\n        error\n      };\n    }\n  },\n  // Confirm payment receipt (rakerdiver only)\n  async confirmPaymentReceipt(paymentId) {\n    try {\n      const {\n        error\n      } = await supabase.rpc('confirm_payment_receipt', {\n        p_payment_id: paymentId\n      });\n      if (error) throw error;\n      return {\n        success: true,\n        error: null\n      };\n    } catch (error) {\n      console.error('Error confirming payment:', error);\n      return {\n        success: false,\n        error\n      };\n    }\n  },\n  // Get all bulk turn-ins for admin\n  async getAdminBulkTurnins() {\n    try {\n      const {\n        data,\n        error\n      } = await supabase.from('admin_bulk_turnins').select('*');\n      if (error) throw error;\n      return {\n        data,\n        error: null\n      };\n    } catch (error) {\n      console.error('Error fetching admin bulk turn-ins:', error);\n      return {\n        data: null,\n        error\n      };\n    }\n  },\n  // Get payments for a specific bulk turn-in\n  async getBulkTurninPayments(turninId) {\n    try {\n      const {\n        data,\n        error\n      } = await supabase.from('bulk_turnin_payments').select('*').eq('bulk_turnin_id', turninId).order('created_at', {\n        ascending: false\n      });\n      if (error) throw error;\n      return {\n        data,\n        error: null\n      };\n    } catch (error) {\n      console.error('Error fetching payments:', error);\n      return {\n        data: null,\n        error\n      };\n    }\n  }\n};\n\n// Image upload service\nexport const imageService = {\n  // Upload multiple images to Supabase storage\n  async uploadImages(files, userId) {\n    try {\n      console.log(`Starting upload of ${files.length} files for user ${userId}`);\n\n      // Check if user is authenticated\n      const {\n        data: {\n          user\n        }\n      } = await supabase.auth.getUser();\n      if (!user) {\n        throw new Error('User not authenticated');\n      }\n      console.log('User authenticated:', user.id);\n      const uploadPromises = files.map(async (file, index) => {\n        // Generate unique filename\n        const fileExt = file.name.split('.').pop();\n        const fileName = `${userId}/${Date.now()}-${index}.${fileExt}`;\n        console.log(`Uploading file ${index + 1}/${files.length}: ${fileName}`);\n        const {\n          data,\n          error\n        } = await supabase.storage.from('disc-images').upload(fileName, file, {\n          cacheControl: '3600',\n          upsert: false\n        });\n        if (error) {\n          console.error(`Upload error for ${fileName}:`, error);\n          throw error;\n        }\n        console.log(`Upload successful for ${fileName}:`, data);\n\n        // Get public URL\n        const {\n          data: urlData\n        } = supabase.storage.from('disc-images').getPublicUrl(fileName);\n        console.log(`Public URL for ${fileName}:`, urlData.publicUrl);\n        return urlData.publicUrl;\n      });\n      const urls = await Promise.all(uploadPromises);\n      console.log('All uploads completed:', urls);\n      return {\n        urls,\n        error: null\n      };\n    } catch (error) {\n      console.error('Error uploading images:', error);\n      return {\n        urls: [],\n        error\n      };\n    }\n  },\n  // Delete images from Supabase storage\n  async deleteImages(imageUrls) {\n    try {\n      // Extract file paths from URLs\n      const filePaths = imageUrls.map(url => {\n        const urlParts = url.split('/');\n        const bucketIndex = urlParts.findIndex(part => part === 'disc-images');\n        if (bucketIndex !== -1 && bucketIndex < urlParts.length - 1) {\n          return urlParts.slice(bucketIndex + 1).join('/');\n        }\n        return null;\n      }).filter(Boolean);\n      if (filePaths.length === 0) {\n        return {\n          success: true,\n          error: null\n        };\n      }\n      const {\n        error\n      } = await supabase.storage.from('disc-images').remove(filePaths);\n      return {\n        success: !error,\n        error\n      };\n    } catch (error) {\n      console.error('Error deleting images:', error);\n      return {\n        success: false,\n        error\n      };\n    }\n  },\n  // Get optimized image URL (for future use with Supabase image transformations)\n  getOptimizedImageUrl(originalUrl, width, height) {\n    // For now, return original URL\n    // In the future, you can add Supabase image transformation parameters\n    return originalUrl;\n  },\n  // Validate image file\n  validateImageFile(file, maxSizeMB = 10) {\n    // Check file type\n    if (!file.type.startsWith('image/')) {\n      return {\n        valid: false,\n        error: 'File must be an image'\n      };\n    }\n\n    // Check file size\n    const maxSizeBytes = maxSizeMB * 1024 * 1024;\n    if (file.size > maxSizeBytes) {\n      return {\n        valid: false,\n        error: `Image size must be less than ${maxSizeMB}MB`\n      };\n    }\n\n    // Check supported formats\n    const supportedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];\n    if (!supportedTypes.includes(file.type)) {\n      return {\n        valid: false,\n        error: 'Supported formats: JPEG, PNG, WebP'\n      };\n    }\n    return {\n      valid: true\n    };\n  }\n};\n\n// Export the service instance\nexport const supabaseService = {\n  getSources: async () => {\n    const {\n      data,\n      error\n    } = await supabase.from('sources').select('*').order('sort_order', {\n      ascending: true\n    }).order('name', {\n      ascending: true\n    });\n    if (error) {\n      console.error('Error fetching sources:', error);\n      throw error;\n    }\n    return data || [];\n  },\n  getActiveSources: async () => {\n    const {\n      data,\n      error\n    } = await supabase.from('sources').select('*').eq('is_active', true).order('sort_order', {\n      ascending: true\n    }).order('name', {\n      ascending: true\n    });\n    if (error) {\n      console.error('Error fetching active sources:', error);\n      throw error;\n    }\n    return data || [];\n  },\n  createSource: async source => {\n    const {\n      data,\n      error\n    } = await supabase.from('sources').insert([source]).select().single();\n    if (error) {\n      console.error('Error creating source:', error);\n      throw error;\n    }\n    return data;\n  },\n  updateSource: async (id, updates) => {\n    const {\n      data,\n      error\n    } = await supabase.from('sources').update(updates).eq('id', id).select().single();\n    if (error) {\n      console.error('Error updating source:', error);\n      throw error;\n    }\n    return data;\n  },\n  deleteSource: async id => {\n    const {\n      error\n    } = await supabase.from('sources').delete().eq('id', id);\n    if (error) {\n      console.error('Error deleting source:', error);\n      throw error;\n    }\n  },\n  // Contact Attempts\n  getContactAttempts: async discId => {\n    try {\n      const {\n        data,\n        error\n      } = await supabase.from('contact_attempts').select(`\n          *,\n          attempted_by_profile:profiles!attempted_by_profile_id(\n            id,\n            full_name,\n            email\n          )\n        `).eq('found_disc_id', discId).order('attempted_at', {\n        ascending: false\n      });\n      if (error) throw error;\n      return {\n        success: true,\n        data: data || []\n      };\n    } catch (error) {\n      console.error('Error fetching contact attempts:', error);\n      return {\n        success: false,\n        error\n      };\n    }\n  },\n  addContactAttempt: async contactAttempt => {\n    try {\n      const {\n        data,\n        error\n      } = await supabase.from('contact_attempts').insert([contactAttempt]).select();\n      if (error) throw error;\n      return {\n        success: true,\n        data: data === null || data === void 0 ? void 0 : data[0]\n      };\n    } catch (error) {\n      console.error('Error adding contact attempt:', error);\n      return {\n        success: false,\n        error\n      };\n    }\n  },\n  updateContactAttempt: async (id, updates) => {\n    try {\n      const {\n        data,\n        error\n      } = await supabase.from('contact_attempts').update({\n        ...updates,\n        updated_at: new Date().toISOString()\n      }).eq('id', id).select();\n      if (error) throw error;\n      return {\n        success: true,\n        data: data === null || data === void 0 ? void 0 : data[0]\n      };\n    } catch (error) {\n      console.error('Error updating contact attempt:', error);\n      return {\n        success: false,\n        error\n      };\n    }\n  },\n  // Default Source Management\n  getUserDefaultSource: async userId => {\n    try {\n      // First get the user's default_source_id\n      const {\n        data: profileData,\n        error: profileError\n      } = await supabase.from('profiles').select('default_source_id').eq('id', userId).single();\n      if (profileError) throw profileError;\n\n      // If no default source is set, return null\n      if (!(profileData !== null && profileData !== void 0 && profileData.default_source_id)) {\n        return null;\n      }\n\n      // Get the source details\n      const {\n        data: sourceData,\n        error: sourceError\n      } = await supabase.from('sources').select('*').eq('id', profileData.default_source_id).eq('is_active', true).single();\n      if (sourceError) {\n        // If source doesn't exist or is inactive, return null\n        return null;\n      }\n      return sourceData;\n    } catch (error) {\n      console.error('Error fetching user default source:', error);\n      return null;\n    }\n  },\n  updateUserDefaultSource: async (userId, sourceId) => {\n    try {\n      const {\n        error\n      } = await supabase.from('profiles').update({\n        default_source_id: sourceId,\n        updated_at: new Date().toISOString()\n      }).eq('id', userId);\n      if (error) throw error;\n      return {\n        success: true\n      };\n    } catch (error) {\n      console.error('Error updating user default source:', error);\n      return {\n        success: false,\n        error\n      };\n    }\n  }\n};", "map": {"version": 3, "names": ["createClient", "supabaseUrl", "process", "env", "REACT_APP_SUPABASE_URL", "supabaseAnonKey", "REACT_APP_SUPABASE_ANON_KEY", "supabase", "discService", "ensureDemoUser", "demoUserId", "data", "existingUser", "from", "select", "eq", "single", "error", "insert", "id", "email", "full_name", "console", "warn", "createFoundDisc", "discData", "status", "getFoundDiscs", "order", "ascending", "length", "hasOwnProperty", "log", "result", "searchFoundDiscs", "searchCriteria", "query", "brand", "ilike", "mold", "color", "discType", "locationFound", "rackId", "rackIdNum", "parseInt", "isNaN", "<PERSON><PERSON><PERSON><PERSON>", "searchFoundDiscsWithQuery", "searchQuery", "trim", "searchTerms", "toLowerCase", "split", "allDiscs", "fetchError", "filteredDiscs", "filter", "disc", "every", "term", "lowerTerm", "textFields", "location_found", "description", "stamp_text", "phone_number", "name_on_disc", "plastic_type", "disc_type", "matchesText", "some", "field", "includes", "matchesRackId", "rack_id", "testConnection", "limit", "connected", "updateReturnStatus", "discId", "returnStatus", "notes", "rpc", "disc_id", "new_status", "success", "getAdminFoundDiscs", "createBulkTurnin", "turninData", "p_location_collected", "location_collected", "p_collection_date", "collection_date", "p_disc_count", "disc_count", "p_turnin_location", "turnin_location", "p_turnin_date", "turnin_date", "p_collection_time", "collection_time", "p_turnin_time", "turnin_time", "p_notes", "getRakerdiverTurnins", "verifyBulkTurnin", "turninId", "verificationNotes", "p_turnin_id", "p_verification_notes", "createBulkTurninPayment", "paymentData", "p_bulk_turnin_id", "bulk_turnin_id", "p_amount", "amount", "p_payment_method", "payment_method", "p_payment_date", "payment_date", "p_payment_notes", "payment_notes", "confirmPaymentReceipt", "paymentId", "p_payment_id", "getAdminBulkTurnins", "getBulkTurninPayments", "imageService", "uploadImages", "files", "userId", "user", "auth", "getUser", "Error", "uploadPromises", "map", "file", "index", "fileExt", "name", "pop", "fileName", "Date", "now", "storage", "upload", "cacheControl", "upsert", "urlData", "getPublicUrl", "publicUrl", "urls", "Promise", "all", "deleteImages", "imageUrls", "filePaths", "url", "urlParts", "bucketIndex", "findIndex", "part", "slice", "join", "Boolean", "remove", "getOptimizedImageUrl", "originalUrl", "width", "height", "validateImageFile", "maxSizeMB", "type", "startsWith", "valid", "maxSizeBytes", "size", "supportedTypes", "supabaseService", "getSources", "getActiveSources", "createSource", "source", "updateSource", "updates", "update", "deleteSource", "delete", "getContactAttempts", "addContactAttempt", "contactAttempt", "updateContactAttempt", "updated_at", "toISOString", "getUserDefaultSource", "profileData", "profileError", "default_source_id", "sourceData", "sourceError", "updateUserDefaultSource", "sourceId"], "sources": ["C:/Users/<USER>/lostandfound/discfinder-app/src/lib/supabase.ts"], "sourcesContent": ["import { createClient } from '@supabase/supabase-js'\n\n// Supabase configuration\nconst supabaseUrl = process.env.REACT_APP_SUPABASE_URL || 'https://demo.supabase.co'\nconst supabaseAnonKey = process.env.REACT_APP_SUPABASE_ANON_KEY || 'demo-key'\n\nexport const supabase = createClient(supabaseUrl, supabaseAnonKey)\n\n// Database types\nexport type UserRole = 'guest' | 'user' | 'admin' | 'rakerdiver';\n\nexport interface Profile {\n  id: string\n  email: string\n  full_name?: string\n  phone?: string\n  role?: UserRole\n  created_at: string\n  updated_at: string\n  // New fields for import and enhanced functionality\n  legacy_row_id?: string\n  pdga_number?: number\n  facebook_profile?: string\n  instagram_handle?: string\n  sms_number?: string\n  phone_number?: string\n  avatar_url?: string\n  default_source_id?: string\n}\n\nexport type ReturnStatus = 'Found' | 'Returned to Owner' | 'Donated' | 'Sold' | 'Trashed';\n\nexport interface ContactAttempt {\n  id: string;\n  found_disc_id: string;\n  attempted_at: string;\n  contact_method: string;\n  message_content: string;\n  attempted_by_profile_id?: string;\n  attempted_by_name?: string;\n  response_received: boolean;\n  response_content?: string;\n  notes?: string;\n  created_at: string;\n  updated_at: string;\n}\n\nexport interface Source {\n  id: string\n  name: string\n  description?: string\n  is_active: boolean\n  sort_order: number\n  legacy_row_id?: string\n  created_at: string\n  updated_at: string\n  created_by?: string\n  updated_by?: string\n}\n\nexport interface FoundDisc {\n  id: string\n  finder_id: string\n  rack_id?: number\n  brand: string\n  mold?: string\n  disc_type?: string\n  color: string\n  weight?: number\n  condition?: string\n  plastic_type?: string\n  stamp_text?: string\n  phone_number?: string\n  name_on_disc?: string\n  location_found: string\n  location_coordinates?: { x: number; y: number }\n  source_id?: string\n  source_name?: string\n  found_date: string\n  description?: string\n  image_urls?: string[]\n  status: 'active' | 'claimed' | 'expired' | 'spam'\n  return_status?: ReturnStatus\n  returned_at?: string\n  returned_notes?: string\n  created_at: string\n  updated_at: string\n}\n\nexport interface LostDisc {\n  id: string\n  owner_id: string\n  brand: string\n  model?: string\n  disc_type?: 'driver' | 'fairway_driver' | 'midrange' | 'putter' | 'approach' | 'distance_driver'\n  color: string\n  weight?: number\n  plastic_type?: string\n  stamp_text?: string\n  location_lost: string\n  location_coordinates?: { x: number; y: number }\n  lost_date: string\n  description?: string\n  reward_offered?: number\n  contact_preference: string\n  status: 'active' | 'claimed' | 'expired' | 'spam'\n  created_at: string\n  updated_at: string\n}\n\nexport interface BulkTurnin {\n  id: string\n  rakerdiver_id: string\n  location_collected: string\n  collection_date: string\n  collection_time?: string\n  disc_count: number\n  turnin_location: string\n  turnin_date: string\n  turnin_time?: string\n  notes?: string\n  admin_verified: boolean\n  verified_by?: string\n  verified_at?: string\n  verification_notes?: string\n  created_at: string\n  updated_at: string\n  total_payments?: number\n  confirmed_payments?: number\n}\n\nexport interface BulkTurninPayment {\n  id: string\n  bulk_turnin_id: string\n  amount: number\n  payment_method?: string\n  payment_date?: string\n  payment_notes?: string\n  created_by: string\n  rakerdiver_confirmed: boolean\n  confirmed_at?: string\n  created_at: string\n  updated_at: string\n}\n\nexport interface AdminBulkTurnin extends BulkTurnin {\n  rakerdiver_name?: string\n  rakerdiver_email?: string\n  verified_by_name?: string\n  payment_count?: number\n}\n\nexport interface DiscMatch {\n  id: string\n  found_disc_id: string\n  lost_disc_id: string\n  match_score: number\n  status: 'potential' | 'confirmed' | 'rejected'\n  finder_contacted_at?: string\n  owner_contacted_at?: string\n  created_at: string\n  updated_at: string\n  found_disc?: FoundDisc\n  lost_disc?: LostDisc\n}\n\n// Helper functions for database operations\nexport const discService = {\n  // Ensure demo user exists\n  async ensureDemoUser() {\n    const demoUserId = '00000000-0000-0000-0000-000000000000';\n    try {\n      // Check if demo user exists\n      const { data: existingUser } = await supabase\n        .from('profiles')\n        .select('id')\n        .eq('id', demoUserId)\n        .single();\n\n      if (!existingUser) {\n        // Create demo user\n        const { error } = await supabase\n          .from('profiles')\n          .insert([{\n            id: demoUserId,\n            email: '<EMAIL>',\n            full_name: 'Demo User'\n          }]);\n\n        if (error) {\n          console.warn('Could not create demo user:', error);\n        }\n      }\n      return demoUserId;\n    } catch (error) {\n      console.warn('Demo user setup failed:', error);\n      return demoUserId;\n    }\n  },\n\n  // Create a new found disc report\n  async createFoundDisc(discData: Omit<FoundDisc, 'id' | 'created_at' | 'updated_at' | 'status'>) {\n    try {\n      const { data, error } = await supabase\n        .from('found_discs')\n        .insert([{\n          ...discData,\n          status: 'active'\n        }])\n        .select()\n        .single()\n\n      if (error) throw error\n      return { data, error: null }\n    } catch (error) {\n      console.error('Error creating found disc:', error)\n      return { data: null, error }\n    }\n  },\n\n  // Get all active found discs (uses public view for role-based filtering)\n  async getFoundDiscs() {\n    try {\n      // Try the public view first, fallback to main table\n      let { data, error } = await supabase\n        .from('public_found_discs')\n        .select('*')\n        .order('created_at', { ascending: false })\n\n      // If public view doesn't exist or doesn't have image_urls, use main table\n      if (error || (data && data.length > 0 && !data[0].hasOwnProperty('image_urls'))) {\n        console.log('Using main table instead of view');\n        const result = await supabase\n          .from('found_discs')\n          .select('*')\n          .eq('status', 'active')\n          .order('created_at', { ascending: false });\n\n        data = result.data;\n        error = result.error;\n      }\n\n      if (error) throw error\n      return { data, error: null }\n    } catch (error) {\n      console.error('Error fetching found discs:', error)\n      return { data: null, error }\n    }\n  },\n\n  // Search found discs by criteria (uses public view for role-based filtering)\n  async searchFoundDiscs(searchCriteria: {\n    brand?: string\n    mold?: string\n    color?: string\n    discType?: string\n    locationFound?: string\n    rackId?: string\n  }) {\n    try {\n      // Try the public view first, fallback to main table\n      let query = supabase\n        .from('public_found_discs')\n        .select('*')\n\n      if (searchCriteria.brand) {\n        query = query.ilike('brand', `%${searchCriteria.brand}%`)\n      }\n      if (searchCriteria.mold) {\n        query = query.ilike('mold', `%${searchCriteria.mold}%`)\n      }\n      if (searchCriteria.color) {\n        query = query.ilike('color', `%${searchCriteria.color}%`)\n      }\n      if (searchCriteria.discType) {\n        query = query.eq('disc_type', searchCriteria.discType)\n      }\n      if (searchCriteria.locationFound) {\n        query = query.ilike('location_found', `%${searchCriteria.locationFound}%`)\n      }\n      if (searchCriteria.rackId) {\n        const rackIdNum = parseInt(searchCriteria.rackId);\n        if (!isNaN(rackIdNum)) {\n          query = query.eq('rack_id', rackIdNum)\n        }\n      }\n\n      let { data, error } = await query.order('created_at', { ascending: false })\n\n      // If public view doesn't exist or doesn't have image_urls, use main table\n      if (error || (data && data.length > 0 && !data[0].hasOwnProperty('image_urls'))) {\n        console.log('Using main table for search instead of view');\n        let mainQuery = supabase\n          .from('found_discs')\n          .select('*')\n          .eq('status', 'active')\n\n        if (searchCriteria.brand) {\n          mainQuery = mainQuery.ilike('brand', `%${searchCriteria.brand}%`)\n        }\n        if (searchCriteria.mold) {\n          mainQuery = mainQuery.ilike('mold', `%${searchCriteria.mold}%`)\n        }\n        if (searchCriteria.color) {\n          mainQuery = mainQuery.ilike('color', `%${searchCriteria.color}%`)\n        }\n        if (searchCriteria.discType) {\n          mainQuery = mainQuery.eq('disc_type', searchCriteria.discType)\n        }\n        if (searchCriteria.locationFound) {\n          mainQuery = mainQuery.ilike('location_found', `%${searchCriteria.locationFound}%`)\n        }\n        if (searchCriteria.rackId) {\n          const rackIdNum = parseInt(searchCriteria.rackId);\n          if (!isNaN(rackIdNum)) {\n            mainQuery = mainQuery.eq('rack_id', rackIdNum)\n          }\n        }\n\n        const result = await mainQuery.order('created_at', { ascending: false });\n        data = result.data;\n        error = result.error;\n      }\n\n      if (error) throw error\n      return { data, error: null }\n    } catch (error) {\n      console.error('Error searching found discs:', error)\n      return { data: null, error }\n    }\n  },\n\n  // Search found discs with a single query string across all fields\n  async searchFoundDiscsWithQuery(searchQuery: string) {\n    try {\n      if (!searchQuery || searchQuery.trim() === '') {\n        // If no search query, return all discs\n        return this.getFoundDiscs();\n      }\n\n      // Split search query into individual terms\n      const searchTerms = searchQuery.trim().toLowerCase().split(/\\s+/);\n\n      // Get all discs first, then filter in JavaScript for complex multi-term search\n      const { data: allDiscs, error: fetchError } = await this.getFoundDiscs();\n\n      if (fetchError) {\n        throw fetchError;\n      }\n\n      if (!allDiscs || allDiscs.length === 0) {\n        return { data: [], error: null };\n      }\n\n      // Filter discs that match all search terms\n      const filteredDiscs = allDiscs.filter(disc => {\n        // For each search term, check if it matches any field in the disc\n        return searchTerms.every(term => {\n          const lowerTerm = term.toLowerCase();\n\n          // Check text fields\n          const textFields = [\n            disc.brand,\n            disc.mold,\n            disc.color,\n            disc.location_found,\n            disc.description,\n            disc.stamp_text,\n            disc.phone_number,\n            disc.name_on_disc,\n            disc.plastic_type,\n            disc.disc_type\n          ];\n\n          // Check if any text field contains the term\n          const matchesText = textFields.some(field =>\n            field && field.toLowerCase().includes(lowerTerm)\n          );\n\n          // Check rack_id if term is a number\n          const rackIdNum = parseInt(term);\n          const matchesRackId = !isNaN(rackIdNum) && disc.rack_id === rackIdNum;\n\n          return matchesText || matchesRackId;\n        });\n      });\n\n      return { data: filteredDiscs, error: null };\n    } catch (error) {\n      console.error('Error searching found discs with query:', error);\n      return { data: null, error };\n    }\n  },\n\n  // Test connection to Supabase\n  async testConnection() {\n    try {\n      const { error } = await supabase\n        .from('found_discs')\n        .select('count')\n        .limit(1)\n\n      return { connected: !error, error }\n    } catch (error) {\n      return { connected: false, error }\n    }\n  },\n\n  // Update return status of a found disc (admin only)\n  async updateReturnStatus(discId: string, returnStatus: ReturnStatus, notes?: string) {\n    try {\n      const { error } = await supabase.rpc('update_disc_return_status', {\n        disc_id: discId,\n        new_status: returnStatus,\n        notes: notes || null\n      });\n\n      if (error) throw error;\n      return { success: true, error: null };\n    } catch (error) {\n      console.error('Error updating return status:', error);\n      return { success: false, error };\n    }\n  },\n\n\n\n  // Get all found discs for admin (includes all return statuses)\n  async getAdminFoundDiscs() {\n    try {\n      const { data, error } = await supabase\n        .from('admin_found_discs')\n        .select('*');\n\n      if (error) throw error;\n      return { data, error: null };\n    } catch (error) {\n      console.error('Error fetching admin found discs:', error);\n      return { data: null, error };\n    }\n  },\n\n  // Bulk Turn-In Functions\n\n  // Create a new bulk turn-in record (rakerdiver only)\n  async createBulkTurnin(turninData: {\n    location_collected: string;\n    collection_date: string;\n    collection_time?: string;\n    disc_count: number;\n    turnin_location: string;\n    turnin_date: string;\n    turnin_time?: string;\n    notes?: string;\n  }) {\n    try {\n      const { data, error } = await supabase.rpc('create_bulk_turnin', {\n        p_location_collected: turninData.location_collected,\n        p_collection_date: turninData.collection_date,\n        p_disc_count: turninData.disc_count,\n        p_turnin_location: turninData.turnin_location,\n        p_turnin_date: turninData.turnin_date,\n        p_collection_time: turninData.collection_time || null,\n        p_turnin_time: turninData.turnin_time || null,\n        p_notes: turninData.notes || null\n      });\n\n      if (error) throw error;\n      return { data, error: null };\n    } catch (error) {\n      console.error('Error creating bulk turn-in:', error);\n      return { data: null, error };\n    }\n  },\n\n  // Get bulk turn-ins for current rakerdiver\n  async getRakerdiverTurnins() {\n    try {\n      const { data, error } = await supabase.rpc('get_rakerdiver_turnins');\n\n      if (error) throw error;\n      return { data, error: null };\n    } catch (error) {\n      console.error('Error fetching rakerdiver turn-ins:', error);\n      return { data: null, error };\n    }\n  },\n\n  // Verify a bulk turn-in (admin only)\n  async verifyBulkTurnin(turninId: string, verificationNotes?: string) {\n    try {\n      const { error } = await supabase.rpc('verify_bulk_turnin', {\n        p_turnin_id: turninId,\n        p_verification_notes: verificationNotes || null\n      });\n\n      if (error) throw error;\n      return { success: true, error: null };\n    } catch (error) {\n      console.error('Error verifying bulk turn-in:', error);\n      return { success: false, error };\n    }\n  },\n\n  // Create a payment record (admin only)\n  async createBulkTurninPayment(paymentData: {\n    bulk_turnin_id: string;\n    amount: number;\n    payment_method?: string;\n    payment_date?: string;\n    payment_notes?: string;\n  }) {\n    try {\n      const { data, error } = await supabase.rpc('create_bulk_turnin_payment', {\n        p_bulk_turnin_id: paymentData.bulk_turnin_id,\n        p_amount: paymentData.amount,\n        p_payment_method: paymentData.payment_method || null,\n        p_payment_date: paymentData.payment_date || null,\n        p_payment_notes: paymentData.payment_notes || null\n      });\n\n      if (error) throw error;\n      return { data, error: null };\n    } catch (error) {\n      console.error('Error creating payment:', error);\n      return { data: null, error };\n    }\n  },\n\n  // Confirm payment receipt (rakerdiver only)\n  async confirmPaymentReceipt(paymentId: string) {\n    try {\n      const { error } = await supabase.rpc('confirm_payment_receipt', {\n        p_payment_id: paymentId\n      });\n\n      if (error) throw error;\n      return { success: true, error: null };\n    } catch (error) {\n      console.error('Error confirming payment:', error);\n      return { success: false, error };\n    }\n  },\n\n  // Get all bulk turn-ins for admin\n  async getAdminBulkTurnins() {\n    try {\n      const { data, error } = await supabase\n        .from('admin_bulk_turnins')\n        .select('*');\n\n      if (error) throw error;\n      return { data, error: null };\n    } catch (error) {\n      console.error('Error fetching admin bulk turn-ins:', error);\n      return { data: null, error };\n    }\n  },\n\n  // Get payments for a specific bulk turn-in\n  async getBulkTurninPayments(turninId: string) {\n    try {\n      const { data, error } = await supabase\n        .from('bulk_turnin_payments')\n        .select('*')\n        .eq('bulk_turnin_id', turninId)\n        .order('created_at', { ascending: false });\n\n      if (error) throw error;\n      return { data, error: null };\n    } catch (error) {\n      console.error('Error fetching payments:', error);\n      return { data: null, error };\n    }\n  }\n}\n\n// Image upload service\nexport const imageService = {\n  // Upload multiple images to Supabase storage\n  async uploadImages(files: File[], userId: string): Promise<{ urls: string[], error: any }> {\n    try {\n      console.log(`Starting upload of ${files.length} files for user ${userId}`);\n\n      // Check if user is authenticated\n      const { data: { user } } = await supabase.auth.getUser();\n      if (!user) {\n        throw new Error('User not authenticated');\n      }\n\n      console.log('User authenticated:', user.id);\n\n      const uploadPromises = files.map(async (file, index) => {\n        // Generate unique filename\n        const fileExt = file.name.split('.').pop();\n        const fileName = `${userId}/${Date.now()}-${index}.${fileExt}`;\n\n        console.log(`Uploading file ${index + 1}/${files.length}: ${fileName}`);\n\n        const { data, error } = await supabase.storage\n          .from('disc-images')\n          .upload(fileName, file, {\n            cacheControl: '3600',\n            upsert: false\n          });\n\n        if (error) {\n          console.error(`Upload error for ${fileName}:`, error);\n          throw error;\n        }\n\n        console.log(`Upload successful for ${fileName}:`, data);\n\n        // Get public URL\n        const { data: urlData } = supabase.storage\n          .from('disc-images')\n          .getPublicUrl(fileName);\n\n        console.log(`Public URL for ${fileName}:`, urlData.publicUrl);\n        return urlData.publicUrl;\n      });\n\n      const urls = await Promise.all(uploadPromises);\n      console.log('All uploads completed:', urls);\n      return { urls, error: null };\n    } catch (error) {\n      console.error('Error uploading images:', error);\n      return { urls: [], error };\n    }\n  },\n\n  // Delete images from Supabase storage\n  async deleteImages(imageUrls: string[]): Promise<{ success: boolean, error: any }> {\n    try {\n      // Extract file paths from URLs\n      const filePaths = imageUrls.map(url => {\n        const urlParts = url.split('/');\n        const bucketIndex = urlParts.findIndex(part => part === 'disc-images');\n        if (bucketIndex !== -1 && bucketIndex < urlParts.length - 1) {\n          return urlParts.slice(bucketIndex + 1).join('/');\n        }\n        return null;\n      }).filter(Boolean) as string[];\n\n      if (filePaths.length === 0) {\n        return { success: true, error: null };\n      }\n\n      const { error } = await supabase.storage\n        .from('disc-images')\n        .remove(filePaths);\n\n      return { success: !error, error };\n    } catch (error) {\n      console.error('Error deleting images:', error);\n      return { success: false, error };\n    }\n  },\n\n  // Get optimized image URL (for future use with Supabase image transformations)\n  getOptimizedImageUrl(originalUrl: string, width?: number, height?: number): string {\n    // For now, return original URL\n    // In the future, you can add Supabase image transformation parameters\n    return originalUrl;\n  },\n\n  // Validate image file\n  validateImageFile(file: File, maxSizeMB: number = 10): { valid: boolean, error?: string } {\n    // Check file type\n    if (!file.type.startsWith('image/')) {\n      return { valid: false, error: 'File must be an image' };\n    }\n\n    // Check file size\n    const maxSizeBytes = maxSizeMB * 1024 * 1024;\n    if (file.size > maxSizeBytes) {\n      return { valid: false, error: `Image size must be less than ${maxSizeMB}MB` };\n    }\n\n    // Check supported formats\n    const supportedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];\n    if (!supportedTypes.includes(file.type)) {\n      return { valid: false, error: 'Supported formats: JPEG, PNG, WebP' };\n    }\n\n    return { valid: true };\n  }\n}\n\n// Export the service instance\nexport const supabaseService = {\n  getSources: async (): Promise<Source[]> => {\n    const { data, error } = await supabase\n      .from('sources')\n      .select('*')\n      .order('sort_order', { ascending: true })\n      .order('name', { ascending: true });\n\n    if (error) {\n      console.error('Error fetching sources:', error);\n      throw error;\n    }\n\n    return data || [];\n  },\n\n  getActiveSources: async (): Promise<Source[]> => {\n    const { data, error } = await supabase\n      .from('sources')\n      .select('*')\n      .eq('is_active', true)\n      .order('sort_order', { ascending: true })\n      .order('name', { ascending: true });\n\n    if (error) {\n      console.error('Error fetching active sources:', error);\n      throw error;\n    }\n\n    return data || [];\n  },\n\n  createSource: async (source: Omit<Source, 'id' | 'created_at' | 'updated_at'>): Promise<Source> => {\n    const { data, error } = await supabase\n      .from('sources')\n      .insert([source])\n      .select()\n      .single();\n\n    if (error) {\n      console.error('Error creating source:', error);\n      throw error;\n    }\n\n    return data;\n  },\n\n  updateSource: async (id: string, updates: Partial<Omit<Source, 'id' | 'created_at' | 'updated_at'>>): Promise<Source> => {\n    const { data, error } = await supabase\n      .from('sources')\n      .update(updates)\n      .eq('id', id)\n      .select()\n      .single();\n\n    if (error) {\n      console.error('Error updating source:', error);\n      throw error;\n    }\n\n    return data;\n  },\n\n  deleteSource: async (id: string): Promise<void> => {\n    const { error } = await supabase\n      .from('sources')\n      .delete()\n      .eq('id', id);\n\n    if (error) {\n      console.error('Error deleting source:', error);\n      throw error;\n    }\n  },\n\n  // Contact Attempts\n  getContactAttempts: async (discId: string) => {\n    try {\n      const { data, error } = await supabase\n        .from('contact_attempts')\n        .select(`\n          *,\n          attempted_by_profile:profiles!attempted_by_profile_id(\n            id,\n            full_name,\n            email\n          )\n        `)\n        .eq('found_disc_id', discId)\n        .order('attempted_at', { ascending: false });\n\n      if (error) throw error;\n      return { success: true, data: data || [] };\n    } catch (error) {\n      console.error('Error fetching contact attempts:', error);\n      return { success: false, error };\n    }\n  },\n\n  addContactAttempt: async (contactAttempt: Omit<ContactAttempt, 'id' | 'created_at' | 'updated_at'>) => {\n    try {\n      const { data, error } = await supabase\n        .from('contact_attempts')\n        .insert([contactAttempt])\n        .select();\n\n      if (error) throw error;\n      return { success: true, data: data?.[0] };\n    } catch (error) {\n      console.error('Error adding contact attempt:', error);\n      return { success: false, error };\n    }\n  },\n\n  updateContactAttempt: async (id: string, updates: Partial<ContactAttempt>) => {\n    try {\n      const { data, error } = await supabase\n        .from('contact_attempts')\n        .update({\n          ...updates,\n          updated_at: new Date().toISOString()\n        })\n        .eq('id', id)\n        .select();\n\n      if (error) throw error;\n      return { success: true, data: data?.[0] };\n    } catch (error) {\n      console.error('Error updating contact attempt:', error);\n      return { success: false, error };\n    }\n  },\n\n  // Default Source Management\n  getUserDefaultSource: async (userId: string): Promise<Source | null> => {\n    try {\n      // First get the user's default_source_id\n      const { data: profileData, error: profileError } = await supabase\n        .from('profiles')\n        .select('default_source_id')\n        .eq('id', userId)\n        .single();\n\n      if (profileError) throw profileError;\n\n      // If no default source is set, return null\n      if (!profileData?.default_source_id) {\n        return null;\n      }\n\n      // Get the source details\n      const { data: sourceData, error: sourceError } = await supabase\n        .from('sources')\n        .select('*')\n        .eq('id', profileData.default_source_id)\n        .eq('is_active', true)\n        .single();\n\n      if (sourceError) {\n        // If source doesn't exist or is inactive, return null\n        return null;\n      }\n\n      return sourceData as Source;\n    } catch (error) {\n      console.error('Error fetching user default source:', error);\n      return null;\n    }\n  },\n\n  updateUserDefaultSource: async (userId: string, sourceId: string | null): Promise<{ success: boolean; error?: any }> => {\n    try {\n      const { error } = await supabase\n        .from('profiles')\n        .update({\n          default_source_id: sourceId,\n          updated_at: new Date().toISOString()\n        })\n        .eq('id', userId);\n\n      if (error) throw error;\n      return { success: true };\n    } catch (error) {\n      console.error('Error updating user default source:', error);\n      return { success: false, error };\n    }\n  }\n};\n"], "mappings": "AAAA,SAASA,YAAY,QAAQ,uBAAuB;;AAEpD;AACA,MAAMC,WAAW,GAAGC,OAAO,CAACC,GAAG,CAACC,sBAAsB,IAAI,0BAA0B;AACpF,MAAMC,eAAe,GAAGH,OAAO,CAACC,GAAG,CAACG,2BAA2B,IAAI,UAAU;AAE7E,OAAO,MAAMC,QAAQ,GAAGP,YAAY,CAACC,WAAW,EAAEI,eAAe,CAAC;;AAElE;;AA8JA;AACA,OAAO,MAAMG,WAAW,GAAG;EACzB;EACA,MAAMC,cAAcA,CAAA,EAAG;IACrB,MAAMC,UAAU,GAAG,sCAAsC;IACzD,IAAI;MACF;MACA,MAAM;QAAEC,IAAI,EAAEC;MAAa,CAAC,GAAG,MAAML,QAAQ,CAC1CM,IAAI,CAAC,UAAU,CAAC,CAChBC,MAAM,CAAC,IAAI,CAAC,CACZC,EAAE,CAAC,IAAI,EAAEL,UAAU,CAAC,CACpBM,MAAM,CAAC,CAAC;MAEX,IAAI,CAACJ,YAAY,EAAE;QACjB;QACA,MAAM;UAAEK;QAAM,CAAC,GAAG,MAAMV,QAAQ,CAC7BM,IAAI,CAAC,UAAU,CAAC,CAChBK,MAAM,CAAC,CAAC;UACPC,EAAE,EAAET,UAAU;UACdU,KAAK,EAAE,qBAAqB;UAC5BC,SAAS,EAAE;QACb,CAAC,CAAC,CAAC;QAEL,IAAIJ,KAAK,EAAE;UACTK,OAAO,CAACC,IAAI,CAAC,6BAA6B,EAAEN,KAAK,CAAC;QACpD;MACF;MACA,OAAOP,UAAU;IACnB,CAAC,CAAC,OAAOO,KAAK,EAAE;MACdK,OAAO,CAACC,IAAI,CAAC,yBAAyB,EAAEN,KAAK,CAAC;MAC9C,OAAOP,UAAU;IACnB;EACF,CAAC;EAED;EACA,MAAMc,eAAeA,CAACC,QAAwE,EAAE;IAC9F,IAAI;MACF,MAAM;QAAEd,IAAI;QAAEM;MAAM,CAAC,GAAG,MAAMV,QAAQ,CACnCM,IAAI,CAAC,aAAa,CAAC,CACnBK,MAAM,CAAC,CAAC;QACP,GAAGO,QAAQ;QACXC,MAAM,EAAE;MACV,CAAC,CAAC,CAAC,CACFZ,MAAM,CAAC,CAAC,CACRE,MAAM,CAAC,CAAC;MAEX,IAAIC,KAAK,EAAE,MAAMA,KAAK;MACtB,OAAO;QAAEN,IAAI;QAAEM,KAAK,EAAE;MAAK,CAAC;IAC9B,CAAC,CAAC,OAAOA,KAAK,EAAE;MACdK,OAAO,CAACL,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;MAClD,OAAO;QAAEN,IAAI,EAAE,IAAI;QAAEM;MAAM,CAAC;IAC9B;EACF,CAAC;EAED;EACA,MAAMU,aAAaA,CAAA,EAAG;IACpB,IAAI;MACF;MACA,IAAI;QAAEhB,IAAI;QAAEM;MAAM,CAAC,GAAG,MAAMV,QAAQ,CACjCM,IAAI,CAAC,oBAAoB,CAAC,CAC1BC,MAAM,CAAC,GAAG,CAAC,CACXc,KAAK,CAAC,YAAY,EAAE;QAAEC,SAAS,EAAE;MAAM,CAAC,CAAC;;MAE5C;MACA,IAAIZ,KAAK,IAAKN,IAAI,IAAIA,IAAI,CAACmB,MAAM,GAAG,CAAC,IAAI,CAACnB,IAAI,CAAC,CAAC,CAAC,CAACoB,cAAc,CAAC,YAAY,CAAE,EAAE;QAC/ET,OAAO,CAACU,GAAG,CAAC,kCAAkC,CAAC;QAC/C,MAAMC,MAAM,GAAG,MAAM1B,QAAQ,CAC1BM,IAAI,CAAC,aAAa,CAAC,CACnBC,MAAM,CAAC,GAAG,CAAC,CACXC,EAAE,CAAC,QAAQ,EAAE,QAAQ,CAAC,CACtBa,KAAK,CAAC,YAAY,EAAE;UAAEC,SAAS,EAAE;QAAM,CAAC,CAAC;QAE5ClB,IAAI,GAAGsB,MAAM,CAACtB,IAAI;QAClBM,KAAK,GAAGgB,MAAM,CAAChB,KAAK;MACtB;MAEA,IAAIA,KAAK,EAAE,MAAMA,KAAK;MACtB,OAAO;QAAEN,IAAI;QAAEM,KAAK,EAAE;MAAK,CAAC;IAC9B,CAAC,CAAC,OAAOA,KAAK,EAAE;MACdK,OAAO,CAACL,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;MACnD,OAAO;QAAEN,IAAI,EAAE,IAAI;QAAEM;MAAM,CAAC;IAC9B;EACF,CAAC;EAED;EACA,MAAMiB,gBAAgBA,CAACC,cAOtB,EAAE;IACD,IAAI;MACF;MACA,IAAIC,KAAK,GAAG7B,QAAQ,CACjBM,IAAI,CAAC,oBAAoB,CAAC,CAC1BC,MAAM,CAAC,GAAG,CAAC;MAEd,IAAIqB,cAAc,CAACE,KAAK,EAAE;QACxBD,KAAK,GAAGA,KAAK,CAACE,KAAK,CAAC,OAAO,EAAE,IAAIH,cAAc,CAACE,KAAK,GAAG,CAAC;MAC3D;MACA,IAAIF,cAAc,CAACI,IAAI,EAAE;QACvBH,KAAK,GAAGA,KAAK,CAACE,KAAK,CAAC,MAAM,EAAE,IAAIH,cAAc,CAACI,IAAI,GAAG,CAAC;MACzD;MACA,IAAIJ,cAAc,CAACK,KAAK,EAAE;QACxBJ,KAAK,GAAGA,KAAK,CAACE,KAAK,CAAC,OAAO,EAAE,IAAIH,cAAc,CAACK,KAAK,GAAG,CAAC;MAC3D;MACA,IAAIL,cAAc,CAACM,QAAQ,EAAE;QAC3BL,KAAK,GAAGA,KAAK,CAACrB,EAAE,CAAC,WAAW,EAAEoB,cAAc,CAACM,QAAQ,CAAC;MACxD;MACA,IAAIN,cAAc,CAACO,aAAa,EAAE;QAChCN,KAAK,GAAGA,KAAK,CAACE,KAAK,CAAC,gBAAgB,EAAE,IAAIH,cAAc,CAACO,aAAa,GAAG,CAAC;MAC5E;MACA,IAAIP,cAAc,CAACQ,MAAM,EAAE;QACzB,MAAMC,SAAS,GAAGC,QAAQ,CAACV,cAAc,CAACQ,MAAM,CAAC;QACjD,IAAI,CAACG,KAAK,CAACF,SAAS,CAAC,EAAE;UACrBR,KAAK,GAAGA,KAAK,CAACrB,EAAE,CAAC,SAAS,EAAE6B,SAAS,CAAC;QACxC;MACF;MAEA,IAAI;QAAEjC,IAAI;QAAEM;MAAM,CAAC,GAAG,MAAMmB,KAAK,CAACR,KAAK,CAAC,YAAY,EAAE;QAAEC,SAAS,EAAE;MAAM,CAAC,CAAC;;MAE3E;MACA,IAAIZ,KAAK,IAAKN,IAAI,IAAIA,IAAI,CAACmB,MAAM,GAAG,CAAC,IAAI,CAACnB,IAAI,CAAC,CAAC,CAAC,CAACoB,cAAc,CAAC,YAAY,CAAE,EAAE;QAC/ET,OAAO,CAACU,GAAG,CAAC,6CAA6C,CAAC;QAC1D,IAAIe,SAAS,GAAGxC,QAAQ,CACrBM,IAAI,CAAC,aAAa,CAAC,CACnBC,MAAM,CAAC,GAAG,CAAC,CACXC,EAAE,CAAC,QAAQ,EAAE,QAAQ,CAAC;QAEzB,IAAIoB,cAAc,CAACE,KAAK,EAAE;UACxBU,SAAS,GAAGA,SAAS,CAACT,KAAK,CAAC,OAAO,EAAE,IAAIH,cAAc,CAACE,KAAK,GAAG,CAAC;QACnE;QACA,IAAIF,cAAc,CAACI,IAAI,EAAE;UACvBQ,SAAS,GAAGA,SAAS,CAACT,KAAK,CAAC,MAAM,EAAE,IAAIH,cAAc,CAACI,IAAI,GAAG,CAAC;QACjE;QACA,IAAIJ,cAAc,CAACK,KAAK,EAAE;UACxBO,SAAS,GAAGA,SAAS,CAACT,KAAK,CAAC,OAAO,EAAE,IAAIH,cAAc,CAACK,KAAK,GAAG,CAAC;QACnE;QACA,IAAIL,cAAc,CAACM,QAAQ,EAAE;UAC3BM,SAAS,GAAGA,SAAS,CAAChC,EAAE,CAAC,WAAW,EAAEoB,cAAc,CAACM,QAAQ,CAAC;QAChE;QACA,IAAIN,cAAc,CAACO,aAAa,EAAE;UAChCK,SAAS,GAAGA,SAAS,CAACT,KAAK,CAAC,gBAAgB,EAAE,IAAIH,cAAc,CAACO,aAAa,GAAG,CAAC;QACpF;QACA,IAAIP,cAAc,CAACQ,MAAM,EAAE;UACzB,MAAMC,SAAS,GAAGC,QAAQ,CAACV,cAAc,CAACQ,MAAM,CAAC;UACjD,IAAI,CAACG,KAAK,CAACF,SAAS,CAAC,EAAE;YACrBG,SAAS,GAAGA,SAAS,CAAChC,EAAE,CAAC,SAAS,EAAE6B,SAAS,CAAC;UAChD;QACF;QAEA,MAAMX,MAAM,GAAG,MAAMc,SAAS,CAACnB,KAAK,CAAC,YAAY,EAAE;UAAEC,SAAS,EAAE;QAAM,CAAC,CAAC;QACxElB,IAAI,GAAGsB,MAAM,CAACtB,IAAI;QAClBM,KAAK,GAAGgB,MAAM,CAAChB,KAAK;MACtB;MAEA,IAAIA,KAAK,EAAE,MAAMA,KAAK;MACtB,OAAO;QAAEN,IAAI;QAAEM,KAAK,EAAE;MAAK,CAAC;IAC9B,CAAC,CAAC,OAAOA,KAAK,EAAE;MACdK,OAAO,CAACL,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MACpD,OAAO;QAAEN,IAAI,EAAE,IAAI;QAAEM;MAAM,CAAC;IAC9B;EACF,CAAC;EAED;EACA,MAAM+B,yBAAyBA,CAACC,WAAmB,EAAE;IACnD,IAAI;MACF,IAAI,CAACA,WAAW,IAAIA,WAAW,CAACC,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;QAC7C;QACA,OAAO,IAAI,CAACvB,aAAa,CAAC,CAAC;MAC7B;;MAEA;MACA,MAAMwB,WAAW,GAAGF,WAAW,CAACC,IAAI,CAAC,CAAC,CAACE,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,KAAK,CAAC;;MAEjE;MACA,MAAM;QAAE1C,IAAI,EAAE2C,QAAQ;QAAErC,KAAK,EAAEsC;MAAW,CAAC,GAAG,MAAM,IAAI,CAAC5B,aAAa,CAAC,CAAC;MAExE,IAAI4B,UAAU,EAAE;QACd,MAAMA,UAAU;MAClB;MAEA,IAAI,CAACD,QAAQ,IAAIA,QAAQ,CAACxB,MAAM,KAAK,CAAC,EAAE;QACtC,OAAO;UAAEnB,IAAI,EAAE,EAAE;UAAEM,KAAK,EAAE;QAAK,CAAC;MAClC;;MAEA;MACA,MAAMuC,aAAa,GAAGF,QAAQ,CAACG,MAAM,CAACC,IAAI,IAAI;QAC5C;QACA,OAAOP,WAAW,CAACQ,KAAK,CAACC,IAAI,IAAI;UAC/B,MAAMC,SAAS,GAAGD,IAAI,CAACR,WAAW,CAAC,CAAC;;UAEpC;UACA,MAAMU,UAAU,GAAG,CACjBJ,IAAI,CAACrB,KAAK,EACVqB,IAAI,CAACnB,IAAI,EACTmB,IAAI,CAAClB,KAAK,EACVkB,IAAI,CAACK,cAAc,EACnBL,IAAI,CAACM,WAAW,EAChBN,IAAI,CAACO,UAAU,EACfP,IAAI,CAACQ,YAAY,EACjBR,IAAI,CAACS,YAAY,EACjBT,IAAI,CAACU,YAAY,EACjBV,IAAI,CAACW,SAAS,CACf;;UAED;UACA,MAAMC,WAAW,GAAGR,UAAU,CAACS,IAAI,CAACC,KAAK,IACvCA,KAAK,IAAIA,KAAK,CAACpB,WAAW,CAAC,CAAC,CAACqB,QAAQ,CAACZ,SAAS,CACjD,CAAC;;UAED;UACA,MAAMjB,SAAS,GAAGC,QAAQ,CAACe,IAAI,CAAC;UAChC,MAAMc,aAAa,GAAG,CAAC5B,KAAK,CAACF,SAAS,CAAC,IAAIc,IAAI,CAACiB,OAAO,KAAK/B,SAAS;UAErE,OAAO0B,WAAW,IAAII,aAAa;QACrC,CAAC,CAAC;MACJ,CAAC,CAAC;MAEF,OAAO;QAAE/D,IAAI,EAAE6C,aAAa;QAAEvC,KAAK,EAAE;MAAK,CAAC;IAC7C,CAAC,CAAC,OAAOA,KAAK,EAAE;MACdK,OAAO,CAACL,KAAK,CAAC,yCAAyC,EAAEA,KAAK,CAAC;MAC/D,OAAO;QAAEN,IAAI,EAAE,IAAI;QAAEM;MAAM,CAAC;IAC9B;EACF,CAAC;EAED;EACA,MAAM2D,cAAcA,CAAA,EAAG;IACrB,IAAI;MACF,MAAM;QAAE3D;MAAM,CAAC,GAAG,MAAMV,QAAQ,CAC7BM,IAAI,CAAC,aAAa,CAAC,CACnBC,MAAM,CAAC,OAAO,CAAC,CACf+D,KAAK,CAAC,CAAC,CAAC;MAEX,OAAO;QAAEC,SAAS,EAAE,CAAC7D,KAAK;QAAEA;MAAM,CAAC;IACrC,CAAC,CAAC,OAAOA,KAAK,EAAE;MACd,OAAO;QAAE6D,SAAS,EAAE,KAAK;QAAE7D;MAAM,CAAC;IACpC;EACF,CAAC;EAED;EACA,MAAM8D,kBAAkBA,CAACC,MAAc,EAAEC,YAA0B,EAAEC,KAAc,EAAE;IACnF,IAAI;MACF,MAAM;QAAEjE;MAAM,CAAC,GAAG,MAAMV,QAAQ,CAAC4E,GAAG,CAAC,2BAA2B,EAAE;QAChEC,OAAO,EAAEJ,MAAM;QACfK,UAAU,EAAEJ,YAAY;QACxBC,KAAK,EAAEA,KAAK,IAAI;MAClB,CAAC,CAAC;MAEF,IAAIjE,KAAK,EAAE,MAAMA,KAAK;MACtB,OAAO;QAAEqE,OAAO,EAAE,IAAI;QAAErE,KAAK,EAAE;MAAK,CAAC;IACvC,CAAC,CAAC,OAAOA,KAAK,EAAE;MACdK,OAAO,CAACL,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;MACrD,OAAO;QAAEqE,OAAO,EAAE,KAAK;QAAErE;MAAM,CAAC;IAClC;EACF,CAAC;EAID;EACA,MAAMsE,kBAAkBA,CAAA,EAAG;IACzB,IAAI;MACF,MAAM;QAAE5E,IAAI;QAAEM;MAAM,CAAC,GAAG,MAAMV,QAAQ,CACnCM,IAAI,CAAC,mBAAmB,CAAC,CACzBC,MAAM,CAAC,GAAG,CAAC;MAEd,IAAIG,KAAK,EAAE,MAAMA,KAAK;MACtB,OAAO;QAAEN,IAAI;QAAEM,KAAK,EAAE;MAAK,CAAC;IAC9B,CAAC,CAAC,OAAOA,KAAK,EAAE;MACdK,OAAO,CAACL,KAAK,CAAC,mCAAmC,EAAEA,KAAK,CAAC;MACzD,OAAO;QAAEN,IAAI,EAAE,IAAI;QAAEM;MAAM,CAAC;IAC9B;EACF,CAAC;EAED;;EAEA;EACA,MAAMuE,gBAAgBA,CAACC,UAStB,EAAE;IACD,IAAI;MACF,MAAM;QAAE9E,IAAI;QAAEM;MAAM,CAAC,GAAG,MAAMV,QAAQ,CAAC4E,GAAG,CAAC,oBAAoB,EAAE;QAC/DO,oBAAoB,EAAED,UAAU,CAACE,kBAAkB;QACnDC,iBAAiB,EAAEH,UAAU,CAACI,eAAe;QAC7CC,YAAY,EAAEL,UAAU,CAACM,UAAU;QACnCC,iBAAiB,EAAEP,UAAU,CAACQ,eAAe;QAC7CC,aAAa,EAAET,UAAU,CAACU,WAAW;QACrCC,iBAAiB,EAAEX,UAAU,CAACY,eAAe,IAAI,IAAI;QACrDC,aAAa,EAAEb,UAAU,CAACc,WAAW,IAAI,IAAI;QAC7CC,OAAO,EAAEf,UAAU,CAACP,KAAK,IAAI;MAC/B,CAAC,CAAC;MAEF,IAAIjE,KAAK,EAAE,MAAMA,KAAK;MACtB,OAAO;QAAEN,IAAI;QAAEM,KAAK,EAAE;MAAK,CAAC;IAC9B,CAAC,CAAC,OAAOA,KAAK,EAAE;MACdK,OAAO,CAACL,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MACpD,OAAO;QAAEN,IAAI,EAAE,IAAI;QAAEM;MAAM,CAAC;IAC9B;EACF,CAAC;EAED;EACA,MAAMwF,oBAAoBA,CAAA,EAAG;IAC3B,IAAI;MACF,MAAM;QAAE9F,IAAI;QAAEM;MAAM,CAAC,GAAG,MAAMV,QAAQ,CAAC4E,GAAG,CAAC,wBAAwB,CAAC;MAEpE,IAAIlE,KAAK,EAAE,MAAMA,KAAK;MACtB,OAAO;QAAEN,IAAI;QAAEM,KAAK,EAAE;MAAK,CAAC;IAC9B,CAAC,CAAC,OAAOA,KAAK,EAAE;MACdK,OAAO,CAACL,KAAK,CAAC,qCAAqC,EAAEA,KAAK,CAAC;MAC3D,OAAO;QAAEN,IAAI,EAAE,IAAI;QAAEM;MAAM,CAAC;IAC9B;EACF,CAAC;EAED;EACA,MAAMyF,gBAAgBA,CAACC,QAAgB,EAAEC,iBAA0B,EAAE;IACnE,IAAI;MACF,MAAM;QAAE3F;MAAM,CAAC,GAAG,MAAMV,QAAQ,CAAC4E,GAAG,CAAC,oBAAoB,EAAE;QACzD0B,WAAW,EAAEF,QAAQ;QACrBG,oBAAoB,EAAEF,iBAAiB,IAAI;MAC7C,CAAC,CAAC;MAEF,IAAI3F,KAAK,EAAE,MAAMA,KAAK;MACtB,OAAO;QAAEqE,OAAO,EAAE,IAAI;QAAErE,KAAK,EAAE;MAAK,CAAC;IACvC,CAAC,CAAC,OAAOA,KAAK,EAAE;MACdK,OAAO,CAACL,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;MACrD,OAAO;QAAEqE,OAAO,EAAE,KAAK;QAAErE;MAAM,CAAC;IAClC;EACF,CAAC;EAED;EACA,MAAM8F,uBAAuBA,CAACC,WAM7B,EAAE;IACD,IAAI;MACF,MAAM;QAAErG,IAAI;QAAEM;MAAM,CAAC,GAAG,MAAMV,QAAQ,CAAC4E,GAAG,CAAC,4BAA4B,EAAE;QACvE8B,gBAAgB,EAAED,WAAW,CAACE,cAAc;QAC5CC,QAAQ,EAAEH,WAAW,CAACI,MAAM;QAC5BC,gBAAgB,EAAEL,WAAW,CAACM,cAAc,IAAI,IAAI;QACpDC,cAAc,EAAEP,WAAW,CAACQ,YAAY,IAAI,IAAI;QAChDC,eAAe,EAAET,WAAW,CAACU,aAAa,IAAI;MAChD,CAAC,CAAC;MAEF,IAAIzG,KAAK,EAAE,MAAMA,KAAK;MACtB,OAAO;QAAEN,IAAI;QAAEM,KAAK,EAAE;MAAK,CAAC;IAC9B,CAAC,CAAC,OAAOA,KAAK,EAAE;MACdK,OAAO,CAACL,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MAC/C,OAAO;QAAEN,IAAI,EAAE,IAAI;QAAEM;MAAM,CAAC;IAC9B;EACF,CAAC;EAED;EACA,MAAM0G,qBAAqBA,CAACC,SAAiB,EAAE;IAC7C,IAAI;MACF,MAAM;QAAE3G;MAAM,CAAC,GAAG,MAAMV,QAAQ,CAAC4E,GAAG,CAAC,yBAAyB,EAAE;QAC9D0C,YAAY,EAAED;MAChB,CAAC,CAAC;MAEF,IAAI3G,KAAK,EAAE,MAAMA,KAAK;MACtB,OAAO;QAAEqE,OAAO,EAAE,IAAI;QAAErE,KAAK,EAAE;MAAK,CAAC;IACvC,CAAC,CAAC,OAAOA,KAAK,EAAE;MACdK,OAAO,CAACL,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACjD,OAAO;QAAEqE,OAAO,EAAE,KAAK;QAAErE;MAAM,CAAC;IAClC;EACF,CAAC;EAED;EACA,MAAM6G,mBAAmBA,CAAA,EAAG;IAC1B,IAAI;MACF,MAAM;QAAEnH,IAAI;QAAEM;MAAM,CAAC,GAAG,MAAMV,QAAQ,CACnCM,IAAI,CAAC,oBAAoB,CAAC,CAC1BC,MAAM,CAAC,GAAG,CAAC;MAEd,IAAIG,KAAK,EAAE,MAAMA,KAAK;MACtB,OAAO;QAAEN,IAAI;QAAEM,KAAK,EAAE;MAAK,CAAC;IAC9B,CAAC,CAAC,OAAOA,KAAK,EAAE;MACdK,OAAO,CAACL,KAAK,CAAC,qCAAqC,EAAEA,KAAK,CAAC;MAC3D,OAAO;QAAEN,IAAI,EAAE,IAAI;QAAEM;MAAM,CAAC;IAC9B;EACF,CAAC;EAED;EACA,MAAM8G,qBAAqBA,CAACpB,QAAgB,EAAE;IAC5C,IAAI;MACF,MAAM;QAAEhG,IAAI;QAAEM;MAAM,CAAC,GAAG,MAAMV,QAAQ,CACnCM,IAAI,CAAC,sBAAsB,CAAC,CAC5BC,MAAM,CAAC,GAAG,CAAC,CACXC,EAAE,CAAC,gBAAgB,EAAE4F,QAAQ,CAAC,CAC9B/E,KAAK,CAAC,YAAY,EAAE;QAAEC,SAAS,EAAE;MAAM,CAAC,CAAC;MAE5C,IAAIZ,KAAK,EAAE,MAAMA,KAAK;MACtB,OAAO;QAAEN,IAAI;QAAEM,KAAK,EAAE;MAAK,CAAC;IAC9B,CAAC,CAAC,OAAOA,KAAK,EAAE;MACdK,OAAO,CAACL,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;MAChD,OAAO;QAAEN,IAAI,EAAE,IAAI;QAAEM;MAAM,CAAC;IAC9B;EACF;AACF,CAAC;;AAED;AACA,OAAO,MAAM+G,YAAY,GAAG;EAC1B;EACA,MAAMC,YAAYA,CAACC,KAAa,EAAEC,MAAc,EAA2C;IACzF,IAAI;MACF7G,OAAO,CAACU,GAAG,CAAC,sBAAsBkG,KAAK,CAACpG,MAAM,mBAAmBqG,MAAM,EAAE,CAAC;;MAE1E;MACA,MAAM;QAAExH,IAAI,EAAE;UAAEyH;QAAK;MAAE,CAAC,GAAG,MAAM7H,QAAQ,CAAC8H,IAAI,CAACC,OAAO,CAAC,CAAC;MACxD,IAAI,CAACF,IAAI,EAAE;QACT,MAAM,IAAIG,KAAK,CAAC,wBAAwB,CAAC;MAC3C;MAEAjH,OAAO,CAACU,GAAG,CAAC,qBAAqB,EAAEoG,IAAI,CAACjH,EAAE,CAAC;MAE3C,MAAMqH,cAAc,GAAGN,KAAK,CAACO,GAAG,CAAC,OAAOC,IAAI,EAAEC,KAAK,KAAK;QACtD;QACA,MAAMC,OAAO,GAAGF,IAAI,CAACG,IAAI,CAACxF,KAAK,CAAC,GAAG,CAAC,CAACyF,GAAG,CAAC,CAAC;QAC1C,MAAMC,QAAQ,GAAG,GAAGZ,MAAM,IAAIa,IAAI,CAACC,GAAG,CAAC,CAAC,IAAIN,KAAK,IAAIC,OAAO,EAAE;QAE9DtH,OAAO,CAACU,GAAG,CAAC,kBAAkB2G,KAAK,GAAG,CAAC,IAAIT,KAAK,CAACpG,MAAM,KAAKiH,QAAQ,EAAE,CAAC;QAEvE,MAAM;UAAEpI,IAAI;UAAEM;QAAM,CAAC,GAAG,MAAMV,QAAQ,CAAC2I,OAAO,CAC3CrI,IAAI,CAAC,aAAa,CAAC,CACnBsI,MAAM,CAACJ,QAAQ,EAAEL,IAAI,EAAE;UACtBU,YAAY,EAAE,MAAM;UACpBC,MAAM,EAAE;QACV,CAAC,CAAC;QAEJ,IAAIpI,KAAK,EAAE;UACTK,OAAO,CAACL,KAAK,CAAC,oBAAoB8H,QAAQ,GAAG,EAAE9H,KAAK,CAAC;UACrD,MAAMA,KAAK;QACb;QAEAK,OAAO,CAACU,GAAG,CAAC,yBAAyB+G,QAAQ,GAAG,EAAEpI,IAAI,CAAC;;QAEvD;QACA,MAAM;UAAEA,IAAI,EAAE2I;QAAQ,CAAC,GAAG/I,QAAQ,CAAC2I,OAAO,CACvCrI,IAAI,CAAC,aAAa,CAAC,CACnB0I,YAAY,CAACR,QAAQ,CAAC;QAEzBzH,OAAO,CAACU,GAAG,CAAC,kBAAkB+G,QAAQ,GAAG,EAAEO,OAAO,CAACE,SAAS,CAAC;QAC7D,OAAOF,OAAO,CAACE,SAAS;MAC1B,CAAC,CAAC;MAEF,MAAMC,IAAI,GAAG,MAAMC,OAAO,CAACC,GAAG,CAACnB,cAAc,CAAC;MAC9ClH,OAAO,CAACU,GAAG,CAAC,wBAAwB,EAAEyH,IAAI,CAAC;MAC3C,OAAO;QAAEA,IAAI;QAAExI,KAAK,EAAE;MAAK,CAAC;IAC9B,CAAC,CAAC,OAAOA,KAAK,EAAE;MACdK,OAAO,CAACL,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MAC/C,OAAO;QAAEwI,IAAI,EAAE,EAAE;QAAExI;MAAM,CAAC;IAC5B;EACF,CAAC;EAED;EACA,MAAM2I,YAAYA,CAACC,SAAmB,EAA6C;IACjF,IAAI;MACF;MACA,MAAMC,SAAS,GAAGD,SAAS,CAACpB,GAAG,CAACsB,GAAG,IAAI;QACrC,MAAMC,QAAQ,GAAGD,GAAG,CAAC1G,KAAK,CAAC,GAAG,CAAC;QAC/B,MAAM4G,WAAW,GAAGD,QAAQ,CAACE,SAAS,CAACC,IAAI,IAAIA,IAAI,KAAK,aAAa,CAAC;QACtE,IAAIF,WAAW,KAAK,CAAC,CAAC,IAAIA,WAAW,GAAGD,QAAQ,CAAClI,MAAM,GAAG,CAAC,EAAE;UAC3D,OAAOkI,QAAQ,CAACI,KAAK,CAACH,WAAW,GAAG,CAAC,CAAC,CAACI,IAAI,CAAC,GAAG,CAAC;QAClD;QACA,OAAO,IAAI;MACb,CAAC,CAAC,CAAC5G,MAAM,CAAC6G,OAAO,CAAa;MAE9B,IAAIR,SAAS,CAAChI,MAAM,KAAK,CAAC,EAAE;QAC1B,OAAO;UAAEwD,OAAO,EAAE,IAAI;UAAErE,KAAK,EAAE;QAAK,CAAC;MACvC;MAEA,MAAM;QAAEA;MAAM,CAAC,GAAG,MAAMV,QAAQ,CAAC2I,OAAO,CACrCrI,IAAI,CAAC,aAAa,CAAC,CACnB0J,MAAM,CAACT,SAAS,CAAC;MAEpB,OAAO;QAAExE,OAAO,EAAE,CAACrE,KAAK;QAAEA;MAAM,CAAC;IACnC,CAAC,CAAC,OAAOA,KAAK,EAAE;MACdK,OAAO,CAACL,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;MAC9C,OAAO;QAAEqE,OAAO,EAAE,KAAK;QAAErE;MAAM,CAAC;IAClC;EACF,CAAC;EAED;EACAuJ,oBAAoBA,CAACC,WAAmB,EAAEC,KAAc,EAAEC,MAAe,EAAU;IACjF;IACA;IACA,OAAOF,WAAW;EACpB,CAAC;EAED;EACAG,iBAAiBA,CAAClC,IAAU,EAAEmC,SAAiB,GAAG,EAAE,EAAsC;IACxF;IACA,IAAI,CAACnC,IAAI,CAACoC,IAAI,CAACC,UAAU,CAAC,QAAQ,CAAC,EAAE;MACnC,OAAO;QAAEC,KAAK,EAAE,KAAK;QAAE/J,KAAK,EAAE;MAAwB,CAAC;IACzD;;IAEA;IACA,MAAMgK,YAAY,GAAGJ,SAAS,GAAG,IAAI,GAAG,IAAI;IAC5C,IAAInC,IAAI,CAACwC,IAAI,GAAGD,YAAY,EAAE;MAC5B,OAAO;QAAED,KAAK,EAAE,KAAK;QAAE/J,KAAK,EAAE,gCAAgC4J,SAAS;MAAK,CAAC;IAC/E;;IAEA;IACA,MAAMM,cAAc,GAAG,CAAC,YAAY,EAAE,WAAW,EAAE,WAAW,EAAE,YAAY,CAAC;IAC7E,IAAI,CAACA,cAAc,CAAC1G,QAAQ,CAACiE,IAAI,CAACoC,IAAI,CAAC,EAAE;MACvC,OAAO;QAAEE,KAAK,EAAE,KAAK;QAAE/J,KAAK,EAAE;MAAqC,CAAC;IACtE;IAEA,OAAO;MAAE+J,KAAK,EAAE;IAAK,CAAC;EACxB;AACF,CAAC;;AAED;AACA,OAAO,MAAMI,eAAe,GAAG;EAC7BC,UAAU,EAAE,MAAAA,CAAA,KAA+B;IACzC,MAAM;MAAE1K,IAAI;MAAEM;IAAM,CAAC,GAAG,MAAMV,QAAQ,CACnCM,IAAI,CAAC,SAAS,CAAC,CACfC,MAAM,CAAC,GAAG,CAAC,CACXc,KAAK,CAAC,YAAY,EAAE;MAAEC,SAAS,EAAE;IAAK,CAAC,CAAC,CACxCD,KAAK,CAAC,MAAM,EAAE;MAAEC,SAAS,EAAE;IAAK,CAAC,CAAC;IAErC,IAAIZ,KAAK,EAAE;MACTK,OAAO,CAACL,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MAC/C,MAAMA,KAAK;IACb;IAEA,OAAON,IAAI,IAAI,EAAE;EACnB,CAAC;EAED2K,gBAAgB,EAAE,MAAAA,CAAA,KAA+B;IAC/C,MAAM;MAAE3K,IAAI;MAAEM;IAAM,CAAC,GAAG,MAAMV,QAAQ,CACnCM,IAAI,CAAC,SAAS,CAAC,CACfC,MAAM,CAAC,GAAG,CAAC,CACXC,EAAE,CAAC,WAAW,EAAE,IAAI,CAAC,CACrBa,KAAK,CAAC,YAAY,EAAE;MAAEC,SAAS,EAAE;IAAK,CAAC,CAAC,CACxCD,KAAK,CAAC,MAAM,EAAE;MAAEC,SAAS,EAAE;IAAK,CAAC,CAAC;IAErC,IAAIZ,KAAK,EAAE;MACTK,OAAO,CAACL,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;MACtD,MAAMA,KAAK;IACb;IAEA,OAAON,IAAI,IAAI,EAAE;EACnB,CAAC;EAED4K,YAAY,EAAE,MAAOC,MAAwD,IAAsB;IACjG,MAAM;MAAE7K,IAAI;MAAEM;IAAM,CAAC,GAAG,MAAMV,QAAQ,CACnCM,IAAI,CAAC,SAAS,CAAC,CACfK,MAAM,CAAC,CAACsK,MAAM,CAAC,CAAC,CAChB1K,MAAM,CAAC,CAAC,CACRE,MAAM,CAAC,CAAC;IAEX,IAAIC,KAAK,EAAE;MACTK,OAAO,CAACL,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;MAC9C,MAAMA,KAAK;IACb;IAEA,OAAON,IAAI;EACb,CAAC;EAED8K,YAAY,EAAE,MAAAA,CAAOtK,EAAU,EAAEuK,OAAkE,KAAsB;IACvH,MAAM;MAAE/K,IAAI;MAAEM;IAAM,CAAC,GAAG,MAAMV,QAAQ,CACnCM,IAAI,CAAC,SAAS,CAAC,CACf8K,MAAM,CAACD,OAAO,CAAC,CACf3K,EAAE,CAAC,IAAI,EAAEI,EAAE,CAAC,CACZL,MAAM,CAAC,CAAC,CACRE,MAAM,CAAC,CAAC;IAEX,IAAIC,KAAK,EAAE;MACTK,OAAO,CAACL,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;MAC9C,MAAMA,KAAK;IACb;IAEA,OAAON,IAAI;EACb,CAAC;EAEDiL,YAAY,EAAE,MAAOzK,EAAU,IAAoB;IACjD,MAAM;MAAEF;IAAM,CAAC,GAAG,MAAMV,QAAQ,CAC7BM,IAAI,CAAC,SAAS,CAAC,CACfgL,MAAM,CAAC,CAAC,CACR9K,EAAE,CAAC,IAAI,EAAEI,EAAE,CAAC;IAEf,IAAIF,KAAK,EAAE;MACTK,OAAO,CAACL,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;MAC9C,MAAMA,KAAK;IACb;EACF,CAAC;EAED;EACA6K,kBAAkB,EAAE,MAAO9G,MAAc,IAAK;IAC5C,IAAI;MACF,MAAM;QAAErE,IAAI;QAAEM;MAAM,CAAC,GAAG,MAAMV,QAAQ,CACnCM,IAAI,CAAC,kBAAkB,CAAC,CACxBC,MAAM,CAAC;AAChB;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,CAAC,CACDC,EAAE,CAAC,eAAe,EAAEiE,MAAM,CAAC,CAC3BpD,KAAK,CAAC,cAAc,EAAE;QAAEC,SAAS,EAAE;MAAM,CAAC,CAAC;MAE9C,IAAIZ,KAAK,EAAE,MAAMA,KAAK;MACtB,OAAO;QAAEqE,OAAO,EAAE,IAAI;QAAE3E,IAAI,EAAEA,IAAI,IAAI;MAAG,CAAC;IAC5C,CAAC,CAAC,OAAOM,KAAK,EAAE;MACdK,OAAO,CAACL,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;MACxD,OAAO;QAAEqE,OAAO,EAAE,KAAK;QAAErE;MAAM,CAAC;IAClC;EACF,CAAC;EAED8K,iBAAiB,EAAE,MAAOC,cAAwE,IAAK;IACrG,IAAI;MACF,MAAM;QAAErL,IAAI;QAAEM;MAAM,CAAC,GAAG,MAAMV,QAAQ,CACnCM,IAAI,CAAC,kBAAkB,CAAC,CACxBK,MAAM,CAAC,CAAC8K,cAAc,CAAC,CAAC,CACxBlL,MAAM,CAAC,CAAC;MAEX,IAAIG,KAAK,EAAE,MAAMA,KAAK;MACtB,OAAO;QAAEqE,OAAO,EAAE,IAAI;QAAE3E,IAAI,EAAEA,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAG,CAAC;MAAE,CAAC;IAC3C,CAAC,CAAC,OAAOM,KAAK,EAAE;MACdK,OAAO,CAACL,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;MACrD,OAAO;QAAEqE,OAAO,EAAE,KAAK;QAAErE;MAAM,CAAC;IAClC;EACF,CAAC;EAEDgL,oBAAoB,EAAE,MAAAA,CAAO9K,EAAU,EAAEuK,OAAgC,KAAK;IAC5E,IAAI;MACF,MAAM;QAAE/K,IAAI;QAAEM;MAAM,CAAC,GAAG,MAAMV,QAAQ,CACnCM,IAAI,CAAC,kBAAkB,CAAC,CACxB8K,MAAM,CAAC;QACN,GAAGD,OAAO;QACVQ,UAAU,EAAE,IAAIlD,IAAI,CAAC,CAAC,CAACmD,WAAW,CAAC;MACrC,CAAC,CAAC,CACDpL,EAAE,CAAC,IAAI,EAAEI,EAAE,CAAC,CACZL,MAAM,CAAC,CAAC;MAEX,IAAIG,KAAK,EAAE,MAAMA,KAAK;MACtB,OAAO;QAAEqE,OAAO,EAAE,IAAI;QAAE3E,IAAI,EAAEA,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAG,CAAC;MAAE,CAAC;IAC3C,CAAC,CAAC,OAAOM,KAAK,EAAE;MACdK,OAAO,CAACL,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;MACvD,OAAO;QAAEqE,OAAO,EAAE,KAAK;QAAErE;MAAM,CAAC;IAClC;EACF,CAAC;EAED;EACAmL,oBAAoB,EAAE,MAAOjE,MAAc,IAA6B;IACtE,IAAI;MACF;MACA,MAAM;QAAExH,IAAI,EAAE0L,WAAW;QAAEpL,KAAK,EAAEqL;MAAa,CAAC,GAAG,MAAM/L,QAAQ,CAC9DM,IAAI,CAAC,UAAU,CAAC,CAChBC,MAAM,CAAC,mBAAmB,CAAC,CAC3BC,EAAE,CAAC,IAAI,EAAEoH,MAAM,CAAC,CAChBnH,MAAM,CAAC,CAAC;MAEX,IAAIsL,YAAY,EAAE,MAAMA,YAAY;;MAEpC;MACA,IAAI,EAACD,WAAW,aAAXA,WAAW,eAAXA,WAAW,CAAEE,iBAAiB,GAAE;QACnC,OAAO,IAAI;MACb;;MAEA;MACA,MAAM;QAAE5L,IAAI,EAAE6L,UAAU;QAAEvL,KAAK,EAAEwL;MAAY,CAAC,GAAG,MAAMlM,QAAQ,CAC5DM,IAAI,CAAC,SAAS,CAAC,CACfC,MAAM,CAAC,GAAG,CAAC,CACXC,EAAE,CAAC,IAAI,EAAEsL,WAAW,CAACE,iBAAiB,CAAC,CACvCxL,EAAE,CAAC,WAAW,EAAE,IAAI,CAAC,CACrBC,MAAM,CAAC,CAAC;MAEX,IAAIyL,WAAW,EAAE;QACf;QACA,OAAO,IAAI;MACb;MAEA,OAAOD,UAAU;IACnB,CAAC,CAAC,OAAOvL,KAAK,EAAE;MACdK,OAAO,CAACL,KAAK,CAAC,qCAAqC,EAAEA,KAAK,CAAC;MAC3D,OAAO,IAAI;IACb;EACF,CAAC;EAEDyL,uBAAuB,EAAE,MAAAA,CAAOvE,MAAc,EAAEwE,QAAuB,KAAiD;IACtH,IAAI;MACF,MAAM;QAAE1L;MAAM,CAAC,GAAG,MAAMV,QAAQ,CAC7BM,IAAI,CAAC,UAAU,CAAC,CAChB8K,MAAM,CAAC;QACNY,iBAAiB,EAAEI,QAAQ;QAC3BT,UAAU,EAAE,IAAIlD,IAAI,CAAC,CAAC,CAACmD,WAAW,CAAC;MACrC,CAAC,CAAC,CACDpL,EAAE,CAAC,IAAI,EAAEoH,MAAM,CAAC;MAEnB,IAAIlH,KAAK,EAAE,MAAMA,KAAK;MACtB,OAAO;QAAEqE,OAAO,EAAE;MAAK,CAAC;IAC1B,CAAC,CAAC,OAAOrE,KAAK,EAAE;MACdK,OAAO,CAACL,KAAK,CAAC,qCAAqC,EAAEA,KAAK,CAAC;MAC3D,OAAO;QAAEqE,OAAO,EAAE,KAAK;QAAErE;MAAM,CAAC;IAClC;EACF;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}