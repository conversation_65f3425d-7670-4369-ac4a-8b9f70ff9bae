{"ast": null, "code": "/**\n * SMS Service for sending text messages via <PERSON>wi<PERSON>\n * Handles automatic SMS notifications when found discs are entered\n */\n\nimport { normalizePhoneNumber, validatePhoneNumber } from '../utils/phoneUtils';\n\n// Twilio configuration from environment variables\nconst TWILIO_ACCOUNT_SID = process.env.REACT_APP_TWILIO_ACCOUNT_SID;\nconst TWILIO_AUTH_TOKEN = process.env.REACT_APP_TWILIO_AUTH_TOKEN;\nconst TWILIO_PHONE_NUMBER = process.env.REACT_APP_TWILIO_PHONE_NUMBER;\n/**\n * Check if SMS is configured and available\n */\nexport function isSMSConfigured() {\n  return !!(TWILIO_ACCOUNT_SID && TWILIO_AUTH_TOKEN && TWILIO_PHONE_NUMBER);\n}\n\n/**\n * Validate a phone number for SMS sending\n * Only accepts 10-digit US phone numbers as per requirements\n */\nexport function validatePhoneForSMS(phone) {\n  if (!phone) {\n    return {\n      isValid: false,\n      error: 'Phone number is required'\n    };\n  }\n  const validation = validatePhoneNumber(phone);\n  if (!validation.isValid) {\n    return {\n      isValid: false,\n      error: validation.error\n    };\n  }\n  const normalized = normalizePhoneNumber(phone);\n  if (!normalized) {\n    return {\n      isValid: false,\n      error: 'Unable to normalize phone number'\n    };\n  }\n\n  // Check if it's a valid 10-digit US number\n  if (!normalized.startsWith('+1') || normalized.length !== 12) {\n    return {\n      isValid: false,\n      error: 'Only 10-digit US phone numbers are supported for SMS'\n    };\n  }\n  return {\n    isValid: true,\n    normalizedPhone: normalized\n  };\n}\n\n/**\n * Send SMS message via Twilio\n * This is a client-side function that would typically call a backend API\n * For now, it simulates the SMS sending process\n */\nexport async function sendSMS(smsMessage) {\n  try {\n    // Validate SMS configuration\n    if (!isSMSConfigured()) {\n      console.warn('SMS not configured - Twilio credentials missing');\n      return {\n        success: false,\n        error: 'SMS service not configured'\n      };\n    }\n\n    // Validate phone number\n    const phoneValidation = validatePhoneForSMS(smsMessage.to);\n    if (!phoneValidation.isValid) {\n      return {\n        success: false,\n        error: phoneValidation.error,\n        phoneNumber: smsMessage.to\n      };\n    }\n\n    // In a real implementation, this would call a backend API that uses Twilio\n    // For now, we'll simulate the API call\n    console.log('SMS would be sent:', {\n      to: phoneValidation.normalizedPhone,\n      message: smsMessage.message,\n      from: TWILIO_PHONE_NUMBER\n    });\n\n    // Simulate API call delay\n    await new Promise(resolve => setTimeout(resolve, 1000));\n\n    // For development, always return success\n    // In production, this would make an actual API call\n    return {\n      success: true,\n      messageId: `sim_${Date.now()}`,\n      phoneNumber: phoneValidation.normalizedPhone\n    };\n  } catch (error) {\n    console.error('SMS sending failed:', error);\n    return {\n      success: false,\n      error: error instanceof Error ? error.message : 'Unknown error occurred'\n    };\n  }\n}\n\n/**\n * Send found disc notification SMS\n * Uses the source's message template if available\n */\nexport async function sendFoundDiscNotification(phoneNumber, messageTemplate, foundDiscId, sourceId) {\n  // Don't send if no message template\n  if (!messageTemplate || messageTemplate.trim() === '') {\n    return {\n      success: false,\n      error: 'No message template available for this source'\n    };\n  }\n  const smsMessage = {\n    to: phoneNumber,\n    message: messageTemplate.trim(),\n    foundDiscId,\n    sourceId\n  };\n  return await sendSMS(smsMessage);\n}\n\n/**\n * Format SMS message with disc details (future enhancement)\n * This could be used to personalize messages with disc information\n */\nexport function formatSMSMessage(template, discDetails) {\n  // For now, just return the template as-is\n  // Future enhancement could replace placeholders like {{brand}}, {{mold}}, etc.\n  return template;\n}", "map": {"version": 3, "names": ["normalizePhoneNumber", "validatePhoneNumber", "TWILIO_ACCOUNT_SID", "process", "env", "REACT_APP_TWILIO_ACCOUNT_SID", "TWILIO_AUTH_TOKEN", "REACT_APP_TWILIO_AUTH_TOKEN", "TWILIO_PHONE_NUMBER", "REACT_APP_TWILIO_PHONE_NUMBER", "isSMSConfigured", "validatePhoneForSMS", "phone", "<PERSON><PERSON><PERSON><PERSON>", "error", "validation", "normalized", "startsWith", "length", "normalizedPhone", "sendSMS", "smsMessage", "console", "warn", "success", "phoneValidation", "to", "phoneNumber", "log", "message", "from", "Promise", "resolve", "setTimeout", "messageId", "Date", "now", "Error", "sendFoundDiscNotification", "messageTemplate", "foundDiscId", "sourceId", "trim", "formatSMSMessage", "template", "discDetails"], "sources": ["C:/Users/<USER>/lostandfound/src/lib/smsService.ts"], "sourcesContent": ["/**\n * SMS Service for sending text messages via <PERSON><PERSON><PERSON>\n * Handles automatic SMS notifications when found discs are entered\n */\n\nimport { normalizePhoneNumber, validatePhoneNumber } from '../utils/phoneUtils';\n\n// Twilio configuration from environment variables\nconst TWILIO_ACCOUNT_SID = process.env.REACT_APP_TWILIO_ACCOUNT_SID;\nconst TWILIO_AUTH_TOKEN = process.env.REACT_APP_TWILIO_AUTH_TOKEN;\nconst TWILIO_PHONE_NUMBER = process.env.REACT_APP_TWILIO_PHONE_NUMBER;\n\nexport interface SMSResult {\n  success: boolean;\n  messageId?: string;\n  error?: string;\n  phoneNumber?: string;\n}\n\nexport interface SMSMessage {\n  to: string;\n  message: string;\n  foundDiscId?: string;\n  sourceId?: string;\n}\n\n/**\n * Check if SMS is configured and available\n */\nexport function isSMSConfigured(): boolean {\n  return !!(TWILIO_ACCOUNT_SID && TWILIO_AUTH_TOKEN && TWILIO_PHONE_NUMBER);\n}\n\n/**\n * Validate a phone number for SMS sending\n * Only accepts 10-digit US phone numbers as per requirements\n */\nexport function validatePhoneForSMS(phone: string | null | undefined): {\n  isValid: boolean;\n  normalizedPhone?: string;\n  error?: string;\n} {\n  if (!phone) {\n    return { isValid: false, error: 'Phone number is required' };\n  }\n\n  const validation = validatePhoneNumber(phone);\n  if (!validation.isValid) {\n    return { isValid: false, error: validation.error };\n  }\n\n  const normalized = normalizePhoneNumber(phone);\n  if (!normalized) {\n    return { isValid: false, error: 'Unable to normalize phone number' };\n  }\n\n  // Check if it's a valid 10-digit US number\n  if (!normalized.startsWith('+1') || normalized.length !== 12) {\n    return { isValid: false, error: 'Only 10-digit US phone numbers are supported for SMS' };\n  }\n\n  return { isValid: true, normalizedPhone: normalized };\n}\n\n/**\n * Send SMS message via Twilio\n * This is a client-side function that would typically call a backend API\n * For now, it simulates the SMS sending process\n */\nexport async function sendSMS(smsMessage: SMSMessage): Promise<SMSResult> {\n  try {\n    // Validate SMS configuration\n    if (!isSMSConfigured()) {\n      console.warn('SMS not configured - Twilio credentials missing');\n      return {\n        success: false,\n        error: 'SMS service not configured'\n      };\n    }\n\n    // Validate phone number\n    const phoneValidation = validatePhoneForSMS(smsMessage.to);\n    if (!phoneValidation.isValid) {\n      return {\n        success: false,\n        error: phoneValidation.error,\n        phoneNumber: smsMessage.to\n      };\n    }\n\n    // In a real implementation, this would call a backend API that uses Twilio\n    // For now, we'll simulate the API call\n    console.log('SMS would be sent:', {\n      to: phoneValidation.normalizedPhone,\n      message: smsMessage.message,\n      from: TWILIO_PHONE_NUMBER\n    });\n\n    // Simulate API call delay\n    await new Promise(resolve => setTimeout(resolve, 1000));\n\n    // For development, always return success\n    // In production, this would make an actual API call\n    return {\n      success: true,\n      messageId: `sim_${Date.now()}`,\n      phoneNumber: phoneValidation.normalizedPhone\n    };\n\n  } catch (error) {\n    console.error('SMS sending failed:', error);\n    return {\n      success: false,\n      error: error instanceof Error ? error.message : 'Unknown error occurred'\n    };\n  }\n}\n\n/**\n * Send found disc notification SMS\n * Uses the source's message template if available\n */\nexport async function sendFoundDiscNotification(\n  phoneNumber: string,\n  messageTemplate: string | null | undefined,\n  foundDiscId: string,\n  sourceId: string\n): Promise<SMSResult> {\n  // Don't send if no message template\n  if (!messageTemplate || messageTemplate.trim() === '') {\n    return {\n      success: false,\n      error: 'No message template available for this source'\n    };\n  }\n\n  const smsMessage: SMSMessage = {\n    to: phoneNumber,\n    message: messageTemplate.trim(),\n    foundDiscId,\n    sourceId\n  };\n\n  return await sendSMS(smsMessage);\n}\n\n/**\n * Format SMS message with disc details (future enhancement)\n * This could be used to personalize messages with disc information\n */\nexport function formatSMSMessage(template: string, discDetails?: any): string {\n  // For now, just return the template as-is\n  // Future enhancement could replace placeholders like {{brand}}, {{mold}}, etc.\n  return template;\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;;AAEA,SAASA,oBAAoB,EAAEC,mBAAmB,QAAQ,qBAAqB;;AAE/E;AACA,MAAMC,kBAAkB,GAAGC,OAAO,CAACC,GAAG,CAACC,4BAA4B;AACnE,MAAMC,iBAAiB,GAAGH,OAAO,CAACC,GAAG,CAACG,2BAA2B;AACjE,MAAMC,mBAAmB,GAAGL,OAAO,CAACC,GAAG,CAACK,6BAA6B;AAgBrE;AACA;AACA;AACA,OAAO,SAASC,eAAeA,CAAA,EAAY;EACzC,OAAO,CAAC,EAAER,kBAAkB,IAAII,iBAAiB,IAAIE,mBAAmB,CAAC;AAC3E;;AAEA;AACA;AACA;AACA;AACA,OAAO,SAASG,mBAAmBA,CAACC,KAAgC,EAIlE;EACA,IAAI,CAACA,KAAK,EAAE;IACV,OAAO;MAAEC,OAAO,EAAE,KAAK;MAAEC,KAAK,EAAE;IAA2B,CAAC;EAC9D;EAEA,MAAMC,UAAU,GAAGd,mBAAmB,CAACW,KAAK,CAAC;EAC7C,IAAI,CAACG,UAAU,CAACF,OAAO,EAAE;IACvB,OAAO;MAAEA,OAAO,EAAE,KAAK;MAAEC,KAAK,EAAEC,UAAU,CAACD;IAAM,CAAC;EACpD;EAEA,MAAME,UAAU,GAAGhB,oBAAoB,CAACY,KAAK,CAAC;EAC9C,IAAI,CAACI,UAAU,EAAE;IACf,OAAO;MAAEH,OAAO,EAAE,KAAK;MAAEC,KAAK,EAAE;IAAmC,CAAC;EACtE;;EAEA;EACA,IAAI,CAACE,UAAU,CAACC,UAAU,CAAC,IAAI,CAAC,IAAID,UAAU,CAACE,MAAM,KAAK,EAAE,EAAE;IAC5D,OAAO;MAAEL,OAAO,EAAE,KAAK;MAAEC,KAAK,EAAE;IAAuD,CAAC;EAC1F;EAEA,OAAO;IAAED,OAAO,EAAE,IAAI;IAAEM,eAAe,EAAEH;EAAW,CAAC;AACvD;;AAEA;AACA;AACA;AACA;AACA;AACA,OAAO,eAAeI,OAAOA,CAACC,UAAsB,EAAsB;EACxE,IAAI;IACF;IACA,IAAI,CAACX,eAAe,CAAC,CAAC,EAAE;MACtBY,OAAO,CAACC,IAAI,CAAC,iDAAiD,CAAC;MAC/D,OAAO;QACLC,OAAO,EAAE,KAAK;QACdV,KAAK,EAAE;MACT,CAAC;IACH;;IAEA;IACA,MAAMW,eAAe,GAAGd,mBAAmB,CAACU,UAAU,CAACK,EAAE,CAAC;IAC1D,IAAI,CAACD,eAAe,CAACZ,OAAO,EAAE;MAC5B,OAAO;QACLW,OAAO,EAAE,KAAK;QACdV,KAAK,EAAEW,eAAe,CAACX,KAAK;QAC5Ba,WAAW,EAAEN,UAAU,CAACK;MAC1B,CAAC;IACH;;IAEA;IACA;IACAJ,OAAO,CAACM,GAAG,CAAC,oBAAoB,EAAE;MAChCF,EAAE,EAAED,eAAe,CAACN,eAAe;MACnCU,OAAO,EAAER,UAAU,CAACQ,OAAO;MAC3BC,IAAI,EAAEtB;IACR,CAAC,CAAC;;IAEF;IACA,MAAM,IAAIuB,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,IAAI,CAAC,CAAC;;IAEvD;IACA;IACA,OAAO;MACLR,OAAO,EAAE,IAAI;MACbU,SAAS,EAAE,OAAOC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE;MAC9BT,WAAW,EAAEF,eAAe,CAACN;IAC/B,CAAC;EAEH,CAAC,CAAC,OAAOL,KAAK,EAAE;IACdQ,OAAO,CAACR,KAAK,CAAC,qBAAqB,EAAEA,KAAK,CAAC;IAC3C,OAAO;MACLU,OAAO,EAAE,KAAK;MACdV,KAAK,EAAEA,KAAK,YAAYuB,KAAK,GAAGvB,KAAK,CAACe,OAAO,GAAG;IAClD,CAAC;EACH;AACF;;AAEA;AACA;AACA;AACA;AACA,OAAO,eAAeS,yBAAyBA,CAC7CX,WAAmB,EACnBY,eAA0C,EAC1CC,WAAmB,EACnBC,QAAgB,EACI;EACpB;EACA,IAAI,CAACF,eAAe,IAAIA,eAAe,CAACG,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;IACrD,OAAO;MACLlB,OAAO,EAAE,KAAK;MACdV,KAAK,EAAE;IACT,CAAC;EACH;EAEA,MAAMO,UAAsB,GAAG;IAC7BK,EAAE,EAAEC,WAAW;IACfE,OAAO,EAAEU,eAAe,CAACG,IAAI,CAAC,CAAC;IAC/BF,WAAW;IACXC;EACF,CAAC;EAED,OAAO,MAAMrB,OAAO,CAACC,UAAU,CAAC;AAClC;;AAEA;AACA;AACA;AACA;AACA,OAAO,SAASsB,gBAAgBA,CAACC,QAAgB,EAAEC,WAAiB,EAAU;EAC5E;EACA;EACA,OAAOD,QAAQ;AACjB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}