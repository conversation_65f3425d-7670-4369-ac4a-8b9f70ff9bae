{"ast": null, "code": "import { endOfDay } from \"./endOfDay.js\";\n\n/**\n * The {@link endOfToday} function options.\n */\n\n/**\n * @name endOfToday\n * @category Day Helpers\n * @summary Return the end of today.\n * @pure false\n *\n * @description\n * Return the end of today.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n * @typeParam ResultDate - The result `Date` type, it is the type returned from the context function if it is passed, or inferred from the arguments.\n *\n * @param options - The options\n *\n * @returns The end of today\n *\n * @example\n * // If today is 6 October 2014:\n * const result = endOfToday()\n * //=> Mon Oct 6 2014 23:59:59.999\n */\nexport function endOfToday(options) {\n  return endOfDay(Date.now(), options);\n}\n\n// Fallback for modularized imports:\nexport default endOfToday;", "map": {"version": 3, "names": ["endOfDay", "endOfToday", "options", "Date", "now"], "sources": ["C:/Users/<USER>/node_modules/date-fns/endOfToday.js"], "sourcesContent": ["import { endOfDay } from \"./endOfDay.js\";\n\n/**\n * The {@link endOfToday} function options.\n */\n\n/**\n * @name endOfToday\n * @category Day Helpers\n * @summary Return the end of today.\n * @pure false\n *\n * @description\n * Return the end of today.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n * @typeParam ResultDate - The result `Date` type, it is the type returned from the context function if it is passed, or inferred from the arguments.\n *\n * @param options - The options\n *\n * @returns The end of today\n *\n * @example\n * // If today is 6 October 2014:\n * const result = endOfToday()\n * //=> Mon Oct 6 2014 23:59:59.999\n */\nexport function endOfToday(options) {\n  return endOfDay(Date.now(), options);\n}\n\n// Fallback for modularized imports:\nexport default endOfToday;\n"], "mappings": "AAAA,SAASA,QAAQ,QAAQ,eAAe;;AAExC;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,UAAUA,CAACC,OAAO,EAAE;EAClC,OAAOF,QAAQ,CAACG,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEF,OAAO,CAAC;AACtC;;AAEA;AACA,eAAeD,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}