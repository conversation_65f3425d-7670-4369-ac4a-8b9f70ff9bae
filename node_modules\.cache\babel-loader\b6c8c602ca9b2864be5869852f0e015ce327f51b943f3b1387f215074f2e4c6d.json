{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\lostandfound\\\\discfinder-app\\\\src\\\\components\\\\SourceManager.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { supabaseService } from '../lib/supabase';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst SourceManager = ({\n  onClose\n}) => {\n  _s();\n  const [sources, setSources] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [editingSource, setEditingSource] = useState(null);\n  const [newSource, setNewSource] = useState({\n    name: '',\n    description: '',\n    is_active: true,\n    sort_order: 0,\n    legacy_row_id: ''\n  });\n  const [showAddForm, setShowAddForm] = useState(false);\n  useEffect(() => {\n    loadSources();\n  }, []);\n  const loadSources = async () => {\n    try {\n      setLoading(true);\n      const data = await supabaseService.getSources();\n      setSources(data);\n    } catch (err) {\n      setError('Failed to load sources');\n      console.error('Error loading sources:', err);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleCreateSource = async e => {\n    e.preventDefault();\n    try {\n      await supabaseService.createSource(newSource);\n      setNewSource({\n        name: '',\n        description: '',\n        is_active: true,\n        sort_order: 0,\n        legacy_row_id: ''\n      });\n      setShowAddForm(false);\n      await loadSources();\n    } catch (err) {\n      setError('Failed to create source');\n      console.error('Error creating source:', err);\n    }\n  };\n  const handleUpdateSource = async source => {\n    try {\n      await supabaseService.updateSource(source.id, {\n        name: source.name,\n        description: source.description,\n        is_active: source.is_active,\n        sort_order: source.sort_order\n      });\n      setEditingSource(null);\n      await loadSources();\n    } catch (err) {\n      setError('Failed to update source');\n      console.error('Error updating source:', err);\n    }\n  };\n  const handleToggleActive = async source => {\n    try {\n      await supabaseService.updateSource(source.id, {\n        is_active: !source.is_active\n      });\n      await loadSources();\n    } catch (err) {\n      setError('Failed to update source status');\n      console.error('Error updating source status:', err);\n    }\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"modal-overlay\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"modal-content\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center\",\n          children: \"Loading sources...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 84,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 83,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 82,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"modal-overlay\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"modal-content large-modal\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"modal-header\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          children: \"Manage Sources\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 94,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: onClose,\n          className: \"close-button\",\n          children: \"\\xD7\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 95,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 93,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"modal-body\",\n        children: [error && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"status-message error\",\n          children: error\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 105,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-actions\",\n          style: {\n            justifyContent: 'flex-start',\n            marginTop: 0,\n            paddingTop: 0,\n            borderTop: 'none'\n          },\n          children: /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => setShowAddForm(!showAddForm),\n            className: \"button primary\",\n            children: showAddForm ? 'Cancel' : 'Add New Source'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 111,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 110,\n          columnNumber: 11\n        }, this), showAddForm && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-section\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"Add New Source\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 121,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n            onSubmit: handleCreateSource,\n            className: \"disc-form\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-row\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  htmlFor: \"new-name\",\n                  children: \"Name *\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 125,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  id: \"new-name\",\n                  value: newSource.name,\n                  onChange: e => setNewSource({\n                    ...newSource,\n                    name: e.target.value\n                  }),\n                  required: true,\n                  placeholder: \"e.g., Jones Park, Emporia\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 126,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 124,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  htmlFor: \"new-sort\",\n                  children: \"Sort Order\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 136,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"number\",\n                  id: \"new-sort\",\n                  value: newSource.sort_order,\n                  onChange: e => setNewSource({\n                    ...newSource,\n                    sort_order: parseInt(e.target.value) || 0\n                  }),\n                  placeholder: \"0\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 137,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 135,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 123,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                htmlFor: \"new-legacy\",\n                children: \"Legacy Row ID\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 147,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                id: \"new-legacy\",\n                value: newSource.legacy_row_id,\n                onChange: e => setNewSource({\n                  ...newSource,\n                  legacy_row_id: e.target.value\n                }),\n                placeholder: \"Optional - for import mapping\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 148,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 146,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                htmlFor: \"new-description\",\n                children: \"Description\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 157,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                id: \"new-description\",\n                value: newSource.description,\n                onChange: e => setNewSource({\n                  ...newSource,\n                  description: e.target.value\n                }),\n                rows: 2,\n                placeholder: \"Optional description or additional context\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 158,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 156,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: /*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"checkbox-label\",\n                children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"checkbox\",\n                  checked: newSource.is_active,\n                  onChange: e => setNewSource({\n                    ...newSource,\n                    is_active: e.target.checked\n                  })\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 168,\n                  columnNumber: 21\n                }, this), \"Active (visible in dropdown)\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 167,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 166,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-actions\",\n              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                type: \"submit\",\n                className: \"button primary\",\n                children: \"Create Source\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 177,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                type: \"button\",\n                onClick: () => setShowAddForm(false),\n                className: \"button secondary\",\n                children: \"Cancel\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 180,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 176,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 122,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 120,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-section\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"Existing Sources\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 193,\n            columnNumber: 13\n          }, this), sources.length === 0 ? /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"coming-soon\",\n            children: \"No sources found.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 195,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"turnin-grid\",\n            children: sources.map(source => /*#__PURE__*/_jsxDEV(\"div\", {\n              className: `turnin-card ${source.is_active ? '' : 'opacity-75'}`,\n              children: (editingSource === null || editingSource === void 0 ? void 0 : editingSource.id) === source.id ? /*#__PURE__*/_jsxDEV(EditSourceForm, {\n                source: editingSource,\n                onSave: handleUpdateSource,\n                onCancel: () => setEditingSource(null),\n                onChange: setEditingSource\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 204,\n                columnNumber: 23\n              }, this) : /*#__PURE__*/_jsxDEV(_Fragment, {\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"turnin-header\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                    children: source.name\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 213,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: `status-badge ${source.is_active ? 'verified' : 'pending'}`,\n                    children: source.is_active ? 'Active' : 'Inactive'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 214,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 212,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"detail-row\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"label\",\n                    children: \"Sort Order:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 219,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"value\",\n                    children: source.sort_order\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 220,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 218,\n                  columnNumber: 25\n                }, this), source.legacy_row_id && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"detail-row\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"label\",\n                    children: \"Legacy ID:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 224,\n                    columnNumber: 29\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"value\",\n                    style: {\n                      fontSize: '0.75rem',\n                      fontFamily: 'monospace'\n                    },\n                    children: [source.legacy_row_id.substring(0, 12), \"...\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 225,\n                    columnNumber: 29\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 223,\n                  columnNumber: 27\n                }, this), source.description && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"detail-row\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"label\",\n                    children: \"Description:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 232,\n                    columnNumber: 29\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"value\",\n                    children: source.description\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 233,\n                    columnNumber: 29\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 231,\n                  columnNumber: 27\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"turnin-actions\",\n                  style: {\n                    marginTop: '1rem',\n                    display: 'flex',\n                    gap: '0.5rem'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => setEditingSource(source),\n                    className: \"button secondary small\",\n                    children: \"Edit\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 237,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => handleToggleActive(source),\n                    className: `button small ${source.is_active ? 'secondary' : 'primary'}`,\n                    children: source.is_active ? 'Deactivate' : 'Activate'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 243,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 236,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true)\n            }, source.id, false, {\n              fileName: _jsxFileName,\n              lineNumber: 199,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 197,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 192,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 103,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 92,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 91,\n    columnNumber: 5\n  }, this);\n};\n_s(SourceManager, \"80rxbwHEpwnK2GiyHkIQ1dyQPMY=\");\n_c = SourceManager;\nconst EditSourceForm = ({\n  source,\n  onSave,\n  onCancel,\n  onChange\n}) => {\n  const handleSubmit = e => {\n    e.preventDefault();\n    onSave(source);\n  };\n  return /*#__PURE__*/_jsxDEV(\"form\", {\n    onSubmit: handleSubmit,\n    className: \"disc-form\",\n    style: {\n      margin: 0\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"form-row\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"form-group\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          htmlFor: `edit-name-${source.id}`,\n          children: \"Name *\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 280,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"text\",\n          id: `edit-name-${source.id}`,\n          value: source.name,\n          onChange: e => onChange({\n            ...source,\n            name: e.target.value\n          }),\n          required: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 281,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 279,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"form-group\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          htmlFor: `edit-sort-${source.id}`,\n          children: \"Sort Order\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 290,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"number\",\n          id: `edit-sort-${source.id}`,\n          value: source.sort_order,\n          onChange: e => onChange({\n            ...source,\n            sort_order: parseInt(e.target.value) || 0\n          })\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 291,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 289,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 278,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"form-group\",\n      children: [/*#__PURE__*/_jsxDEV(\"label\", {\n        htmlFor: `edit-legacy-${source.id}`,\n        children: \"Legacy Row ID\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 300,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n        type: \"text\",\n        id: `edit-legacy-${source.id}`,\n        value: source.legacy_row_id || '',\n        onChange: e => onChange({\n          ...source,\n          legacy_row_id: e.target.value\n        }),\n        placeholder: \"Optional - for import mapping\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 301,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 299,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"form-group\",\n      children: [/*#__PURE__*/_jsxDEV(\"label\", {\n        htmlFor: `edit-description-${source.id}`,\n        children: \"Description\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 310,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n        id: `edit-description-${source.id}`,\n        value: source.description || '',\n        onChange: e => onChange({\n          ...source,\n          description: e.target.value\n        }),\n        rows: 2\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 311,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 309,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"form-group\",\n      children: /*#__PURE__*/_jsxDEV(\"label\", {\n        className: \"checkbox-label\",\n        children: [/*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"checkbox\",\n          checked: source.is_active,\n          onChange: e => onChange({\n            ...source,\n            is_active: e.target.checked\n          })\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 320,\n          columnNumber: 11\n        }, this), \"Active (visible in dropdown)\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 319,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 318,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"form-actions\",\n      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n        type: \"submit\",\n        className: \"button primary\",\n        children: \"Save Changes\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 329,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        type: \"button\",\n        onClick: onCancel,\n        className: \"button secondary\",\n        children: \"Cancel\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 332,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 328,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 277,\n    columnNumber: 5\n  }, this);\n};\n_c2 = EditSourceForm;\nexport default SourceManager;\nvar _c, _c2;\n$RefreshReg$(_c, \"SourceManager\");\n$RefreshReg$(_c2, \"EditSourceForm\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "supabaseService", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "SourceManager", "onClose", "_s", "sources", "setSources", "loading", "setLoading", "error", "setError", "editingSource", "setEditingSource", "newSource", "setNewSource", "name", "description", "is_active", "sort_order", "legacy_row_id", "showAddForm", "setShowAddForm", "loadSources", "data", "getSources", "err", "console", "handleCreateSource", "e", "preventDefault", "createSource", "handleUpdateSource", "source", "updateSource", "id", "handleToggleActive", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "style", "justifyContent", "marginTop", "paddingTop", "borderTop", "onSubmit", "htmlFor", "type", "value", "onChange", "target", "required", "placeholder", "parseInt", "rows", "checked", "length", "map", "EditSourceForm", "onSave", "onCancel", "fontSize", "fontFamily", "substring", "display", "gap", "_c", "handleSubmit", "margin", "_c2", "$RefreshReg$"], "sources": ["C:/Users/<USER>/lostandfound/discfinder-app/src/components/SourceManager.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { supabaseService, Source } from '../lib/supabase';\n\ninterface SourceManagerProps {\n  onClose: () => void;\n}\n\nconst SourceManager: React.FC<SourceManagerProps> = ({ onClose }) => {\n  const [sources, setSources] = useState<Source[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n  const [editingSource, setEditingSource] = useState<Source | null>(null);\n  const [newSource, setNewSource] = useState({\n    name: '',\n    description: '',\n    is_active: true,\n    sort_order: 0,\n    legacy_row_id: ''\n  });\n  const [showAddForm, setShowAddForm] = useState(false);\n\n  useEffect(() => {\n    loadSources();\n  }, []);\n\n  const loadSources = async () => {\n    try {\n      setLoading(true);\n      const data = await supabaseService.getSources();\n      setSources(data);\n    } catch (err) {\n      setError('Failed to load sources');\n      console.error('Error loading sources:', err);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleCreateSource = async (e: React.FormEvent) => {\n    e.preventDefault();\n    try {\n      await supabaseService.createSource(newSource);\n      setNewSource({ name: '', description: '', is_active: true, sort_order: 0, legacy_row_id: '' });\n      setShowAddForm(false);\n      await loadSources();\n    } catch (err) {\n      setError('Failed to create source');\n      console.error('Error creating source:', err);\n    }\n  };\n\n  const handleUpdateSource = async (source: Source) => {\n    try {\n      await supabaseService.updateSource(source.id, {\n        name: source.name,\n        description: source.description,\n        is_active: source.is_active,\n        sort_order: source.sort_order\n      });\n      setEditingSource(null);\n      await loadSources();\n    } catch (err) {\n      setError('Failed to update source');\n      console.error('Error updating source:', err);\n    }\n  };\n\n  const handleToggleActive = async (source: Source) => {\n    try {\n      await supabaseService.updateSource(source.id, {\n        is_active: !source.is_active\n      });\n      await loadSources();\n    } catch (err) {\n      setError('Failed to update source status');\n      console.error('Error updating source status:', err);\n    }\n  };\n\n  if (loading) {\n    return (\n      <div className=\"modal-overlay\">\n        <div className=\"modal-content\">\n          <div className=\"text-center\">Loading sources...</div>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"modal-overlay\">\n      <div className=\"modal-content large-modal\">\n        <div className=\"modal-header\">\n          <h3>Manage Sources</h3>\n          <button\n            onClick={onClose}\n            className=\"close-button\"\n          >\n            ×\n          </button>\n        </div>\n\n        <div className=\"modal-body\">\n          {error && (\n            <div className=\"status-message error\">\n              {error}\n            </div>\n          )}\n\n          <div className=\"form-actions\" style={{ justifyContent: 'flex-start', marginTop: 0, paddingTop: 0, borderTop: 'none' }}>\n            <button\n              onClick={() => setShowAddForm(!showAddForm)}\n              className=\"button primary\"\n            >\n              {showAddForm ? 'Cancel' : 'Add New Source'}\n            </button>\n          </div>\n\n          {showAddForm && (\n            <div className=\"form-section\">\n              <h3>Add New Source</h3>\n              <form onSubmit={handleCreateSource} className=\"disc-form\">\n                <div className=\"form-row\">\n                  <div className=\"form-group\">\n                    <label htmlFor=\"new-name\">Name *</label>\n                    <input\n                      type=\"text\"\n                      id=\"new-name\"\n                      value={newSource.name}\n                      onChange={(e) => setNewSource({ ...newSource, name: e.target.value })}\n                      required\n                      placeholder=\"e.g., Jones Park, Emporia\"\n                    />\n                  </div>\n                  <div className=\"form-group\">\n                    <label htmlFor=\"new-sort\">Sort Order</label>\n                    <input\n                      type=\"number\"\n                      id=\"new-sort\"\n                      value={newSource.sort_order}\n                      onChange={(e) => setNewSource({ ...newSource, sort_order: parseInt(e.target.value) || 0 })}\n                      placeholder=\"0\"\n                    />\n                  </div>\n                </div>\n                <div className=\"form-group\">\n                  <label htmlFor=\"new-legacy\">Legacy Row ID</label>\n                  <input\n                    type=\"text\"\n                    id=\"new-legacy\"\n                    value={newSource.legacy_row_id}\n                    onChange={(e) => setNewSource({ ...newSource, legacy_row_id: e.target.value })}\n                    placeholder=\"Optional - for import mapping\"\n                  />\n                </div>\n                <div className=\"form-group\">\n                  <label htmlFor=\"new-description\">Description</label>\n                  <textarea\n                    id=\"new-description\"\n                    value={newSource.description}\n                    onChange={(e) => setNewSource({ ...newSource, description: e.target.value })}\n                    rows={2}\n                    placeholder=\"Optional description or additional context\"\n                  />\n                </div>\n                <div className=\"form-group\">\n                  <label className=\"checkbox-label\">\n                    <input\n                      type=\"checkbox\"\n                      checked={newSource.is_active}\n                      onChange={(e) => setNewSource({ ...newSource, is_active: e.target.checked })}\n                    />\n                    Active (visible in dropdown)\n                  </label>\n                </div>\n                <div className=\"form-actions\">\n                  <button type=\"submit\" className=\"button primary\">\n                    Create Source\n                  </button>\n                  <button\n                    type=\"button\"\n                    onClick={() => setShowAddForm(false)}\n                    className=\"button secondary\"\n                  >\n                    Cancel\n                  </button>\n                </div>\n              </form>\n            </div>\n          )}\n\n          <div className=\"form-section\">\n            <h3>Existing Sources</h3>\n            {sources.length === 0 ? (\n              <p className=\"coming-soon\">No sources found.</p>\n            ) : (\n              <div className=\"turnin-grid\">\n                {sources.map((source) => (\n                  <div\n                    key={source.id}\n                    className={`turnin-card ${source.is_active ? '' : 'opacity-75'}`}\n                  >\n                    {editingSource?.id === source.id ? (\n                      <EditSourceForm\n                        source={editingSource}\n                        onSave={handleUpdateSource}\n                        onCancel={() => setEditingSource(null)}\n                        onChange={setEditingSource}\n                      />\n                    ) : (\n                      <>\n                        <div className=\"turnin-header\">\n                          <h4>{source.name}</h4>\n                          <span className={`status-badge ${source.is_active ? 'verified' : 'pending'}`}>\n                            {source.is_active ? 'Active' : 'Inactive'}\n                          </span>\n                        </div>\n                        <div className=\"detail-row\">\n                          <span className=\"label\">Sort Order:</span>\n                          <span className=\"value\">{source.sort_order}</span>\n                        </div>\n                        {source.legacy_row_id && (\n                          <div className=\"detail-row\">\n                            <span className=\"label\">Legacy ID:</span>\n                            <span className=\"value\" style={{ fontSize: '0.75rem', fontFamily: 'monospace' }}>\n                              {source.legacy_row_id.substring(0, 12)}...\n                            </span>\n                          </div>\n                        )}\n                        {source.description && (\n                          <div className=\"detail-row\">\n                            <span className=\"label\">Description:</span>\n                            <span className=\"value\">{source.description}</span>\n                          </div>\n                        )}\n                        <div className=\"turnin-actions\" style={{ marginTop: '1rem', display: 'flex', gap: '0.5rem' }}>\n                          <button\n                            onClick={() => setEditingSource(source)}\n                            className=\"button secondary small\"\n                          >\n                            Edit\n                          </button>\n                          <button\n                            onClick={() => handleToggleActive(source)}\n                            className={`button small ${source.is_active ? 'secondary' : 'primary'}`}\n                          >\n                            {source.is_active ? 'Deactivate' : 'Activate'}\n                          </button>\n                        </div>\n                      </>\n                    )}\n                  </div>\n                ))}\n              </div>\n            )}\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\ninterface EditSourceFormProps {\n  source: Source;\n  onSave: (source: Source) => void;\n  onCancel: () => void;\n  onChange: (source: Source) => void;\n}\n\nconst EditSourceForm: React.FC<EditSourceFormProps> = ({ source, onSave, onCancel, onChange }) => {\n  const handleSubmit = (e: React.FormEvent) => {\n    e.preventDefault();\n    onSave(source);\n  };\n\n  return (\n    <form onSubmit={handleSubmit} className=\"disc-form\" style={{ margin: 0 }}>\n      <div className=\"form-row\">\n        <div className=\"form-group\">\n          <label htmlFor={`edit-name-${source.id}`}>Name *</label>\n          <input\n            type=\"text\"\n            id={`edit-name-${source.id}`}\n            value={source.name}\n            onChange={(e) => onChange({ ...source, name: e.target.value })}\n            required\n          />\n        </div>\n        <div className=\"form-group\">\n          <label htmlFor={`edit-sort-${source.id}`}>Sort Order</label>\n          <input\n            type=\"number\"\n            id={`edit-sort-${source.id}`}\n            value={source.sort_order}\n            onChange={(e) => onChange({ ...source, sort_order: parseInt(e.target.value) || 0 })}\n          />\n        </div>\n      </div>\n      <div className=\"form-group\">\n        <label htmlFor={`edit-legacy-${source.id}`}>Legacy Row ID</label>\n        <input\n          type=\"text\"\n          id={`edit-legacy-${source.id}`}\n          value={source.legacy_row_id || ''}\n          onChange={(e) => onChange({ ...source, legacy_row_id: e.target.value })}\n          placeholder=\"Optional - for import mapping\"\n        />\n      </div>\n      <div className=\"form-group\">\n        <label htmlFor={`edit-description-${source.id}`}>Description</label>\n        <textarea\n          id={`edit-description-${source.id}`}\n          value={source.description || ''}\n          onChange={(e) => onChange({ ...source, description: e.target.value })}\n          rows={2}\n        />\n      </div>\n      <div className=\"form-group\">\n        <label className=\"checkbox-label\">\n          <input\n            type=\"checkbox\"\n            checked={source.is_active}\n            onChange={(e) => onChange({ ...source, is_active: e.target.checked })}\n          />\n          Active (visible in dropdown)\n        </label>\n      </div>\n      <div className=\"form-actions\">\n        <button type=\"submit\" className=\"button primary\">\n          Save Changes\n        </button>\n        <button type=\"button\" onClick={onCancel} className=\"button secondary\">\n          Cancel\n        </button>\n      </div>\n    </form>\n  );\n};\n\nexport default SourceManager;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,eAAe,QAAgB,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAM1D,MAAMC,aAA2C,GAAGA,CAAC;EAAEC;AAAQ,CAAC,KAAK;EAAAC,EAAA;EACnE,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGX,QAAQ,CAAW,EAAE,CAAC;EACpD,MAAM,CAACY,OAAO,EAAEC,UAAU,CAAC,GAAGb,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACc,KAAK,EAAEC,QAAQ,CAAC,GAAGf,QAAQ,CAAgB,IAAI,CAAC;EACvD,MAAM,CAACgB,aAAa,EAAEC,gBAAgB,CAAC,GAAGjB,QAAQ,CAAgB,IAAI,CAAC;EACvE,MAAM,CAACkB,SAAS,EAAEC,YAAY,CAAC,GAAGnB,QAAQ,CAAC;IACzCoB,IAAI,EAAE,EAAE;IACRC,WAAW,EAAE,EAAE;IACfC,SAAS,EAAE,IAAI;IACfC,UAAU,EAAE,CAAC;IACbC,aAAa,EAAE;EACjB,CAAC,CAAC;EACF,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAG1B,QAAQ,CAAC,KAAK,CAAC;EAErDC,SAAS,CAAC,MAAM;IACd0B,WAAW,CAAC,CAAC;EACf,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMA,WAAW,GAAG,MAAAA,CAAA,KAAY;IAC9B,IAAI;MACFd,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMe,IAAI,GAAG,MAAM1B,eAAe,CAAC2B,UAAU,CAAC,CAAC;MAC/ClB,UAAU,CAACiB,IAAI,CAAC;IAClB,CAAC,CAAC,OAAOE,GAAG,EAAE;MACZf,QAAQ,CAAC,wBAAwB,CAAC;MAClCgB,OAAO,CAACjB,KAAK,CAAC,wBAAwB,EAAEgB,GAAG,CAAC;IAC9C,CAAC,SAAS;MACRjB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMmB,kBAAkB,GAAG,MAAOC,CAAkB,IAAK;IACvDA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClB,IAAI;MACF,MAAMhC,eAAe,CAACiC,YAAY,CAACjB,SAAS,CAAC;MAC7CC,YAAY,CAAC;QAAEC,IAAI,EAAE,EAAE;QAAEC,WAAW,EAAE,EAAE;QAAEC,SAAS,EAAE,IAAI;QAAEC,UAAU,EAAE,CAAC;QAAEC,aAAa,EAAE;MAAG,CAAC,CAAC;MAC9FE,cAAc,CAAC,KAAK,CAAC;MACrB,MAAMC,WAAW,CAAC,CAAC;IACrB,CAAC,CAAC,OAAOG,GAAG,EAAE;MACZf,QAAQ,CAAC,yBAAyB,CAAC;MACnCgB,OAAO,CAACjB,KAAK,CAAC,wBAAwB,EAAEgB,GAAG,CAAC;IAC9C;EACF,CAAC;EAED,MAAMM,kBAAkB,GAAG,MAAOC,MAAc,IAAK;IACnD,IAAI;MACF,MAAMnC,eAAe,CAACoC,YAAY,CAACD,MAAM,CAACE,EAAE,EAAE;QAC5CnB,IAAI,EAAEiB,MAAM,CAACjB,IAAI;QACjBC,WAAW,EAAEgB,MAAM,CAAChB,WAAW;QAC/BC,SAAS,EAAEe,MAAM,CAACf,SAAS;QAC3BC,UAAU,EAAEc,MAAM,CAACd;MACrB,CAAC,CAAC;MACFN,gBAAgB,CAAC,IAAI,CAAC;MACtB,MAAMU,WAAW,CAAC,CAAC;IACrB,CAAC,CAAC,OAAOG,GAAG,EAAE;MACZf,QAAQ,CAAC,yBAAyB,CAAC;MACnCgB,OAAO,CAACjB,KAAK,CAAC,wBAAwB,EAAEgB,GAAG,CAAC;IAC9C;EACF,CAAC;EAED,MAAMU,kBAAkB,GAAG,MAAOH,MAAc,IAAK;IACnD,IAAI;MACF,MAAMnC,eAAe,CAACoC,YAAY,CAACD,MAAM,CAACE,EAAE,EAAE;QAC5CjB,SAAS,EAAE,CAACe,MAAM,CAACf;MACrB,CAAC,CAAC;MACF,MAAMK,WAAW,CAAC,CAAC;IACrB,CAAC,CAAC,OAAOG,GAAG,EAAE;MACZf,QAAQ,CAAC,gCAAgC,CAAC;MAC1CgB,OAAO,CAACjB,KAAK,CAAC,+BAA+B,EAAEgB,GAAG,CAAC;IACrD;EACF,CAAC;EAED,IAAIlB,OAAO,EAAE;IACX,oBACER,OAAA;MAAKqC,SAAS,EAAC,eAAe;MAAAC,QAAA,eAC5BtC,OAAA;QAAKqC,SAAS,EAAC,eAAe;QAAAC,QAAA,eAC5BtC,OAAA;UAAKqC,SAAS,EAAC,aAAa;UAAAC,QAAA,EAAC;QAAkB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,oBACE1C,OAAA;IAAKqC,SAAS,EAAC,eAAe;IAAAC,QAAA,eAC5BtC,OAAA;MAAKqC,SAAS,EAAC,2BAA2B;MAAAC,QAAA,gBACxCtC,OAAA;QAAKqC,SAAS,EAAC,cAAc;QAAAC,QAAA,gBAC3BtC,OAAA;UAAAsC,QAAA,EAAI;QAAc;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACvB1C,OAAA;UACE2C,OAAO,EAAEvC,OAAQ;UACjBiC,SAAS,EAAC,cAAc;UAAAC,QAAA,EACzB;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eAEN1C,OAAA;QAAKqC,SAAS,EAAC,YAAY;QAAAC,QAAA,GACxB5B,KAAK,iBACJV,OAAA;UAAKqC,SAAS,EAAC,sBAAsB;UAAAC,QAAA,EAClC5B;QAAK;UAAA6B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN,eAED1C,OAAA;UAAKqC,SAAS,EAAC,cAAc;UAACO,KAAK,EAAE;YAAEC,cAAc,EAAE,YAAY;YAAEC,SAAS,EAAE,CAAC;YAAEC,UAAU,EAAE,CAAC;YAAEC,SAAS,EAAE;UAAO,CAAE;UAAAV,QAAA,eACpHtC,OAAA;YACE2C,OAAO,EAAEA,CAAA,KAAMrB,cAAc,CAAC,CAACD,WAAW,CAAE;YAC5CgB,SAAS,EAAC,gBAAgB;YAAAC,QAAA,EAEzBjB,WAAW,GAAG,QAAQ,GAAG;UAAgB;YAAAkB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,EAELrB,WAAW,iBACVrB,OAAA;UAAKqC,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC3BtC,OAAA;YAAAsC,QAAA,EAAI;UAAc;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACvB1C,OAAA;YAAMiD,QAAQ,EAAErB,kBAAmB;YAACS,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACvDtC,OAAA;cAAKqC,SAAS,EAAC,UAAU;cAAAC,QAAA,gBACvBtC,OAAA;gBAAKqC,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBtC,OAAA;kBAAOkD,OAAO,EAAC,UAAU;kBAAAZ,QAAA,EAAC;gBAAM;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACxC1C,OAAA;kBACEmD,IAAI,EAAC,MAAM;kBACXhB,EAAE,EAAC,UAAU;kBACbiB,KAAK,EAAEtC,SAAS,CAACE,IAAK;kBACtBqC,QAAQ,EAAGxB,CAAC,IAAKd,YAAY,CAAC;oBAAE,GAAGD,SAAS;oBAAEE,IAAI,EAAEa,CAAC,CAACyB,MAAM,CAACF;kBAAM,CAAC,CAAE;kBACtEG,QAAQ;kBACRC,WAAW,EAAC;gBAA2B;kBAAAjB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACN1C,OAAA;gBAAKqC,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBtC,OAAA;kBAAOkD,OAAO,EAAC,UAAU;kBAAAZ,QAAA,EAAC;gBAAU;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC5C1C,OAAA;kBACEmD,IAAI,EAAC,QAAQ;kBACbhB,EAAE,EAAC,UAAU;kBACbiB,KAAK,EAAEtC,SAAS,CAACK,UAAW;kBAC5BkC,QAAQ,EAAGxB,CAAC,IAAKd,YAAY,CAAC;oBAAE,GAAGD,SAAS;oBAAEK,UAAU,EAAEsC,QAAQ,CAAC5B,CAAC,CAACyB,MAAM,CAACF,KAAK,CAAC,IAAI;kBAAE,CAAC,CAAE;kBAC3FI,WAAW,EAAC;gBAAG;kBAAAjB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACN1C,OAAA;cAAKqC,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzBtC,OAAA;gBAAOkD,OAAO,EAAC,YAAY;gBAAAZ,QAAA,EAAC;cAAa;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACjD1C,OAAA;gBACEmD,IAAI,EAAC,MAAM;gBACXhB,EAAE,EAAC,YAAY;gBACfiB,KAAK,EAAEtC,SAAS,CAACM,aAAc;gBAC/BiC,QAAQ,EAAGxB,CAAC,IAAKd,YAAY,CAAC;kBAAE,GAAGD,SAAS;kBAAEM,aAAa,EAAES,CAAC,CAACyB,MAAM,CAACF;gBAAM,CAAC,CAAE;gBAC/EI,WAAW,EAAC;cAA+B;gBAAAjB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5C,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACN1C,OAAA;cAAKqC,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzBtC,OAAA;gBAAOkD,OAAO,EAAC,iBAAiB;gBAAAZ,QAAA,EAAC;cAAW;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACpD1C,OAAA;gBACEmC,EAAE,EAAC,iBAAiB;gBACpBiB,KAAK,EAAEtC,SAAS,CAACG,WAAY;gBAC7BoC,QAAQ,EAAGxB,CAAC,IAAKd,YAAY,CAAC;kBAAE,GAAGD,SAAS;kBAAEG,WAAW,EAAEY,CAAC,CAACyB,MAAM,CAACF;gBAAM,CAAC,CAAE;gBAC7EM,IAAI,EAAE,CAAE;gBACRF,WAAW,EAAC;cAA4C;gBAAAjB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACN1C,OAAA;cAAKqC,SAAS,EAAC,YAAY;cAAAC,QAAA,eACzBtC,OAAA;gBAAOqC,SAAS,EAAC,gBAAgB;gBAAAC,QAAA,gBAC/BtC,OAAA;kBACEmD,IAAI,EAAC,UAAU;kBACfQ,OAAO,EAAE7C,SAAS,CAACI,SAAU;kBAC7BmC,QAAQ,EAAGxB,CAAC,IAAKd,YAAY,CAAC;oBAAE,GAAGD,SAAS;oBAAEI,SAAS,EAAEW,CAAC,CAACyB,MAAM,CAACK;kBAAQ,CAAC;gBAAE;kBAAApB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC9E,CAAC,gCAEJ;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC,eACN1C,OAAA;cAAKqC,SAAS,EAAC,cAAc;cAAAC,QAAA,gBAC3BtC,OAAA;gBAAQmD,IAAI,EAAC,QAAQ;gBAACd,SAAS,EAAC,gBAAgB;gBAAAC,QAAA,EAAC;cAEjD;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACT1C,OAAA;gBACEmD,IAAI,EAAC,QAAQ;gBACbR,OAAO,EAAEA,CAAA,KAAMrB,cAAc,CAAC,KAAK,CAAE;gBACrCe,SAAS,EAAC,kBAAkB;gBAAAC,QAAA,EAC7B;cAED;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CACN,eAED1C,OAAA;UAAKqC,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC3BtC,OAAA;YAAAsC,QAAA,EAAI;UAAgB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,EACxBpC,OAAO,CAACsD,MAAM,KAAK,CAAC,gBACnB5D,OAAA;YAAGqC,SAAS,EAAC,aAAa;YAAAC,QAAA,EAAC;UAAiB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,gBAEhD1C,OAAA;YAAKqC,SAAS,EAAC,aAAa;YAAAC,QAAA,EACzBhC,OAAO,CAACuD,GAAG,CAAE5B,MAAM,iBAClBjC,OAAA;cAEEqC,SAAS,EAAE,eAAeJ,MAAM,CAACf,SAAS,GAAG,EAAE,GAAG,YAAY,EAAG;cAAAoB,QAAA,EAEhE,CAAA1B,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAEuB,EAAE,MAAKF,MAAM,CAACE,EAAE,gBAC9BnC,OAAA,CAAC8D,cAAc;gBACb7B,MAAM,EAAErB,aAAc;gBACtBmD,MAAM,EAAE/B,kBAAmB;gBAC3BgC,QAAQ,EAAEA,CAAA,KAAMnD,gBAAgB,CAAC,IAAI,CAAE;gBACvCwC,QAAQ,EAAExC;cAAiB;gBAAA0B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5B,CAAC,gBAEF1C,OAAA,CAAAE,SAAA;gBAAAoC,QAAA,gBACEtC,OAAA;kBAAKqC,SAAS,EAAC,eAAe;kBAAAC,QAAA,gBAC5BtC,OAAA;oBAAAsC,QAAA,EAAKL,MAAM,CAACjB;kBAAI;oBAAAuB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACtB1C,OAAA;oBAAMqC,SAAS,EAAE,gBAAgBJ,MAAM,CAACf,SAAS,GAAG,UAAU,GAAG,SAAS,EAAG;oBAAAoB,QAAA,EAC1EL,MAAM,CAACf,SAAS,GAAG,QAAQ,GAAG;kBAAU;oBAAAqB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACrC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC,eACN1C,OAAA;kBAAKqC,SAAS,EAAC,YAAY;kBAAAC,QAAA,gBACzBtC,OAAA;oBAAMqC,SAAS,EAAC,OAAO;oBAAAC,QAAA,EAAC;kBAAW;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAC1C1C,OAAA;oBAAMqC,SAAS,EAAC,OAAO;oBAAAC,QAAA,EAAEL,MAAM,CAACd;kBAAU;oBAAAoB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC/C,CAAC,EACLT,MAAM,CAACb,aAAa,iBACnBpB,OAAA;kBAAKqC,SAAS,EAAC,YAAY;kBAAAC,QAAA,gBACzBtC,OAAA;oBAAMqC,SAAS,EAAC,OAAO;oBAAAC,QAAA,EAAC;kBAAU;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACzC1C,OAAA;oBAAMqC,SAAS,EAAC,OAAO;oBAACO,KAAK,EAAE;sBAAEqB,QAAQ,EAAE,SAAS;sBAAEC,UAAU,EAAE;oBAAY,CAAE;oBAAA5B,QAAA,GAC7EL,MAAM,CAACb,aAAa,CAAC+C,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,EAAC,KACzC;kBAAA;oBAAA5B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CACN,EACAT,MAAM,CAAChB,WAAW,iBACjBjB,OAAA;kBAAKqC,SAAS,EAAC,YAAY;kBAAAC,QAAA,gBACzBtC,OAAA;oBAAMqC,SAAS,EAAC,OAAO;oBAAAC,QAAA,EAAC;kBAAY;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAC3C1C,OAAA;oBAAMqC,SAAS,EAAC,OAAO;oBAAAC,QAAA,EAAEL,MAAM,CAAChB;kBAAW;oBAAAsB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChD,CACN,eACD1C,OAAA;kBAAKqC,SAAS,EAAC,gBAAgB;kBAACO,KAAK,EAAE;oBAAEE,SAAS,EAAE,MAAM;oBAAEsB,OAAO,EAAE,MAAM;oBAAEC,GAAG,EAAE;kBAAS,CAAE;kBAAA/B,QAAA,gBAC3FtC,OAAA;oBACE2C,OAAO,EAAEA,CAAA,KAAM9B,gBAAgB,CAACoB,MAAM,CAAE;oBACxCI,SAAS,EAAC,wBAAwB;oBAAAC,QAAA,EACnC;kBAED;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACT1C,OAAA;oBACE2C,OAAO,EAAEA,CAAA,KAAMP,kBAAkB,CAACH,MAAM,CAAE;oBAC1CI,SAAS,EAAE,gBAAgBJ,MAAM,CAACf,SAAS,GAAG,WAAW,GAAG,SAAS,EAAG;oBAAAoB,QAAA,EAEvEL,MAAM,CAACf,SAAS,GAAG,YAAY,GAAG;kBAAU;oBAAAqB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACvC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC;cAAA,eACN;YACH,GAnDIT,MAAM,CAACE,EAAE;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAoDX,CACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACrC,EAAA,CA7PIF,aAA2C;AAAAmE,EAAA,GAA3CnE,aAA2C;AAsQjD,MAAM2D,cAA6C,GAAGA,CAAC;EAAE7B,MAAM;EAAE8B,MAAM;EAAEC,QAAQ;EAAEX;AAAS,CAAC,KAAK;EAChG,MAAMkB,YAAY,GAAI1C,CAAkB,IAAK;IAC3CA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClBiC,MAAM,CAAC9B,MAAM,CAAC;EAChB,CAAC;EAED,oBACEjC,OAAA;IAAMiD,QAAQ,EAAEsB,YAAa;IAAClC,SAAS,EAAC,WAAW;IAACO,KAAK,EAAE;MAAE4B,MAAM,EAAE;IAAE,CAAE;IAAAlC,QAAA,gBACvEtC,OAAA;MAAKqC,SAAS,EAAC,UAAU;MAAAC,QAAA,gBACvBtC,OAAA;QAAKqC,SAAS,EAAC,YAAY;QAAAC,QAAA,gBACzBtC,OAAA;UAAOkD,OAAO,EAAE,aAAajB,MAAM,CAACE,EAAE,EAAG;UAAAG,QAAA,EAAC;QAAM;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACxD1C,OAAA;UACEmD,IAAI,EAAC,MAAM;UACXhB,EAAE,EAAE,aAAaF,MAAM,CAACE,EAAE,EAAG;UAC7BiB,KAAK,EAAEnB,MAAM,CAACjB,IAAK;UACnBqC,QAAQ,EAAGxB,CAAC,IAAKwB,QAAQ,CAAC;YAAE,GAAGpB,MAAM;YAAEjB,IAAI,EAAEa,CAAC,CAACyB,MAAM,CAACF;UAAM,CAAC,CAAE;UAC/DG,QAAQ;QAAA;UAAAhB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eACN1C,OAAA;QAAKqC,SAAS,EAAC,YAAY;QAAAC,QAAA,gBACzBtC,OAAA;UAAOkD,OAAO,EAAE,aAAajB,MAAM,CAACE,EAAE,EAAG;UAAAG,QAAA,EAAC;QAAU;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eAC5D1C,OAAA;UACEmD,IAAI,EAAC,QAAQ;UACbhB,EAAE,EAAE,aAAaF,MAAM,CAACE,EAAE,EAAG;UAC7BiB,KAAK,EAAEnB,MAAM,CAACd,UAAW;UACzBkC,QAAQ,EAAGxB,CAAC,IAAKwB,QAAQ,CAAC;YAAE,GAAGpB,MAAM;YAAEd,UAAU,EAAEsC,QAAQ,CAAC5B,CAAC,CAACyB,MAAM,CAACF,KAAK,CAAC,IAAI;UAAE,CAAC;QAAE;UAAAb,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eACN1C,OAAA;MAAKqC,SAAS,EAAC,YAAY;MAAAC,QAAA,gBACzBtC,OAAA;QAAOkD,OAAO,EAAE,eAAejB,MAAM,CAACE,EAAE,EAAG;QAAAG,QAAA,EAAC;MAAa;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eACjE1C,OAAA;QACEmD,IAAI,EAAC,MAAM;QACXhB,EAAE,EAAE,eAAeF,MAAM,CAACE,EAAE,EAAG;QAC/BiB,KAAK,EAAEnB,MAAM,CAACb,aAAa,IAAI,EAAG;QAClCiC,QAAQ,EAAGxB,CAAC,IAAKwB,QAAQ,CAAC;UAAE,GAAGpB,MAAM;UAAEb,aAAa,EAAES,CAAC,CAACyB,MAAM,CAACF;QAAM,CAAC,CAAE;QACxEI,WAAW,EAAC;MAA+B;QAAAjB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5C,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eACN1C,OAAA;MAAKqC,SAAS,EAAC,YAAY;MAAAC,QAAA,gBACzBtC,OAAA;QAAOkD,OAAO,EAAE,oBAAoBjB,MAAM,CAACE,EAAE,EAAG;QAAAG,QAAA,EAAC;MAAW;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eACpE1C,OAAA;QACEmC,EAAE,EAAE,oBAAoBF,MAAM,CAACE,EAAE,EAAG;QACpCiB,KAAK,EAAEnB,MAAM,CAAChB,WAAW,IAAI,EAAG;QAChCoC,QAAQ,EAAGxB,CAAC,IAAKwB,QAAQ,CAAC;UAAE,GAAGpB,MAAM;UAAEhB,WAAW,EAAEY,CAAC,CAACyB,MAAM,CAACF;QAAM,CAAC,CAAE;QACtEM,IAAI,EAAE;MAAE;QAAAnB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACT,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eACN1C,OAAA;MAAKqC,SAAS,EAAC,YAAY;MAAAC,QAAA,eACzBtC,OAAA;QAAOqC,SAAS,EAAC,gBAAgB;QAAAC,QAAA,gBAC/BtC,OAAA;UACEmD,IAAI,EAAC,UAAU;UACfQ,OAAO,EAAE1B,MAAM,CAACf,SAAU;UAC1BmC,QAAQ,EAAGxB,CAAC,IAAKwB,QAAQ,CAAC;YAAE,GAAGpB,MAAM;YAAEf,SAAS,EAAEW,CAAC,CAACyB,MAAM,CAACK;UAAQ,CAAC;QAAE;UAAApB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvE,CAAC,gCAEJ;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,eACN1C,OAAA;MAAKqC,SAAS,EAAC,cAAc;MAAAC,QAAA,gBAC3BtC,OAAA;QAAQmD,IAAI,EAAC,QAAQ;QAACd,SAAS,EAAC,gBAAgB;QAAAC,QAAA,EAAC;MAEjD;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACT1C,OAAA;QAAQmD,IAAI,EAAC,QAAQ;QAACR,OAAO,EAAEqB,QAAS;QAAC3B,SAAS,EAAC,kBAAkB;QAAAC,QAAA,EAAC;MAEtE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACF,CAAC;AAEX,CAAC;AAAC+B,GAAA,GApEIX,cAA6C;AAsEnD,eAAe3D,aAAa;AAAC,IAAAmE,EAAA,EAAAG,GAAA;AAAAC,YAAA,CAAAJ,EAAA;AAAAI,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}