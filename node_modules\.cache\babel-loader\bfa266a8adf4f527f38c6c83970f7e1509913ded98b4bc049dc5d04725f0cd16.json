{"ast": null, "code": "import { getWeekYear } from \"../../../getWeekYear.js\";\nimport { startOfWeek } from \"../../../startOfWeek.js\";\nimport { Parser } from \"../Parser.js\";\nimport { mapValue, normalizeTwoDigitYear, parseNDigits } from \"../utils.js\";\n\n// Local week-numbering year\nexport class LocalWeekYearParser extends Parser {\n  priority = 130;\n  parse(dateString, token, match) {\n    const valueCallback = year => ({\n      year,\n      isTwoDigitYear: token === \"YY\"\n    });\n    switch (token) {\n      case \"Y\":\n        return mapValue(parseNDigits(4, dateString), valueCallback);\n      case \"Yo\":\n        return mapValue(match.ordinalNumber(dateString, {\n          unit: \"year\"\n        }), valueCallback);\n      default:\n        return mapValue(parseNDigits(token.length, dateString), valueCallback);\n    }\n  }\n  validate(_date, value) {\n    return value.isTwoDigitYear || value.year > 0;\n  }\n  set(date, flags, value, options) {\n    const currentYear = getWeekYear(date, options);\n    if (value.isTwoDigitYear) {\n      const normalizedTwoDigitYear = normalizeTwoDigitYear(value.year, currentYear);\n      date.setFullYear(normalizedTwoDigitYear, 0, options.firstWeekContainsDate);\n      date.setHours(0, 0, 0, 0);\n      return startOfWeek(date, options);\n    }\n    const year = !(\"era\" in flags) || flags.era === 1 ? value.year : 1 - value.year;\n    date.setFullYear(year, 0, options.firstWeekContainsDate);\n    date.setHours(0, 0, 0, 0);\n    return startOfWeek(date, options);\n  }\n  incompatibleTokens = [\"y\", \"R\", \"u\", \"Q\", \"q\", \"M\", \"L\", \"I\", \"d\", \"D\", \"i\", \"t\", \"T\"];\n}", "map": {"version": 3, "names": ["getWeekYear", "startOfWeek", "<PERSON><PERSON><PERSON>", "mapValue", "normalizeTwoDigitYear", "parseNDigits", "LocalWeekYearParser", "priority", "parse", "dateString", "token", "match", "valueCallback", "year", "isTwoDigitYear", "ordinalNumber", "unit", "length", "validate", "_date", "value", "set", "date", "flags", "options", "currentYear", "normalizedTwoDigitYear", "setFullYear", "firstWeekContainsDate", "setHours", "era", "incompatibleTokens"], "sources": ["C:/Users/<USER>/node_modules/date-fns/parse/_lib/parsers/LocalWeekYearParser.js"], "sourcesContent": ["import { getWeekYear } from \"../../../getWeekYear.js\";\n\nimport { startOfWeek } from \"../../../startOfWeek.js\";\nimport { Parser } from \"../Parser.js\";\n\nimport { mapValue, normalizeTwoDigitYear, parseNDigits } from \"../utils.js\";\n\n// Local week-numbering year\nexport class LocalWeekYearParser extends Parser {\n  priority = 130;\n\n  parse(dateString, token, match) {\n    const valueCallback = (year) => ({\n      year,\n      isTwoDigitYear: token === \"YY\",\n    });\n\n    switch (token) {\n      case \"Y\":\n        return mapValue(parseNDigits(4, dateString), valueCallback);\n      case \"Yo\":\n        return mapValue(\n          match.ordinalNumber(dateString, {\n            unit: \"year\",\n          }),\n          valueCallback,\n        );\n      default:\n        return mapValue(parseNDigits(token.length, dateString), valueCallback);\n    }\n  }\n\n  validate(_date, value) {\n    return value.isTwoDigitYear || value.year > 0;\n  }\n\n  set(date, flags, value, options) {\n    const currentYear = getWeekYear(date, options);\n\n    if (value.isTwoDigitYear) {\n      const normalizedTwoDigitYear = normalizeTwoDigitYear(\n        value.year,\n        currentYear,\n      );\n      date.setFullYear(\n        normalizedTwoDigitYear,\n        0,\n        options.firstWeekContainsDate,\n      );\n      date.setHours(0, 0, 0, 0);\n      return startOfWeek(date, options);\n    }\n\n    const year =\n      !(\"era\" in flags) || flags.era === 1 ? value.year : 1 - value.year;\n    date.setFullYear(year, 0, options.firstWeekContainsDate);\n    date.setHours(0, 0, 0, 0);\n    return startOfWeek(date, options);\n  }\n\n  incompatibleTokens = [\n    \"y\",\n    \"R\",\n    \"u\",\n    \"Q\",\n    \"q\",\n    \"M\",\n    \"L\",\n    \"I\",\n    \"d\",\n    \"D\",\n    \"i\",\n    \"t\",\n    \"T\",\n  ];\n}\n"], "mappings": "AAAA,SAASA,WAAW,QAAQ,yBAAyB;AAErD,SAASC,WAAW,QAAQ,yBAAyB;AACrD,SAASC,MAAM,QAAQ,cAAc;AAErC,SAASC,QAAQ,EAAEC,qBAAqB,EAAEC,YAAY,QAAQ,aAAa;;AAE3E;AACA,OAAO,MAAMC,mBAAmB,SAASJ,MAAM,CAAC;EAC9CK,QAAQ,GAAG,GAAG;EAEdC,KAAKA,CAACC,UAAU,EAAEC,KAAK,EAAEC,KAAK,EAAE;IAC9B,MAAMC,aAAa,GAAIC,IAAI,KAAM;MAC/BA,IAAI;MACJC,cAAc,EAAEJ,KAAK,KAAK;IAC5B,CAAC,CAAC;IAEF,QAAQA,KAAK;MACX,KAAK,GAAG;QACN,OAAOP,QAAQ,CAACE,YAAY,CAAC,CAAC,EAAEI,UAAU,CAAC,EAAEG,aAAa,CAAC;MAC7D,KAAK,IAAI;QACP,OAAOT,QAAQ,CACbQ,KAAK,CAACI,aAAa,CAACN,UAAU,EAAE;UAC9BO,IAAI,EAAE;QACR,CAAC,CAAC,EACFJ,aACF,CAAC;MACH;QACE,OAAOT,QAAQ,CAACE,YAAY,CAACK,KAAK,CAACO,MAAM,EAAER,UAAU,CAAC,EAAEG,aAAa,CAAC;IAC1E;EACF;EAEAM,QAAQA,CAACC,KAAK,EAAEC,KAAK,EAAE;IACrB,OAAOA,KAAK,CAACN,cAAc,IAAIM,KAAK,CAACP,IAAI,GAAG,CAAC;EAC/C;EAEAQ,GAAGA,CAACC,IAAI,EAAEC,KAAK,EAAEH,KAAK,EAAEI,OAAO,EAAE;IAC/B,MAAMC,WAAW,GAAGzB,WAAW,CAACsB,IAAI,EAAEE,OAAO,CAAC;IAE9C,IAAIJ,KAAK,CAACN,cAAc,EAAE;MACxB,MAAMY,sBAAsB,GAAGtB,qBAAqB,CAClDgB,KAAK,CAACP,IAAI,EACVY,WACF,CAAC;MACDH,IAAI,CAACK,WAAW,CACdD,sBAAsB,EACtB,CAAC,EACDF,OAAO,CAACI,qBACV,CAAC;MACDN,IAAI,CAACO,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;MACzB,OAAO5B,WAAW,CAACqB,IAAI,EAAEE,OAAO,CAAC;IACnC;IAEA,MAAMX,IAAI,GACR,EAAE,KAAK,IAAIU,KAAK,CAAC,IAAIA,KAAK,CAACO,GAAG,KAAK,CAAC,GAAGV,KAAK,CAACP,IAAI,GAAG,CAAC,GAAGO,KAAK,CAACP,IAAI;IACpES,IAAI,CAACK,WAAW,CAACd,IAAI,EAAE,CAAC,EAAEW,OAAO,CAACI,qBAAqB,CAAC;IACxDN,IAAI,CAACO,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IACzB,OAAO5B,WAAW,CAACqB,IAAI,EAAEE,OAAO,CAAC;EACnC;EAEAO,kBAAkB,GAAG,CACnB,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,CACJ;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}