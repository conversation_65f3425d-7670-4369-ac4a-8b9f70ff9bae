{"ast": null, "code": "import { eachWeekendOfInterval } from \"./eachWeekendOfInterval.js\";\nimport { endOfYear } from \"./endOfYear.js\";\nimport { startOfYear } from \"./startOfYear.js\";\n\n/**\n * The {@link eachWeekendOfYear} function options.\n */\n\n/**\n * @name eachWeekendOfYear\n * @category Year Helpers\n * @summary List all the Saturdays and Sundays in the year.\n *\n * @description\n * Get all the Saturdays and Sundays in the year.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n * @typeParam ResultDate - The result `Date` type, it is the type returned from the context function if it is passed, or inferred from the arguments.\n *\n * @param date - The given year\n * @param options - An object with options\n *\n * @returns An array containing all the Saturdays and Sundays\n *\n * @example\n * // Lists all Saturdays and Sundays in the year\n * const result = eachWeekendOfYear(new Date(2020, 1, 1))\n * //=> [\n * //   Sat Jan 03 2020 00:00:00,\n * //   Sun Jan 04 2020 00:00:00,\n * //   ...\n * //   Sun Dec 27 2020 00:00:00\n * // ]\n * ]\n */\nexport function eachWeekendOfYear(date, options) {\n  const start = startOfYear(date, options);\n  const end = endOfYear(date, options);\n  return eachWeekendOfInterval({\n    start,\n    end\n  }, options);\n}\n\n// Fallback for modularized imports:\nexport default eachWeekendOfYear;", "map": {"version": 3, "names": ["eachWeekendOfInterval", "endOfYear", "startOfYear", "eachWeekendOfYear", "date", "options", "start", "end"], "sources": ["C:/Users/<USER>/node_modules/date-fns/eachWeekendOfYear.js"], "sourcesContent": ["import { eachWeekendOfInterval } from \"./eachWeekendOfInterval.js\";\nimport { endOfYear } from \"./endOfYear.js\";\nimport { startOfYear } from \"./startOfYear.js\";\n\n/**\n * The {@link eachWeekendOfYear} function options.\n */\n\n/**\n * @name eachWeekendOfYear\n * @category Year Helpers\n * @summary List all the Saturdays and Sundays in the year.\n *\n * @description\n * Get all the Saturdays and Sundays in the year.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n * @typeParam ResultDate - The result `Date` type, it is the type returned from the context function if it is passed, or inferred from the arguments.\n *\n * @param date - The given year\n * @param options - An object with options\n *\n * @returns An array containing all the Saturdays and Sundays\n *\n * @example\n * // Lists all Saturdays and Sundays in the year\n * const result = eachWeekendOfYear(new Date(2020, 1, 1))\n * //=> [\n * //   Sat Jan 03 2020 00:00:00,\n * //   Sun Jan 04 2020 00:00:00,\n * //   ...\n * //   Sun Dec 27 2020 00:00:00\n * // ]\n * ]\n */\nexport function eachWeekendOfYear(date, options) {\n  const start = startOfYear(date, options);\n  const end = endOfYear(date, options);\n  return eachWeekendOfInterval({ start, end }, options);\n}\n\n// Fallback for modularized imports:\nexport default eachWeekendOfYear;\n"], "mappings": "AAAA,SAASA,qBAAqB,QAAQ,4BAA4B;AAClE,SAASC,SAAS,QAAQ,gBAAgB;AAC1C,SAASC,WAAW,QAAQ,kBAAkB;;AAE9C;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,iBAAiBA,CAACC,IAAI,EAAEC,OAAO,EAAE;EAC/C,MAAMC,KAAK,GAAGJ,WAAW,CAACE,IAAI,EAAEC,OAAO,CAAC;EACxC,MAAME,GAAG,GAAGN,SAAS,CAACG,IAAI,EAAEC,OAAO,CAAC;EACpC,OAAOL,qBAAqB,CAAC;IAAEM,KAAK;IAAEC;EAAI,CAAC,EAAEF,OAAO,CAAC;AACvD;;AAEA;AACA,eAAeF,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}