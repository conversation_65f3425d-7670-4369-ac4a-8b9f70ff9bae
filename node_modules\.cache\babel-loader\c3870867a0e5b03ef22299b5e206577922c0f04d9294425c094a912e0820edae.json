{"ast": null, "code": "/**\n * Reusable Photo Migration System\n * \n * This module provides functionality to download photos from external URLs\n * and upload them to Supabase Storage, with support for different folders\n * and table types (profiles, discs, etc.)\n */\n\nimport { supabase } from './supabase';\n/**\n * Downloads an image from a URL and returns the blob\n */\nasync function downloadImage(url, maxSize = 5 * 1024 * 1024) {\n  const response = await fetch(url);\n  if (!response.ok) {\n    throw new Error(`HTTP ${response.status}: ${response.statusText}`);\n  }\n  const contentType = response.headers.get('content-type') || 'image/jpeg';\n  const contentLength = response.headers.get('content-length');\n  if (contentLength && parseInt(contentLength) > maxSize) {\n    throw new Error(`File too large: ${contentLength} bytes (max: ${maxSize})`);\n  }\n  const blob = await response.blob();\n  if (blob.size > maxSize) {\n    throw new Error(`File too large: ${blob.size} bytes (max: ${maxSize})`);\n  }\n  return {\n    blob,\n    contentType\n  };\n}\n\n/**\n * Generates a unique filename for the uploaded image\n */\nfunction generateFileName(recordId, originalUrl, contentType) {\n  var _originalUrl$split$po;\n  const timestamp = Date.now();\n  const extension = contentType.split('/')[1] || 'jpg';\n  const urlHash = ((_originalUrl$split$po = originalUrl.split('/').pop()) === null || _originalUrl$split$po === void 0 ? void 0 : _originalUrl$split$po.substring(0, 8)) || 'unknown';\n  return `${recordId}-${timestamp}-${urlHash}.${extension}`;\n}\n\n/**\n * Uploads a blob to Supabase Storage\n */\nasync function uploadToStorage(blob, fileName, folder, bucket = 'disc-images') {\n  const filePath = `${folder}/${fileName}`;\n  const {\n    error: uploadError\n  } = await supabase.storage.from(bucket).upload(filePath, blob, {\n    contentType: blob.type,\n    upsert: false\n  });\n  if (uploadError) {\n    throw new Error(`Upload failed: ${uploadError.message}`);\n  }\n  const {\n    data: {\n      publicUrl\n    }\n  } = supabase.storage.from(bucket).getPublicUrl(filePath);\n  return publicUrl;\n}\n\n/**\n * Updates the database record with the new photo URL\n */\nasync function updateRecord(tableName, idColumn, urlColumn, recordId, newUrl) {\n  const {\n    error\n  } = await supabase.from(tableName).update({\n    [urlColumn]: newUrl\n  }).eq(idColumn, recordId);\n  if (error) {\n    throw new Error(`Database update failed: ${error.message}`);\n  }\n}\n\n/**\n * Migrates a single photo from external URL to Supabase Storage\n */\nasync function migrateSinglePhoto(record, config) {\n  const recordId = record[config.idColumn];\n  const originalUrl = record[config.urlColumn];\n\n  // Skip if no URL or already a Supabase URL\n  if (!originalUrl || originalUrl.includes('supabase')) {\n    return {\n      status: 'skipped'\n    };\n  }\n  try {\n    // Download the image\n    const {\n      blob,\n      contentType\n    } = await downloadImage(originalUrl, config.maxFileSize || 5 * 1024 * 1024);\n\n    // Check if content type is allowed\n    const allowedTypes = config.allowedTypes || ['image/jpeg', 'image/png', 'image/webp', 'image/gif'];\n    if (!allowedTypes.includes(contentType)) {\n      return {\n        status: 'skipped',\n        error: `Unsupported content type: ${contentType}`\n      };\n    }\n\n    // Generate filename and upload\n    const fileName = generateFileName(recordId, originalUrl, contentType);\n    const newUrl = await uploadToStorage(blob, fileName, config.storageFolder, config.bucketName);\n\n    // Update database record\n    await updateRecord(config.tableName, config.idColumn, config.urlColumn, recordId, newUrl);\n    return {\n      status: 'migrated',\n      newUrl\n    };\n  } catch (error) {\n    return {\n      status: 'failed',\n      error: error instanceof Error ? error.message : 'Unknown error'\n    };\n  }\n}\n\n/**\n * Migrates photos for all records in a table\n */\nexport async function migratePhotos(config) {\n  console.log(`🚀 Starting photo migration for ${config.tableName}...`);\n  const result = {\n    success: true,\n    processed: 0,\n    migrated: 0,\n    failed: 0,\n    skipped: 0,\n    errors: [],\n    details: []\n  };\n  try {\n    // Fetch all records with photos\n    const {\n      data: records,\n      error\n    } = await supabase.from(config.tableName).select(`${config.idColumn}, ${config.urlColumn}`).not(config.urlColumn, 'is', null).not(config.urlColumn, 'eq', '');\n    if (error) {\n      throw new Error(`Failed to fetch records: ${error.message}`);\n    }\n    if (!records || records.length === 0) {\n      console.log('📭 No records with photos found');\n      return result;\n    }\n    console.log(`📊 Found ${records.length} records with photos to process`);\n\n    // Process each record\n    for (const record of records) {\n      const recordData = record;\n      const recordId = recordData[config.idColumn];\n      const originalUrl = recordData[config.urlColumn];\n      console.log(`📸 Processing ${recordId}: ${originalUrl}`);\n      const migrationResult = await migrateSinglePhoto(recordData, config);\n      result.processed++;\n      result.details.push({\n        recordId,\n        originalUrl,\n        newUrl: migrationResult.newUrl,\n        status: migrationResult.status,\n        error: migrationResult.error\n      });\n      switch (migrationResult.status) {\n        case 'migrated':\n          result.migrated++;\n          console.log(`✅ Migrated ${recordId}: ${migrationResult.newUrl}`);\n          break;\n        case 'failed':\n          result.failed++;\n          result.errors.push(`${recordId}: ${migrationResult.error}`);\n          console.error(`❌ Failed ${recordId}: ${migrationResult.error}`);\n          break;\n        case 'skipped':\n          result.skipped++;\n          console.log(`⚠️  Skipped ${recordId}: ${migrationResult.error || 'No URL or already migrated'}`);\n          break;\n      }\n\n      // Add a small delay to avoid overwhelming the servers\n      await new Promise(resolve => setTimeout(resolve, 100));\n    }\n    if (result.failed > 0) {\n      result.success = false;\n    }\n    console.log('\\n📈 Migration Summary:');\n    console.log(`✅ Migrated: ${result.migrated}`);\n    console.log(`⚠️  Skipped: ${result.skipped}`);\n    console.log(`❌ Failed: ${result.failed}`);\n    return result;\n  } catch (error) {\n    result.success = false;\n    result.errors.push(error instanceof Error ? error.message : 'Unknown error');\n    console.error('💥 Migration failed:', error);\n    return result;\n  }\n}\n\n/**\n * Predefined configurations for common use cases\n */\nexport const MIGRATION_CONFIGS = {\n  PROFILE_AVATARS: {\n    tableName: 'imported_profiles_staging',\n    urlColumn: 'avatar_url',\n    idColumn: 'id',\n    storageFolder: 'avatars',\n    bucketName: 'disc-images',\n    maxFileSize: 5 * 1024 * 1024,\n    // 5MB\n    allowedTypes: ['image/jpeg', 'image/png', 'image/webp', 'image/gif']\n  },\n  DISC_PHOTOS: {\n    tableName: 'found_discs',\n    // Will be updated when disc import is implemented\n    urlColumn: 'photo_url',\n    idColumn: 'legacy_row_id',\n    storageFolder: 'disc-photos',\n    bucketName: 'disc-images',\n    maxFileSize: 10 * 1024 * 1024,\n    // 10MB for disc photos\n    allowedTypes: ['image/jpeg', 'image/png', 'image/webp']\n  }\n};\n\n/**\n * Convenience function to migrate profile avatars\n */\nexport async function migrateProfileAvatars() {\n  return migratePhotos(MIGRATION_CONFIGS.PROFILE_AVATARS);\n}\n\n/**\n * Convenience function to migrate disc photos\n */\nexport async function migrateDiscPhotos(config) {\n  const finalConfig = {\n    ...MIGRATION_CONFIGS.DISC_PHOTOS,\n    ...config\n  };\n  return migratePhotos(finalConfig);\n}", "map": {"version": 3, "names": ["supabase", "downloadImage", "url", "maxSize", "response", "fetch", "ok", "Error", "status", "statusText", "contentType", "headers", "get", "contentLength", "parseInt", "blob", "size", "generateFileName", "recordId", "originalUrl", "_originalUrl$split$po", "timestamp", "Date", "now", "extension", "split", "urlHash", "pop", "substring", "uploadToStorage", "fileName", "folder", "bucket", "filePath", "error", "uploadError", "storage", "from", "upload", "type", "upsert", "message", "data", "publicUrl", "getPublicUrl", "updateRecord", "tableName", "idColumn", "urlColumn", "newUrl", "update", "eq", "migrateSinglePhoto", "record", "config", "includes", "maxFileSize", "allowedTypes", "storageFolder", "bucketName", "migratePhotos", "console", "log", "result", "success", "processed", "migrated", "failed", "skipped", "errors", "details", "records", "select", "not", "length", "recordData", "migrationResult", "push", "Promise", "resolve", "setTimeout", "MIGRATION_CONFIGS", "PROFILE_AVATARS", "DISC_PHOTOS", "migrateProfileAvatars", "migrateDiscPhotos", "finalConfig"], "sources": ["C:/Users/<USER>/lostandfound/src/lib/photoMigration.ts"], "sourcesContent": ["/**\n * Reusable Photo Migration System\n * \n * This module provides functionality to download photos from external URLs\n * and upload them to Supabase Storage, with support for different folders\n * and table types (profiles, discs, etc.)\n */\n\nimport { supabase } from './supabase';\n\nexport interface PhotoMigrationConfig {\n  tableName: string;           // e.g., 'profiles', 'found_discs'\n  urlColumn: string;           // e.g., 'avatar_url', 'photo_url'\n  idColumn: string;            // e.g., 'id', 'legacy_row_id'\n  storageFolder: string;       // e.g., 'avatars', 'disc-photos'\n  bucketName?: string;         // defaults to 'disc-images'\n  maxFileSize?: number;        // defaults to 5MB\n  allowedTypes?: string[];     // defaults to ['image/jpeg', 'image/png', 'image/webp']\n}\n\nexport interface PhotoMigrationResult {\n  success: boolean;\n  processed: number;\n  migrated: number;\n  failed: number;\n  skipped: number;\n  errors: string[];\n  details: {\n    recordId: string;\n    originalUrl: string;\n    newUrl?: string;\n    status: 'migrated' | 'failed' | 'skipped';\n    error?: string;\n  }[];\n}\n\n/**\n * Downloads an image from a URL and returns the blob\n */\nasync function downloadImage(url: string, maxSize: number = 5 * 1024 * 1024): Promise<{ blob: Blob; contentType: string }> {\n  const response = await fetch(url);\n  \n  if (!response.ok) {\n    throw new Error(`HTTP ${response.status}: ${response.statusText}`);\n  }\n  \n  const contentType = response.headers.get('content-type') || 'image/jpeg';\n  const contentLength = response.headers.get('content-length');\n  \n  if (contentLength && parseInt(contentLength) > maxSize) {\n    throw new Error(`File too large: ${contentLength} bytes (max: ${maxSize})`);\n  }\n  \n  const blob = await response.blob();\n  \n  if (blob.size > maxSize) {\n    throw new Error(`File too large: ${blob.size} bytes (max: ${maxSize})`);\n  }\n  \n  return { blob, contentType };\n}\n\n/**\n * Generates a unique filename for the uploaded image\n */\nfunction generateFileName(recordId: string, originalUrl: string, contentType: string): string {\n  const timestamp = Date.now();\n  const extension = contentType.split('/')[1] || 'jpg';\n  const urlHash = originalUrl.split('/').pop()?.substring(0, 8) || 'unknown';\n  return `${recordId}-${timestamp}-${urlHash}.${extension}`;\n}\n\n/**\n * Uploads a blob to Supabase Storage\n */\nasync function uploadToStorage(\n  blob: Blob,\n  fileName: string,\n  folder: string,\n  bucket: string = 'disc-images'\n): Promise<string> {\n  const filePath = `${folder}/${fileName}`;\n  \n  const { error: uploadError } = await supabase.storage\n    .from(bucket)\n    .upload(filePath, blob, {\n      contentType: blob.type,\n      upsert: false\n    });\n  \n  if (uploadError) {\n    throw new Error(`Upload failed: ${uploadError.message}`);\n  }\n  \n  const { data: { publicUrl } } = supabase.storage\n    .from(bucket)\n    .getPublicUrl(filePath);\n  \n  return publicUrl;\n}\n\n/**\n * Updates the database record with the new photo URL\n */\nasync function updateRecord(\n  tableName: string,\n  idColumn: string,\n  urlColumn: string,\n  recordId: string,\n  newUrl: string\n): Promise<void> {\n  const { error } = await supabase\n    .from(tableName)\n    .update({ [urlColumn]: newUrl })\n    .eq(idColumn, recordId);\n  \n  if (error) {\n    throw new Error(`Database update failed: ${error.message}`);\n  }\n}\n\n/**\n * Migrates a single photo from external URL to Supabase Storage\n */\nasync function migrateSinglePhoto(\n  record: Record<string, any>,\n  config: PhotoMigrationConfig\n): Promise<{ status: 'migrated' | 'failed' | 'skipped'; newUrl?: string; error?: string }> {\n  const recordId = record[config.idColumn] as string;\n  const originalUrl = record[config.urlColumn] as string;\n  \n  // Skip if no URL or already a Supabase URL\n  if (!originalUrl || originalUrl.includes('supabase')) {\n    return { status: 'skipped' };\n  }\n  \n  try {\n    // Download the image\n    const { blob, contentType } = await downloadImage(\n      originalUrl,\n      config.maxFileSize || 5 * 1024 * 1024\n    );\n    \n    // Check if content type is allowed\n    const allowedTypes = config.allowedTypes || ['image/jpeg', 'image/png', 'image/webp', 'image/gif'];\n    if (!allowedTypes.includes(contentType)) {\n      return { \n        status: 'skipped',\n        error: `Unsupported content type: ${contentType}`\n      };\n    }\n    \n    // Generate filename and upload\n    const fileName = generateFileName(recordId, originalUrl, contentType);\n    const newUrl = await uploadToStorage(\n      blob,\n      fileName,\n      config.storageFolder,\n      config.bucketName\n    );\n    \n    // Update database record\n    await updateRecord(\n      config.tableName,\n      config.idColumn,\n      config.urlColumn,\n      recordId,\n      newUrl\n    );\n    \n    return { status: 'migrated', newUrl };\n    \n  } catch (error) {\n    return {\n      status: 'failed',\n      error: error instanceof Error ? error.message : 'Unknown error'\n    };\n  }\n}\n\n/**\n * Migrates photos for all records in a table\n */\nexport async function migratePhotos(config: PhotoMigrationConfig): Promise<PhotoMigrationResult> {\n  console.log(`🚀 Starting photo migration for ${config.tableName}...`);\n  \n  const result: PhotoMigrationResult = {\n    success: true,\n    processed: 0,\n    migrated: 0,\n    failed: 0,\n    skipped: 0,\n    errors: [],\n    details: []\n  };\n  \n  try {\n    // Fetch all records with photos\n    const { data: records, error } = await supabase\n      .from(config.tableName)\n      .select(`${config.idColumn}, ${config.urlColumn}`)\n      .not(config.urlColumn, 'is', null)\n      .not(config.urlColumn, 'eq', '');\n\n    if (error) {\n      throw new Error(`Failed to fetch records: ${error.message}`);\n    }\n\n    if (!records || records.length === 0) {\n      console.log('📭 No records with photos found');\n      return result;\n    }\n\n    console.log(`📊 Found ${records.length} records with photos to process`);\n\n    // Process each record\n    for (const record of records) {\n      const recordData = record as Record<string, any>;\n      const recordId = recordData[config.idColumn] as string;\n      const originalUrl = recordData[config.urlColumn] as string;\n      \n      console.log(`📸 Processing ${recordId}: ${originalUrl}`);\n\n      const migrationResult = await migrateSinglePhoto(recordData, config);\n      \n      result.processed++;\n      result.details.push({\n        recordId,\n        originalUrl,\n        newUrl: migrationResult.newUrl,\n        status: migrationResult.status,\n        error: migrationResult.error\n      });\n      \n      switch (migrationResult.status) {\n        case 'migrated':\n          result.migrated++;\n          console.log(`✅ Migrated ${recordId}: ${migrationResult.newUrl}`);\n          break;\n        case 'failed':\n          result.failed++;\n          result.errors.push(`${recordId}: ${migrationResult.error}`);\n          console.error(`❌ Failed ${recordId}: ${migrationResult.error}`);\n          break;\n        case 'skipped':\n          result.skipped++;\n          console.log(`⚠️  Skipped ${recordId}: ${migrationResult.error || 'No URL or already migrated'}`);\n          break;\n      }\n      \n      // Add a small delay to avoid overwhelming the servers\n      await new Promise(resolve => setTimeout(resolve, 100));\n    }\n    \n    if (result.failed > 0) {\n      result.success = false;\n    }\n    \n    console.log('\\n📈 Migration Summary:');\n    console.log(`✅ Migrated: ${result.migrated}`);\n    console.log(`⚠️  Skipped: ${result.skipped}`);\n    console.log(`❌ Failed: ${result.failed}`);\n    \n    return result;\n    \n  } catch (error) {\n    result.success = false;\n    result.errors.push(error instanceof Error ? error.message : 'Unknown error');\n    console.error('💥 Migration failed:', error);\n    return result;\n  }\n}\n\n/**\n * Predefined configurations for common use cases\n */\nexport const MIGRATION_CONFIGS = {\n  PROFILE_AVATARS: {\n    tableName: 'imported_profiles_staging',\n    urlColumn: 'avatar_url',\n    idColumn: 'id',\n    storageFolder: 'avatars',\n    bucketName: 'disc-images',\n    maxFileSize: 5 * 1024 * 1024, // 5MB\n    allowedTypes: ['image/jpeg', 'image/png', 'image/webp', 'image/gif']\n  } as PhotoMigrationConfig,\n  \n  DISC_PHOTOS: {\n    tableName: 'found_discs', // Will be updated when disc import is implemented\n    urlColumn: 'photo_url',\n    idColumn: 'legacy_row_id',\n    storageFolder: 'disc-photos',\n    bucketName: 'disc-images',\n    maxFileSize: 10 * 1024 * 1024, // 10MB for disc photos\n    allowedTypes: ['image/jpeg', 'image/png', 'image/webp']\n  } as PhotoMigrationConfig\n};\n\n/**\n * Convenience function to migrate profile avatars\n */\nexport async function migrateProfileAvatars(): Promise<PhotoMigrationResult> {\n  return migratePhotos(MIGRATION_CONFIGS.PROFILE_AVATARS);\n}\n\n/**\n * Convenience function to migrate disc photos\n */\nexport async function migrateDiscPhotos(config?: Partial<PhotoMigrationConfig>): Promise<PhotoMigrationResult> {\n  const finalConfig = { ...MIGRATION_CONFIGS.DISC_PHOTOS, ...config };\n  return migratePhotos(finalConfig);\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAASA,QAAQ,QAAQ,YAAY;AA4BrC;AACA;AACA;AACA,eAAeC,aAAaA,CAACC,GAAW,EAAEC,OAAe,GAAG,CAAC,GAAG,IAAI,GAAG,IAAI,EAAgD;EACzH,MAAMC,QAAQ,GAAG,MAAMC,KAAK,CAACH,GAAG,CAAC;EAEjC,IAAI,CAACE,QAAQ,CAACE,EAAE,EAAE;IAChB,MAAM,IAAIC,KAAK,CAAC,QAAQH,QAAQ,CAACI,MAAM,KAAKJ,QAAQ,CAACK,UAAU,EAAE,CAAC;EACpE;EAEA,MAAMC,WAAW,GAAGN,QAAQ,CAACO,OAAO,CAACC,GAAG,CAAC,cAAc,CAAC,IAAI,YAAY;EACxE,MAAMC,aAAa,GAAGT,QAAQ,CAACO,OAAO,CAACC,GAAG,CAAC,gBAAgB,CAAC;EAE5D,IAAIC,aAAa,IAAIC,QAAQ,CAACD,aAAa,CAAC,GAAGV,OAAO,EAAE;IACtD,MAAM,IAAII,KAAK,CAAC,mBAAmBM,aAAa,gBAAgBV,OAAO,GAAG,CAAC;EAC7E;EAEA,MAAMY,IAAI,GAAG,MAAMX,QAAQ,CAACW,IAAI,CAAC,CAAC;EAElC,IAAIA,IAAI,CAACC,IAAI,GAAGb,OAAO,EAAE;IACvB,MAAM,IAAII,KAAK,CAAC,mBAAmBQ,IAAI,CAACC,IAAI,gBAAgBb,OAAO,GAAG,CAAC;EACzE;EAEA,OAAO;IAAEY,IAAI;IAAEL;EAAY,CAAC;AAC9B;;AAEA;AACA;AACA;AACA,SAASO,gBAAgBA,CAACC,QAAgB,EAAEC,WAAmB,EAAET,WAAmB,EAAU;EAAA,IAAAU,qBAAA;EAC5F,MAAMC,SAAS,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC;EAC5B,MAAMC,SAAS,GAAGd,WAAW,CAACe,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,KAAK;EACpD,MAAMC,OAAO,GAAG,EAAAN,qBAAA,GAAAD,WAAW,CAACM,KAAK,CAAC,GAAG,CAAC,CAACE,GAAG,CAAC,CAAC,cAAAP,qBAAA,uBAA5BA,qBAAA,CAA8BQ,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,KAAI,SAAS;EAC1E,OAAO,GAAGV,QAAQ,IAAIG,SAAS,IAAIK,OAAO,IAAIF,SAAS,EAAE;AAC3D;;AAEA;AACA;AACA;AACA,eAAeK,eAAeA,CAC5Bd,IAAU,EACVe,QAAgB,EAChBC,MAAc,EACdC,MAAc,GAAG,aAAa,EACb;EACjB,MAAMC,QAAQ,GAAG,GAAGF,MAAM,IAAID,QAAQ,EAAE;EAExC,MAAM;IAAEI,KAAK,EAAEC;EAAY,CAAC,GAAG,MAAMnC,QAAQ,CAACoC,OAAO,CAClDC,IAAI,CAACL,MAAM,CAAC,CACZM,MAAM,CAACL,QAAQ,EAAElB,IAAI,EAAE;IACtBL,WAAW,EAAEK,IAAI,CAACwB,IAAI;IACtBC,MAAM,EAAE;EACV,CAAC,CAAC;EAEJ,IAAIL,WAAW,EAAE;IACf,MAAM,IAAI5B,KAAK,CAAC,kBAAkB4B,WAAW,CAACM,OAAO,EAAE,CAAC;EAC1D;EAEA,MAAM;IAAEC,IAAI,EAAE;MAAEC;IAAU;EAAE,CAAC,GAAG3C,QAAQ,CAACoC,OAAO,CAC7CC,IAAI,CAACL,MAAM,CAAC,CACZY,YAAY,CAACX,QAAQ,CAAC;EAEzB,OAAOU,SAAS;AAClB;;AAEA;AACA;AACA;AACA,eAAeE,YAAYA,CACzBC,SAAiB,EACjBC,QAAgB,EAChBC,SAAiB,EACjB9B,QAAgB,EAChB+B,MAAc,EACC;EACf,MAAM;IAAEf;EAAM,CAAC,GAAG,MAAMlC,QAAQ,CAC7BqC,IAAI,CAACS,SAAS,CAAC,CACfI,MAAM,CAAC;IAAE,CAACF,SAAS,GAAGC;EAAO,CAAC,CAAC,CAC/BE,EAAE,CAACJ,QAAQ,EAAE7B,QAAQ,CAAC;EAEzB,IAAIgB,KAAK,EAAE;IACT,MAAM,IAAI3B,KAAK,CAAC,2BAA2B2B,KAAK,CAACO,OAAO,EAAE,CAAC;EAC7D;AACF;;AAEA;AACA;AACA;AACA,eAAeW,kBAAkBA,CAC/BC,MAA2B,EAC3BC,MAA4B,EAC6D;EACzF,MAAMpC,QAAQ,GAAGmC,MAAM,CAACC,MAAM,CAACP,QAAQ,CAAW;EAClD,MAAM5B,WAAW,GAAGkC,MAAM,CAACC,MAAM,CAACN,SAAS,CAAW;;EAEtD;EACA,IAAI,CAAC7B,WAAW,IAAIA,WAAW,CAACoC,QAAQ,CAAC,UAAU,CAAC,EAAE;IACpD,OAAO;MAAE/C,MAAM,EAAE;IAAU,CAAC;EAC9B;EAEA,IAAI;IACF;IACA,MAAM;MAAEO,IAAI;MAAEL;IAAY,CAAC,GAAG,MAAMT,aAAa,CAC/CkB,WAAW,EACXmC,MAAM,CAACE,WAAW,IAAI,CAAC,GAAG,IAAI,GAAG,IACnC,CAAC;;IAED;IACA,MAAMC,YAAY,GAAGH,MAAM,CAACG,YAAY,IAAI,CAAC,YAAY,EAAE,WAAW,EAAE,YAAY,EAAE,WAAW,CAAC;IAClG,IAAI,CAACA,YAAY,CAACF,QAAQ,CAAC7C,WAAW,CAAC,EAAE;MACvC,OAAO;QACLF,MAAM,EAAE,SAAS;QACjB0B,KAAK,EAAE,6BAA6BxB,WAAW;MACjD,CAAC;IACH;;IAEA;IACA,MAAMoB,QAAQ,GAAGb,gBAAgB,CAACC,QAAQ,EAAEC,WAAW,EAAET,WAAW,CAAC;IACrE,MAAMuC,MAAM,GAAG,MAAMpB,eAAe,CAClCd,IAAI,EACJe,QAAQ,EACRwB,MAAM,CAACI,aAAa,EACpBJ,MAAM,CAACK,UACT,CAAC;;IAED;IACA,MAAMd,YAAY,CAChBS,MAAM,CAACR,SAAS,EAChBQ,MAAM,CAACP,QAAQ,EACfO,MAAM,CAACN,SAAS,EAChB9B,QAAQ,EACR+B,MACF,CAAC;IAED,OAAO;MAAEzC,MAAM,EAAE,UAAU;MAAEyC;IAAO,CAAC;EAEvC,CAAC,CAAC,OAAOf,KAAK,EAAE;IACd,OAAO;MACL1B,MAAM,EAAE,QAAQ;MAChB0B,KAAK,EAAEA,KAAK,YAAY3B,KAAK,GAAG2B,KAAK,CAACO,OAAO,GAAG;IAClD,CAAC;EACH;AACF;;AAEA;AACA;AACA;AACA,OAAO,eAAemB,aAAaA,CAACN,MAA4B,EAAiC;EAC/FO,OAAO,CAACC,GAAG,CAAC,mCAAmCR,MAAM,CAACR,SAAS,KAAK,CAAC;EAErE,MAAMiB,MAA4B,GAAG;IACnCC,OAAO,EAAE,IAAI;IACbC,SAAS,EAAE,CAAC;IACZC,QAAQ,EAAE,CAAC;IACXC,MAAM,EAAE,CAAC;IACTC,OAAO,EAAE,CAAC;IACVC,MAAM,EAAE,EAAE;IACVC,OAAO,EAAE;EACX,CAAC;EAED,IAAI;IACF;IACA,MAAM;MAAE5B,IAAI,EAAE6B,OAAO;MAAErC;IAAM,CAAC,GAAG,MAAMlC,QAAQ,CAC5CqC,IAAI,CAACiB,MAAM,CAACR,SAAS,CAAC,CACtB0B,MAAM,CAAC,GAAGlB,MAAM,CAACP,QAAQ,KAAKO,MAAM,CAACN,SAAS,EAAE,CAAC,CACjDyB,GAAG,CAACnB,MAAM,CAACN,SAAS,EAAE,IAAI,EAAE,IAAI,CAAC,CACjCyB,GAAG,CAACnB,MAAM,CAACN,SAAS,EAAE,IAAI,EAAE,EAAE,CAAC;IAElC,IAAId,KAAK,EAAE;MACT,MAAM,IAAI3B,KAAK,CAAC,4BAA4B2B,KAAK,CAACO,OAAO,EAAE,CAAC;IAC9D;IAEA,IAAI,CAAC8B,OAAO,IAAIA,OAAO,CAACG,MAAM,KAAK,CAAC,EAAE;MACpCb,OAAO,CAACC,GAAG,CAAC,iCAAiC,CAAC;MAC9C,OAAOC,MAAM;IACf;IAEAF,OAAO,CAACC,GAAG,CAAC,YAAYS,OAAO,CAACG,MAAM,iCAAiC,CAAC;;IAExE;IACA,KAAK,MAAMrB,MAAM,IAAIkB,OAAO,EAAE;MAC5B,MAAMI,UAAU,GAAGtB,MAA6B;MAChD,MAAMnC,QAAQ,GAAGyD,UAAU,CAACrB,MAAM,CAACP,QAAQ,CAAW;MACtD,MAAM5B,WAAW,GAAGwD,UAAU,CAACrB,MAAM,CAACN,SAAS,CAAW;MAE1Da,OAAO,CAACC,GAAG,CAAC,iBAAiB5C,QAAQ,KAAKC,WAAW,EAAE,CAAC;MAExD,MAAMyD,eAAe,GAAG,MAAMxB,kBAAkB,CAACuB,UAAU,EAAErB,MAAM,CAAC;MAEpES,MAAM,CAACE,SAAS,EAAE;MAClBF,MAAM,CAACO,OAAO,CAACO,IAAI,CAAC;QAClB3D,QAAQ;QACRC,WAAW;QACX8B,MAAM,EAAE2B,eAAe,CAAC3B,MAAM;QAC9BzC,MAAM,EAAEoE,eAAe,CAACpE,MAAM;QAC9B0B,KAAK,EAAE0C,eAAe,CAAC1C;MACzB,CAAC,CAAC;MAEF,QAAQ0C,eAAe,CAACpE,MAAM;QAC5B,KAAK,UAAU;UACbuD,MAAM,CAACG,QAAQ,EAAE;UACjBL,OAAO,CAACC,GAAG,CAAC,cAAc5C,QAAQ,KAAK0D,eAAe,CAAC3B,MAAM,EAAE,CAAC;UAChE;QACF,KAAK,QAAQ;UACXc,MAAM,CAACI,MAAM,EAAE;UACfJ,MAAM,CAACM,MAAM,CAACQ,IAAI,CAAC,GAAG3D,QAAQ,KAAK0D,eAAe,CAAC1C,KAAK,EAAE,CAAC;UAC3D2B,OAAO,CAAC3B,KAAK,CAAC,YAAYhB,QAAQ,KAAK0D,eAAe,CAAC1C,KAAK,EAAE,CAAC;UAC/D;QACF,KAAK,SAAS;UACZ6B,MAAM,CAACK,OAAO,EAAE;UAChBP,OAAO,CAACC,GAAG,CAAC,eAAe5C,QAAQ,KAAK0D,eAAe,CAAC1C,KAAK,IAAI,4BAA4B,EAAE,CAAC;UAChG;MACJ;;MAEA;MACA,MAAM,IAAI4C,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,GAAG,CAAC,CAAC;IACxD;IAEA,IAAIhB,MAAM,CAACI,MAAM,GAAG,CAAC,EAAE;MACrBJ,MAAM,CAACC,OAAO,GAAG,KAAK;IACxB;IAEAH,OAAO,CAACC,GAAG,CAAC,yBAAyB,CAAC;IACtCD,OAAO,CAACC,GAAG,CAAC,eAAeC,MAAM,CAACG,QAAQ,EAAE,CAAC;IAC7CL,OAAO,CAACC,GAAG,CAAC,gBAAgBC,MAAM,CAACK,OAAO,EAAE,CAAC;IAC7CP,OAAO,CAACC,GAAG,CAAC,aAAaC,MAAM,CAACI,MAAM,EAAE,CAAC;IAEzC,OAAOJ,MAAM;EAEf,CAAC,CAAC,OAAO7B,KAAK,EAAE;IACd6B,MAAM,CAACC,OAAO,GAAG,KAAK;IACtBD,MAAM,CAACM,MAAM,CAACQ,IAAI,CAAC3C,KAAK,YAAY3B,KAAK,GAAG2B,KAAK,CAACO,OAAO,GAAG,eAAe,CAAC;IAC5EoB,OAAO,CAAC3B,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;IAC5C,OAAO6B,MAAM;EACf;AACF;;AAEA;AACA;AACA;AACA,OAAO,MAAMkB,iBAAiB,GAAG;EAC/BC,eAAe,EAAE;IACfpC,SAAS,EAAE,2BAA2B;IACtCE,SAAS,EAAE,YAAY;IACvBD,QAAQ,EAAE,IAAI;IACdW,aAAa,EAAE,SAAS;IACxBC,UAAU,EAAE,aAAa;IACzBH,WAAW,EAAE,CAAC,GAAG,IAAI,GAAG,IAAI;IAAE;IAC9BC,YAAY,EAAE,CAAC,YAAY,EAAE,WAAW,EAAE,YAAY,EAAE,WAAW;EACrE,CAAyB;EAEzB0B,WAAW,EAAE;IACXrC,SAAS,EAAE,aAAa;IAAE;IAC1BE,SAAS,EAAE,WAAW;IACtBD,QAAQ,EAAE,eAAe;IACzBW,aAAa,EAAE,aAAa;IAC5BC,UAAU,EAAE,aAAa;IACzBH,WAAW,EAAE,EAAE,GAAG,IAAI,GAAG,IAAI;IAAE;IAC/BC,YAAY,EAAE,CAAC,YAAY,EAAE,WAAW,EAAE,YAAY;EACxD;AACF,CAAC;;AAED;AACA;AACA;AACA,OAAO,eAAe2B,qBAAqBA,CAAA,EAAkC;EAC3E,OAAOxB,aAAa,CAACqB,iBAAiB,CAACC,eAAe,CAAC;AACzD;;AAEA;AACA;AACA;AACA,OAAO,eAAeG,iBAAiBA,CAAC/B,MAAsC,EAAiC;EAC7G,MAAMgC,WAAW,GAAG;IAAE,GAAGL,iBAAiB,CAACE,WAAW;IAAE,GAAG7B;EAAO,CAAC;EACnE,OAAOM,aAAa,CAAC0B,WAAW,CAAC;AACnC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}