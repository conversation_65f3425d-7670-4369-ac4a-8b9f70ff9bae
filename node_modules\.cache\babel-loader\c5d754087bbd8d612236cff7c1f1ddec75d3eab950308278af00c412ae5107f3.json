{"ast": null, "code": "export class AuthError extends Error {\n  constructor(message, status, code) {\n    super(message);\n    this.__isAuthError = true;\n    this.name = 'AuthError';\n    this.status = status;\n    this.code = code;\n  }\n}\nexport function isAuthError(error) {\n  return typeof error === 'object' && error !== null && '__isAuthError' in error;\n}\nexport class AuthApiError extends AuthError {\n  constructor(message, status, code) {\n    super(message, status, code);\n    this.name = 'AuthApiError';\n    this.status = status;\n    this.code = code;\n  }\n}\nexport function isAuthApiError(error) {\n  return isAuthError(error) && error.name === 'AuthApiError';\n}\nexport class AuthUnknownError extends AuthError {\n  constructor(message, originalError) {\n    super(message);\n    this.name = 'AuthUnknownError';\n    this.originalError = originalError;\n  }\n}\nexport class CustomAuthError extends AuthError {\n  constructor(message, name, status, code) {\n    super(message, status, code);\n    this.name = name;\n    this.status = status;\n  }\n}\nexport class AuthSessionMissingError extends CustomAuthError {\n  constructor() {\n    super('Auth session missing!', 'AuthSessionMissingError', 400, undefined);\n  }\n}\nexport function isAuthSessionMissingError(error) {\n  return isAuthError(error) && error.name === 'AuthSessionMissingError';\n}\nexport class AuthInvalidTokenResponseError extends CustomAuthError {\n  constructor() {\n    super('Auth session or user missing', 'AuthInvalidTokenResponseError', 500, undefined);\n  }\n}\nexport class AuthInvalidCredentialsError extends CustomAuthError {\n  constructor(message) {\n    super(message, 'AuthInvalidCredentialsError', 400, undefined);\n  }\n}\nexport class AuthImplicitGrantRedirectError extends CustomAuthError {\n  constructor(message, details = null) {\n    super(message, 'AuthImplicitGrantRedirectError', 500, undefined);\n    this.details = null;\n    this.details = details;\n  }\n  toJSON() {\n    return {\n      name: this.name,\n      message: this.message,\n      status: this.status,\n      details: this.details\n    };\n  }\n}\nexport function isAuthImplicitGrantRedirectError(error) {\n  return isAuthError(error) && error.name === 'AuthImplicitGrantRedirectError';\n}\nexport class AuthPKCEGrantCodeExchangeError extends CustomAuthError {\n  constructor(message, details = null) {\n    super(message, 'AuthPKCEGrantCodeExchangeError', 500, undefined);\n    this.details = null;\n    this.details = details;\n  }\n  toJSON() {\n    return {\n      name: this.name,\n      message: this.message,\n      status: this.status,\n      details: this.details\n    };\n  }\n}\nexport class AuthRetryableFetchError extends CustomAuthError {\n  constructor(message, status) {\n    super(message, 'AuthRetryableFetchError', status, undefined);\n  }\n}\nexport function isAuthRetryableFetchError(error) {\n  return isAuthError(error) && error.name === 'AuthRetryableFetchError';\n}\n/**\n * This error is thrown on certain methods when the password used is deemed\n * weak. Inspect the reasons to identify what password strength rules are\n * inadequate.\n */\nexport class AuthWeakPasswordError extends CustomAuthError {\n  constructor(message, status, reasons) {\n    super(message, 'AuthWeakPasswordError', status, 'weak_password');\n    this.reasons = reasons;\n  }\n}\nexport function isAuthWeakPasswordError(error) {\n  return isAuthError(error) && error.name === 'AuthWeakPasswordError';\n}\nexport class AuthInvalidJwtError extends CustomAuthError {\n  constructor(message) {\n    super(message, 'AuthInvalidJwtError', 400, 'invalid_jwt');\n  }\n}", "map": {"version": 3, "names": ["<PERSON>th<PERSON><PERSON><PERSON>", "Error", "constructor", "message", "status", "code", "__is<PERSON><PERSON><PERSON><PERSON><PERSON>", "name", "isAuthError", "error", "AuthApiError", "isAuthApiError", "AuthUnknownError", "originalError", "CustomAuthError", "AuthSessionMissingError", "undefined", "isAuthSessionMissingError", "AuthInvalidTokenResponseError", "AuthInvalidCredentialsError", "AuthImplicitGrantRedirectError", "details", "toJSON", "isAuthImplicitGrantRedirectError", "AuthPKCEGrantCodeExchangeError", "AuthRetryableFetchError", "isAuthRetryableFetchError", "AuthWeakPasswordError", "reasons", "isAuthWeakPasswordError", "AuthInvalidJwtError"], "sources": ["C:\\Users\\<USER>\\lostandfound\\discfinder-app\\node_modules\\@supabase\\auth-js\\src\\lib\\errors.ts"], "sourcesContent": ["import { WeakPasswordReasons } from './types'\nimport { ErrorCode } from './error-codes'\n\nexport class AuthError extends Error {\n  /**\n   * Error code associated with the error. Most errors coming from\n   * HTTP responses will have a code, though some errors that occur\n   * before a response is received will not have one present. In that\n   * case {@link #status} will also be undefined.\n   */\n  code: ErrorCode | (string & {}) | undefined\n\n  /** HTTP status code that caused the error. */\n  status: number | undefined\n\n  protected __isAuthError = true\n\n  constructor(message: string, status?: number, code?: string) {\n    super(message)\n    this.name = 'AuthError'\n    this.status = status\n    this.code = code\n  }\n}\n\nexport function isAuthError(error: unknown): error is AuthError {\n  return typeof error === 'object' && error !== null && '__isAuthError' in error\n}\n\nexport class AuthApiError extends AuthError {\n  status: number\n\n  constructor(message: string, status: number, code: string | undefined) {\n    super(message, status, code)\n    this.name = 'AuthApiError'\n    this.status = status\n    this.code = code\n  }\n}\n\nexport function isAuthApiError(error: unknown): error is AuthApiError {\n  return isAuthError(error) && error.name === 'AuthApiError'\n}\n\nexport class AuthUnknownError extends AuthError {\n  originalError: unknown\n\n  constructor(message: string, originalError: unknown) {\n    super(message)\n    this.name = 'AuthUnknownError'\n    this.originalError = originalError\n  }\n}\n\nexport class CustomAuthError extends AuthError {\n  name: string\n  status: number\n\n  constructor(message: string, name: string, status: number, code: string | undefined) {\n    super(message, status, code)\n    this.name = name\n    this.status = status\n  }\n}\n\nexport class AuthSessionMissingError extends CustomAuthError {\n  constructor() {\n    super('Auth session missing!', 'AuthSessionMissingError', 400, undefined)\n  }\n}\n\nexport function isAuthSessionMissingError(error: any): error is AuthSessionMissingError {\n  return isAuthError(error) && error.name === 'AuthSessionMissingError'\n}\n\nexport class AuthInvalidTokenResponseError extends CustomAuthError {\n  constructor() {\n    super('Auth session or user missing', 'AuthInvalidTokenResponseError', 500, undefined)\n  }\n}\n\nexport class AuthInvalidCredentialsError extends CustomAuthError {\n  constructor(message: string) {\n    super(message, 'AuthInvalidCredentialsError', 400, undefined)\n  }\n}\n\nexport class AuthImplicitGrantRedirectError extends CustomAuthError {\n  details: { error: string; code: string } | null = null\n  constructor(message: string, details: { error: string; code: string } | null = null) {\n    super(message, 'AuthImplicitGrantRedirectError', 500, undefined)\n    this.details = details\n  }\n\n  toJSON() {\n    return {\n      name: this.name,\n      message: this.message,\n      status: this.status,\n      details: this.details,\n    }\n  }\n}\n\nexport function isAuthImplicitGrantRedirectError(\n  error: any\n): error is AuthImplicitGrantRedirectError {\n  return isAuthError(error) && error.name === 'AuthImplicitGrantRedirectError'\n}\n\nexport class AuthPKCEGrantCodeExchangeError extends CustomAuthError {\n  details: { error: string; code: string } | null = null\n\n  constructor(message: string, details: { error: string; code: string } | null = null) {\n    super(message, 'AuthPKCEGrantCodeExchangeError', 500, undefined)\n    this.details = details\n  }\n\n  toJSON() {\n    return {\n      name: this.name,\n      message: this.message,\n      status: this.status,\n      details: this.details,\n    }\n  }\n}\n\nexport class AuthRetryableFetchError extends CustomAuthError {\n  constructor(message: string, status: number) {\n    super(message, 'AuthRetryableFetchError', status, undefined)\n  }\n}\n\nexport function isAuthRetryableFetchError(error: unknown): error is AuthRetryableFetchError {\n  return isAuthError(error) && error.name === 'AuthRetryableFetchError'\n}\n\n/**\n * This error is thrown on certain methods when the password used is deemed\n * weak. Inspect the reasons to identify what password strength rules are\n * inadequate.\n */\nexport class AuthWeakPasswordError extends CustomAuthError {\n  /**\n   * Reasons why the password is deemed weak.\n   */\n  reasons: WeakPasswordReasons[]\n\n  constructor(message: string, status: number, reasons: string[]) {\n    super(message, 'AuthWeakPasswordError', status, 'weak_password')\n\n    this.reasons = reasons\n  }\n}\n\nexport function isAuthWeakPasswordError(error: unknown): error is AuthWeakPasswordError {\n  return isAuthError(error) && error.name === 'AuthWeakPasswordError'\n}\n\nexport class AuthInvalidJwtError extends CustomAuthError {\n  constructor(message: string) {\n    super(message, 'AuthInvalidJwtError', 400, 'invalid_jwt')\n  }\n}\n"], "mappings": "AAGA,OAAM,MAAOA,SAAU,SAAQC,KAAK;EAclCC,YAAYC,OAAe,EAAEC,MAAe,EAAEC,IAAa;IACzD,KAAK,CAACF,OAAO,CAAC;IAHN,KAAAG,aAAa,GAAG,IAAI;IAI5B,IAAI,CAACC,IAAI,GAAG,WAAW;IACvB,IAAI,CAACH,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACC,IAAI,GAAGA,IAAI;EAClB;;AAGF,OAAM,SAAUG,WAAWA,CAACC,KAAc;EACxC,OAAO,OAAOA,KAAK,KAAK,QAAQ,IAAIA,KAAK,KAAK,IAAI,IAAI,eAAe,IAAIA,KAAK;AAChF;AAEA,OAAM,MAAOC,YAAa,SAAQV,SAAS;EAGzCE,YAAYC,OAAe,EAAEC,MAAc,EAAEC,IAAwB;IACnE,KAAK,CAACF,OAAO,EAAEC,MAAM,EAAEC,IAAI,CAAC;IAC5B,IAAI,CAACE,IAAI,GAAG,cAAc;IAC1B,IAAI,CAACH,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACC,IAAI,GAAGA,IAAI;EAClB;;AAGF,OAAM,SAAUM,cAAcA,CAACF,KAAc;EAC3C,OAAOD,WAAW,CAACC,KAAK,CAAC,IAAIA,KAAK,CAACF,IAAI,KAAK,cAAc;AAC5D;AAEA,OAAM,MAAOK,gBAAiB,SAAQZ,SAAS;EAG7CE,YAAYC,OAAe,EAAEU,aAAsB;IACjD,KAAK,CAACV,OAAO,CAAC;IACd,IAAI,CAACI,IAAI,GAAG,kBAAkB;IAC9B,IAAI,CAACM,aAAa,GAAGA,aAAa;EACpC;;AAGF,OAAM,MAAOC,eAAgB,SAAQd,SAAS;EAI5CE,YAAYC,OAAe,EAAEI,IAAY,EAAEH,MAAc,EAAEC,IAAwB;IACjF,KAAK,CAACF,OAAO,EAAEC,MAAM,EAAEC,IAAI,CAAC;IAC5B,IAAI,CAACE,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACH,MAAM,GAAGA,MAAM;EACtB;;AAGF,OAAM,MAAOW,uBAAwB,SAAQD,eAAe;EAC1DZ,YAAA;IACE,KAAK,CAAC,uBAAuB,EAAE,yBAAyB,EAAE,GAAG,EAAEc,SAAS,CAAC;EAC3E;;AAGF,OAAM,SAAUC,yBAAyBA,CAACR,KAAU;EAClD,OAAOD,WAAW,CAACC,KAAK,CAAC,IAAIA,KAAK,CAACF,IAAI,KAAK,yBAAyB;AACvE;AAEA,OAAM,MAAOW,6BAA8B,SAAQJ,eAAe;EAChEZ,YAAA;IACE,KAAK,CAAC,8BAA8B,EAAE,+BAA+B,EAAE,GAAG,EAAEc,SAAS,CAAC;EACxF;;AAGF,OAAM,MAAOG,2BAA4B,SAAQL,eAAe;EAC9DZ,YAAYC,OAAe;IACzB,KAAK,CAACA,OAAO,EAAE,6BAA6B,EAAE,GAAG,EAAEa,SAAS,CAAC;EAC/D;;AAGF,OAAM,MAAOI,8BAA+B,SAAQN,eAAe;EAEjEZ,YAAYC,OAAe,EAAEkB,OAAA,GAAkD,IAAI;IACjF,KAAK,CAAClB,OAAO,EAAE,gCAAgC,EAAE,GAAG,EAAEa,SAAS,CAAC;IAFlE,KAAAK,OAAO,GAA2C,IAAI;IAGpD,IAAI,CAACA,OAAO,GAAGA,OAAO;EACxB;EAEAC,MAAMA,CAAA;IACJ,OAAO;MACLf,IAAI,EAAE,IAAI,CAACA,IAAI;MACfJ,OAAO,EAAE,IAAI,CAACA,OAAO;MACrBC,MAAM,EAAE,IAAI,CAACA,MAAM;MACnBiB,OAAO,EAAE,IAAI,CAACA;KACf;EACH;;AAGF,OAAM,SAAUE,gCAAgCA,CAC9Cd,KAAU;EAEV,OAAOD,WAAW,CAACC,KAAK,CAAC,IAAIA,KAAK,CAACF,IAAI,KAAK,gCAAgC;AAC9E;AAEA,OAAM,MAAOiB,8BAA+B,SAAQV,eAAe;EAGjEZ,YAAYC,OAAe,EAAEkB,OAAA,GAAkD,IAAI;IACjF,KAAK,CAAClB,OAAO,EAAE,gCAAgC,EAAE,GAAG,EAAEa,SAAS,CAAC;IAHlE,KAAAK,OAAO,GAA2C,IAAI;IAIpD,IAAI,CAACA,OAAO,GAAGA,OAAO;EACxB;EAEAC,MAAMA,CAAA;IACJ,OAAO;MACLf,IAAI,EAAE,IAAI,CAACA,IAAI;MACfJ,OAAO,EAAE,IAAI,CAACA,OAAO;MACrBC,MAAM,EAAE,IAAI,CAACA,MAAM;MACnBiB,OAAO,EAAE,IAAI,CAACA;KACf;EACH;;AAGF,OAAM,MAAOI,uBAAwB,SAAQX,eAAe;EAC1DZ,YAAYC,OAAe,EAAEC,MAAc;IACzC,KAAK,CAACD,OAAO,EAAE,yBAAyB,EAAEC,MAAM,EAAEY,SAAS,CAAC;EAC9D;;AAGF,OAAM,SAAUU,yBAAyBA,CAACjB,KAAc;EACtD,OAAOD,WAAW,CAACC,KAAK,CAAC,IAAIA,KAAK,CAACF,IAAI,KAAK,yBAAyB;AACvE;AAEA;;;;;AAKA,OAAM,MAAOoB,qBAAsB,SAAQb,eAAe;EAMxDZ,YAAYC,OAAe,EAAEC,MAAc,EAAEwB,OAAiB;IAC5D,KAAK,CAACzB,OAAO,EAAE,uBAAuB,EAAEC,MAAM,EAAE,eAAe,CAAC;IAEhE,IAAI,CAACwB,OAAO,GAAGA,OAAO;EACxB;;AAGF,OAAM,SAAUC,uBAAuBA,CAACpB,KAAc;EACpD,OAAOD,WAAW,CAACC,KAAK,CAAC,IAAIA,KAAK,CAACF,IAAI,KAAK,uBAAuB;AACrE;AAEA,OAAM,MAAOuB,mBAAoB,SAAQhB,eAAe;EACtDZ,YAAYC,OAAe;IACzB,KAAK,CAACA,OAAO,EAAE,qBAAqB,EAAE,GAAG,EAAE,aAAa,CAAC;EAC3D", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}