{"ast": null, "code": "import { millisecondsInSecond } from \"./constants.js\";\n\n/**\n * @name secondsToMilliseconds\n * @category Conversion Helpers\n * @summary Convert seconds to milliseconds.\n *\n * @description\n * Convert a number of seconds to a full number of milliseconds.\n *\n * @param seconds - The number of seconds to be converted\n *\n * @returns The number of seconds converted in milliseconds\n *\n * @example\n * // Convert 2 seconds into milliseconds\n * const result = secondsToMilliseconds(2)\n * //=> 2000\n */\nexport function secondsToMilliseconds(seconds) {\n  return seconds * millisecondsInSecond;\n}\n\n// Fallback for modularized imports:\nexport default secondsToMilliseconds;", "map": {"version": 3, "names": ["millisecondsInSecond", "secondsToMilliseconds", "seconds"], "sources": ["C:/Users/<USER>/node_modules/date-fns/secondsToMilliseconds.js"], "sourcesContent": ["import { millisecondsInSecond } from \"./constants.js\";\n\n/**\n * @name secondsToMilliseconds\n * @category Conversion Helpers\n * @summary Convert seconds to milliseconds.\n *\n * @description\n * Convert a number of seconds to a full number of milliseconds.\n *\n * @param seconds - The number of seconds to be converted\n *\n * @returns The number of seconds converted in milliseconds\n *\n * @example\n * // Convert 2 seconds into milliseconds\n * const result = secondsToMilliseconds(2)\n * //=> 2000\n */\nexport function secondsToMilliseconds(seconds) {\n  return seconds * millisecondsInSecond;\n}\n\n// Fallback for modularized imports:\nexport default secondsToMilliseconds;\n"], "mappings": "AAAA,SAASA,oBAAoB,QAAQ,gBAAgB;;AAErD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,qBAAqBA,CAACC,OAAO,EAAE;EAC7C,OAAOA,OAAO,GAAGF,oBAAoB;AACvC;;AAEA;AACA,eAAeC,qBAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}