{"ast": null, "code": "/**\n * https://mathiasbynens.be/notes/globalthis\n */\nexport function polyfillGlobalThis() {\n  if (typeof globalThis === 'object') return;\n  try {\n    Object.defineProperty(Object.prototype, '__magic__', {\n      get: function () {\n        return this;\n      },\n      configurable: true\n    });\n    // @ts-expect-error 'Allow access to magic'\n    __magic__.globalThis = __magic__;\n    // @ts-expect-error 'Allow access to magic'\n    delete Object.prototype.__magic__;\n  } catch (e) {\n    if (typeof self !== 'undefined') {\n      // @ts-expect-error 'Allow access to globals'\n      self.globalThis = self;\n    }\n  }\n}", "map": {"version": 3, "names": ["polyfillGlobalThis", "globalThis", "Object", "defineProperty", "prototype", "get", "configurable", "__magic__", "e", "self"], "sources": ["C:\\Users\\<USER>\\node_modules\\@supabase\\auth-js\\src\\lib\\polyfills.ts"], "sourcesContent": ["/**\n * https://mathiasbynens.be/notes/globalthis\n */\nexport function polyfillGlobalThis() {\n  if (typeof globalThis === 'object') return\n  try {\n    Object.defineProperty(Object.prototype, '__magic__', {\n      get: function () {\n        return this\n      },\n      configurable: true,\n    })\n    // @ts-expect-error 'Allow access to magic'\n    __magic__.globalThis = __magic__\n    // @ts-expect-error 'Allow access to magic'\n    delete Object.prototype.__magic__\n  } catch (e) {\n    if (typeof self !== 'undefined') {\n      // @ts-expect-error 'Allow access to globals'\n      self.globalThis = self\n    }\n  }\n}\n"], "mappings": "AAAA;;;AAGA,OAAM,SAAUA,kBAAkBA,CAAA;EAChC,IAAI,OAAOC,UAAU,KAAK,QAAQ,EAAE;EACpC,IAAI;IACFC,MAAM,CAACC,cAAc,CAACD,MAAM,CAACE,SAAS,EAAE,WAAW,EAAE;MACnDC,GAAG,EAAE,SAAAA,CAAA;QACH,OAAO,IAAI;MACb,CAAC;MACDC,YAAY,EAAE;KACf,CAAC;IACF;IACAC,SAAS,CAACN,UAAU,GAAGM,SAAS;IAChC;IACA,OAAOL,MAAM,CAACE,SAAS,CAACG,SAAS;GAClC,CAAC,OAAOC,CAAC,EAAE;IACV,IAAI,OAAOC,IAAI,KAAK,WAAW,EAAE;MAC/B;MACAA,IAAI,CAACR,UAAU,GAAGQ,IAAI;;;AAG5B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}