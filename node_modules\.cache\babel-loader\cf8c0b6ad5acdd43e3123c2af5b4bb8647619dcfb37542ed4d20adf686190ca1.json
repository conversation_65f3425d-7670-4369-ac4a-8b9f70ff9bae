{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\lostandfound\\\\discfinder-app\\\\src\\\\components\\\\PhotoMigrationManager.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { migratePhotos, MIGRATION_CONFIGS } from '../lib/photoMigration';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst PhotoMigrationManager = () => {\n  _s();\n  const [migrationStatus, setMigrationStatus] = useState({\n    isRunning: false,\n    type: null,\n    result: null\n  });\n  const handleMigration = async (type, config) => {\n    setMigrationStatus({\n      isRunning: true,\n      type,\n      result: null\n    });\n    try {\n      const result = await migratePhotos(config);\n      setMigrationStatus({\n        isRunning: false,\n        type,\n        result\n      });\n    } catch (error) {\n      console.error('Migration failed:', error);\n      setMigrationStatus({\n        isRunning: false,\n        type,\n        result: {\n          success: false,\n          processed: 0,\n          migrated: 0,\n          failed: 1,\n          skipped: 0,\n          errors: [error instanceof Error ? error.message : 'Unknown error'],\n          details: []\n        }\n      });\n    }\n  };\n  const handleProfileMigration = () => {\n    handleMigration('profiles', MIGRATION_CONFIGS.PROFILE_AVATARS);\n  };\n  const handleDiscMigration = () => {\n    handleMigration('discs', MIGRATION_CONFIGS.DISC_PHOTOS);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"max-w-4xl mx-auto p-6\",\n    children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n      className: \"text-3xl font-bold text-gray-900 mb-6\",\n      children: \"Photo Migration Manager\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 59,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-blue-50 border border-blue-200 rounded-lg p-6 mb-6\",\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        className: \"text-lg font-medium text-blue-900 mb-2\",\n        children: \"About Photo Migration\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 62,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-blue-800 text-sm space-y-2\",\n        children: [/*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"This tool downloads photos from external URLs (like Google Photos, Dropbox, etc.) and uploads them to your Supabase Storage.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 64,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"Benefits:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 65,\n            columnNumber: 14\n          }, this), \" Faster loading, better reliability, no broken links if external services change.\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 65,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"Process:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 66,\n            columnNumber: 14\n          }, this), \" Downloads \\u2192 Validates \\u2192 Uploads to Supabase \\u2192 Updates database URLs\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 66,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 63,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 61,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white shadow rounded-lg p-6 mb-6\",\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        className: \"text-xl font-semibold text-gray-900 mb-4\",\n        children: \"Profile Avatar Migration\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 72,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-gray-600 mb-4\",\n        children: \"Migrate profile photos from imported users to Supabase Storage. This will download photos from external URLs (Google Photos, etc.) and store them in your own storage.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 73,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center space-x-4 mb-4\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-sm text-gray-500\",\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Source:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 80,\n              columnNumber: 16\n            }, this), \" imported_profiles_staging.avatar_url\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 80,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Destination:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 81,\n              columnNumber: 16\n            }, this), \" disc-images/avatars/\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 81,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Max Size:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 82,\n              columnNumber: 16\n            }, this), \" 5MB per image\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 82,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 79,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 78,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: handleProfileMigration,\n        disabled: migrationStatus.isRunning,\n        className: \"bg-blue-600 hover:bg-blue-700 disabled:bg-blue-300 text-white px-4 py-2 rounded-md font-medium\",\n        children: migrationStatus.isRunning && migrationStatus.type === 'profiles' ? 'Migrating Profile Photos...' : 'Migrate Profile Photos'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 86,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 71,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white shadow rounded-lg p-6 mb-6\",\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        className: \"text-xl font-semibold text-gray-900 mb-4\",\n        children: \"Disc Photo Migration\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 100,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-gray-600 mb-4\",\n        children: \"Migrate disc photos from imported disc records to Supabase Storage. This will be available when you import disc data from your previous app.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 101,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center space-x-4 mb-4\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-sm text-gray-500\",\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Source:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 108,\n              columnNumber: 16\n            }, this), \" found_discs.photo_url (when disc import is implemented)\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 108,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Destination:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 109,\n              columnNumber: 16\n            }, this), \" disc-images/disc-photos/\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 109,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Max Size:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 110,\n              columnNumber: 16\n            }, this), \" 10MB per image\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 110,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 107,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 106,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: handleDiscMigration,\n        disabled: migrationStatus.isRunning,\n        className: \"bg-green-600 hover:bg-green-700 disabled:bg-green-300 text-white px-4 py-2 rounded-md font-medium\",\n        children: migrationStatus.isRunning && migrationStatus.type === 'discs' ? 'Migrating Disc Photos...' : 'Migrate Disc Photos'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 114,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 99,\n      columnNumber: 7\n    }, this), migrationStatus.result && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: `p-6 rounded-lg mb-6 ${migrationStatus.result.success ? 'bg-green-50 border border-green-200' : 'bg-red-50 border border-red-200'}`,\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        className: `font-medium mb-4 ${migrationStatus.result.success ? 'text-green-800' : 'text-red-800'}`,\n        children: [migrationStatus.type === 'profiles' ? 'Profile Photo' : 'Disc Photo', \" Migration \", migrationStatus.result.success ? 'Completed' : 'Failed']\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 131,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: `grid grid-cols-2 md:grid-cols-4 gap-4 mb-4 text-sm ${migrationStatus.result.success ? 'text-green-700' : 'text-red-700'}`,\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"font-medium\",\n            children: \"Processed\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 141,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-lg\",\n            children: migrationStatus.result.processed\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 142,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 140,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"font-medium\",\n            children: \"Migrated\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 145,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-lg\",\n            children: migrationStatus.result.migrated\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 146,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 144,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"font-medium\",\n            children: \"Skipped\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 149,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-lg\",\n            children: migrationStatus.result.skipped\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 150,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 148,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"font-medium\",\n            children: \"Failed\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 153,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-lg\",\n            children: migrationStatus.result.failed\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 154,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 152,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 137,\n        columnNumber: 11\n      }, this), migrationStatus.result.errors.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mt-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"font-medium mb-2\",\n          children: \"Errors:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 160,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white rounded border max-h-40 overflow-y-auto\",\n          children: /*#__PURE__*/_jsxDEV(\"ul\", {\n            className: \"text-xs space-y-1 p-3\",\n            children: migrationStatus.result.errors.map((error, index) => /*#__PURE__*/_jsxDEV(\"li\", {\n              className: \"text-red-600\",\n              children: [index + 1, \". \", error]\n            }, index, true, {\n              fileName: _jsxFileName,\n              lineNumber: 164,\n              columnNumber: 21\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 162,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 161,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 159,\n        columnNumber: 13\n      }, this), migrationStatus.result.details.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mt-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"font-medium mb-2\",\n          children: \"Details:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 175,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white rounded border max-h-60 overflow-y-auto\",\n          children: /*#__PURE__*/_jsxDEV(\"table\", {\n            className: \"min-w-full text-xs\",\n            children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n              className: \"bg-gray-50\",\n              children: /*#__PURE__*/_jsxDEV(\"tr\", {\n                children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                  className: \"px-3 py-2 text-left\",\n                  children: \"Record ID\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 180,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  className: \"px-3 py-2 text-left\",\n                  children: \"Status\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 181,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  className: \"px-3 py-2 text-left\",\n                  children: \"Original URL\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 182,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  className: \"px-3 py-2 text-left\",\n                  children: \"New URL\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 183,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 179,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 178,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n              className: \"divide-y divide-gray-200\",\n              children: migrationStatus.result.details.map((detail, index) => /*#__PURE__*/_jsxDEV(\"tr\", {\n                children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                  className: \"px-3 py-2 font-mono\",\n                  children: detail.recordId\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 189,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  className: \"px-3 py-2\",\n                  children: /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: `inline-flex px-2 py-1 text-xs font-semibold rounded-full ${detail.status === 'migrated' ? 'bg-green-100 text-green-800' : detail.status === 'failed' ? 'bg-red-100 text-red-800' : 'bg-yellow-100 text-yellow-800'}`,\n                    children: detail.status\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 191,\n                    columnNumber: 27\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 190,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  className: \"px-3 py-2 max-w-xs truncate\",\n                  title: detail.originalUrl,\n                  children: detail.originalUrl\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 199,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  className: \"px-3 py-2 max-w-xs truncate\",\n                  title: detail.newUrl,\n                  children: detail.newUrl || detail.error || '-'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 202,\n                  columnNumber: 25\n                }, this)]\n              }, index, true, {\n                fileName: _jsxFileName,\n                lineNumber: 188,\n                columnNumber: 23\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 186,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 177,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 176,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 174,\n        columnNumber: 13\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 128,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-gray-50 border border-gray-200 rounded-lg p-6\",\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        className: \"text-lg font-medium text-gray-900 mb-2\",\n        children: \"Command Line Usage\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 217,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-gray-700 text-sm space-y-2\",\n        children: [/*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"You can also run photo migrations from the command line:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 219,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-gray-800 text-green-400 p-3 rounded font-mono text-xs\",\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"# Migrate profile photos\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 221,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"npm run migrate-photos\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 222,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 223,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"# Migrate disc photos (when available)\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 224,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"npm run migrate-disc-photos\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 225,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 220,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"Note:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 227,\n            columnNumber: 14\n          }, this), \" Command line migration may be faster for large batches.\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 227,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 218,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 216,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 58,\n    columnNumber: 5\n  }, this);\n};\n_s(PhotoMigrationManager, \"FMrdHFJQLSZV7wMV2dQ4GUmRNoY=\");\n_c = PhotoMigrationManager;\nexport default PhotoMigrationManager;\nvar _c;\n$RefreshReg$(_c, \"PhotoMigrationManager\");", "map": {"version": 3, "names": ["React", "useState", "migratePhotos", "MIGRATION_CONFIGS", "jsxDEV", "_jsxDEV", "PhotoMigrationManager", "_s", "migrationStatus", "setMigrationStatus", "isRunning", "type", "result", "handleMigration", "config", "error", "console", "success", "processed", "migrated", "failed", "skipped", "errors", "Error", "message", "details", "handleProfileMigration", "PROFILE_AVATARS", "handleDiscMigration", "DISC_PHOTOS", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "disabled", "length", "map", "index", "detail", "recordId", "status", "title", "originalUrl", "newUrl", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/lostandfound/discfinder-app/src/components/PhotoMigrationManager.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { migratePhotos, MIGRATION_CONFIGS, PhotoMigrationResult, PhotoMigrationConfig } from '../lib/photoMigration';\n\ninterface MigrationStatus {\n  isRunning: boolean;\n  type: string | null;\n  result: PhotoMigrationResult | null;\n}\n\nconst PhotoMigrationManager: React.FC = () => {\n  const [migrationStatus, setMigrationStatus] = useState<MigrationStatus>({\n    isRunning: false,\n    type: null,\n    result: null\n  });\n\n  const handleMigration = async (type: 'profiles' | 'discs', config: PhotoMigrationConfig) => {\n    setMigrationStatus({\n      isRunning: true,\n      type,\n      result: null\n    });\n\n    try {\n      const result = await migratePhotos(config);\n      setMigrationStatus({\n        isRunning: false,\n        type,\n        result\n      });\n    } catch (error) {\n      console.error('Migration failed:', error);\n      setMigrationStatus({\n        isRunning: false,\n        type,\n        result: {\n          success: false,\n          processed: 0,\n          migrated: 0,\n          failed: 1,\n          skipped: 0,\n          errors: [error instanceof Error ? error.message : 'Unknown error'],\n          details: []\n        }\n      });\n    }\n  };\n\n  const handleProfileMigration = () => {\n    handleMigration('profiles', MIGRATION_CONFIGS.PROFILE_AVATARS);\n  };\n\n  const handleDiscMigration = () => {\n    handleMigration('discs', MIGRATION_CONFIGS.DISC_PHOTOS);\n  };\n\n  return (\n    <div className=\"max-w-4xl mx-auto p-6\">\n      <h1 className=\"text-3xl font-bold text-gray-900 mb-6\">Photo Migration Manager</h1>\n      \n      <div className=\"bg-blue-50 border border-blue-200 rounded-lg p-6 mb-6\">\n        <h2 className=\"text-lg font-medium text-blue-900 mb-2\">About Photo Migration</h2>\n        <div className=\"text-blue-800 text-sm space-y-2\">\n          <p>This tool downloads photos from external URLs (like Google Photos, Dropbox, etc.) and uploads them to your Supabase Storage.</p>\n          <p><strong>Benefits:</strong> Faster loading, better reliability, no broken links if external services change.</p>\n          <p><strong>Process:</strong> Downloads → Validates → Uploads to Supabase → Updates database URLs</p>\n        </div>\n      </div>\n\n      {/* Profile Avatar Migration */}\n      <div className=\"bg-white shadow rounded-lg p-6 mb-6\">\n        <h2 className=\"text-xl font-semibold text-gray-900 mb-4\">Profile Avatar Migration</h2>\n        <p className=\"text-gray-600 mb-4\">\n          Migrate profile photos from imported users to Supabase Storage. This will download photos from \n          external URLs (Google Photos, etc.) and store them in your own storage.\n        </p>\n        \n        <div className=\"flex items-center space-x-4 mb-4\">\n          <div className=\"text-sm text-gray-500\">\n            <p><strong>Source:</strong> imported_profiles_staging.avatar_url</p>\n            <p><strong>Destination:</strong> disc-images/avatars/</p>\n            <p><strong>Max Size:</strong> 5MB per image</p>\n          </div>\n        </div>\n\n        <button\n          onClick={handleProfileMigration}\n          disabled={migrationStatus.isRunning}\n          className=\"bg-blue-600 hover:bg-blue-700 disabled:bg-blue-300 text-white px-4 py-2 rounded-md font-medium\"\n        >\n          {migrationStatus.isRunning && migrationStatus.type === 'profiles' \n            ? 'Migrating Profile Photos...' \n            : 'Migrate Profile Photos'\n          }\n        </button>\n      </div>\n\n      {/* Disc Photo Migration */}\n      <div className=\"bg-white shadow rounded-lg p-6 mb-6\">\n        <h2 className=\"text-xl font-semibold text-gray-900 mb-4\">Disc Photo Migration</h2>\n        <p className=\"text-gray-600 mb-4\">\n          Migrate disc photos from imported disc records to Supabase Storage. This will be available \n          when you import disc data from your previous app.\n        </p>\n        \n        <div className=\"flex items-center space-x-4 mb-4\">\n          <div className=\"text-sm text-gray-500\">\n            <p><strong>Source:</strong> found_discs.photo_url (when disc import is implemented)</p>\n            <p><strong>Destination:</strong> disc-images/disc-photos/</p>\n            <p><strong>Max Size:</strong> 10MB per image</p>\n          </div>\n        </div>\n\n        <button\n          onClick={handleDiscMigration}\n          disabled={migrationStatus.isRunning}\n          className=\"bg-green-600 hover:bg-green-700 disabled:bg-green-300 text-white px-4 py-2 rounded-md font-medium\"\n        >\n          {migrationStatus.isRunning && migrationStatus.type === 'discs' \n            ? 'Migrating Disc Photos...' \n            : 'Migrate Disc Photos'\n          }\n        </button>\n      </div>\n\n      {/* Migration Results */}\n      {migrationStatus.result && (\n        <div className={`p-6 rounded-lg mb-6 ${\n          migrationStatus.result.success ? 'bg-green-50 border border-green-200' : 'bg-red-50 border border-red-200'\n        }`}>\n          <h3 className={`font-medium mb-4 ${\n            migrationStatus.result.success ? 'text-green-800' : 'text-red-800'\n          }`}>\n            {migrationStatus.type === 'profiles' ? 'Profile Photo' : 'Disc Photo'} Migration {migrationStatus.result.success ? 'Completed' : 'Failed'}\n          </h3>\n          \n          <div className={`grid grid-cols-2 md:grid-cols-4 gap-4 mb-4 text-sm ${\n            migrationStatus.result.success ? 'text-green-700' : 'text-red-700'\n          }`}>\n            <div>\n              <p className=\"font-medium\">Processed</p>\n              <p className=\"text-lg\">{migrationStatus.result.processed}</p>\n            </div>\n            <div>\n              <p className=\"font-medium\">Migrated</p>\n              <p className=\"text-lg\">{migrationStatus.result.migrated}</p>\n            </div>\n            <div>\n              <p className=\"font-medium\">Skipped</p>\n              <p className=\"text-lg\">{migrationStatus.result.skipped}</p>\n            </div>\n            <div>\n              <p className=\"font-medium\">Failed</p>\n              <p className=\"text-lg\">{migrationStatus.result.failed}</p>\n            </div>\n          </div>\n\n          {migrationStatus.result.errors.length > 0 && (\n            <div className=\"mt-4\">\n              <p className=\"font-medium mb-2\">Errors:</p>\n              <div className=\"bg-white rounded border max-h-40 overflow-y-auto\">\n                <ul className=\"text-xs space-y-1 p-3\">\n                  {migrationStatus.result.errors.map((error, index) => (\n                    <li key={index} className=\"text-red-600\">\n                      {index + 1}. {error}\n                    </li>\n                  ))}\n                </ul>\n              </div>\n            </div>\n          )}\n\n          {migrationStatus.result.details.length > 0 && (\n            <div className=\"mt-4\">\n              <p className=\"font-medium mb-2\">Details:</p>\n              <div className=\"bg-white rounded border max-h-60 overflow-y-auto\">\n                <table className=\"min-w-full text-xs\">\n                  <thead className=\"bg-gray-50\">\n                    <tr>\n                      <th className=\"px-3 py-2 text-left\">Record ID</th>\n                      <th className=\"px-3 py-2 text-left\">Status</th>\n                      <th className=\"px-3 py-2 text-left\">Original URL</th>\n                      <th className=\"px-3 py-2 text-left\">New URL</th>\n                    </tr>\n                  </thead>\n                  <tbody className=\"divide-y divide-gray-200\">\n                    {migrationStatus.result.details.map((detail, index) => (\n                      <tr key={index}>\n                        <td className=\"px-3 py-2 font-mono\">{detail.recordId}</td>\n                        <td className=\"px-3 py-2\">\n                          <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${\n                            detail.status === 'migrated' ? 'bg-green-100 text-green-800' :\n                            detail.status === 'failed' ? 'bg-red-100 text-red-800' :\n                            'bg-yellow-100 text-yellow-800'\n                          }`}>\n                            {detail.status}\n                          </span>\n                        </td>\n                        <td className=\"px-3 py-2 max-w-xs truncate\" title={detail.originalUrl}>\n                          {detail.originalUrl}\n                        </td>\n                        <td className=\"px-3 py-2 max-w-xs truncate\" title={detail.newUrl}>\n                          {detail.newUrl || detail.error || '-'}\n                        </td>\n                      </tr>\n                    ))}\n                  </tbody>\n                </table>\n              </div>\n            </div>\n          )}\n        </div>\n      )}\n\n      {/* Instructions */}\n      <div className=\"bg-gray-50 border border-gray-200 rounded-lg p-6\">\n        <h3 className=\"text-lg font-medium text-gray-900 mb-2\">Command Line Usage</h3>\n        <div className=\"text-gray-700 text-sm space-y-2\">\n          <p>You can also run photo migrations from the command line:</p>\n          <div className=\"bg-gray-800 text-green-400 p-3 rounded font-mono text-xs\">\n            <p># Migrate profile photos</p>\n            <p>npm run migrate-photos</p>\n            <p></p>\n            <p># Migrate disc photos (when available)</p>\n            <p>npm run migrate-disc-photos</p>\n          </div>\n          <p><strong>Note:</strong> Command line migration may be faster for large batches.</p>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default PhotoMigrationManager;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,aAAa,EAAEC,iBAAiB,QAAoD,uBAAuB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAQrH,MAAMC,qBAA+B,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC5C,MAAM,CAACC,eAAe,EAAEC,kBAAkB,CAAC,GAAGR,QAAQ,CAAkB;IACtES,SAAS,EAAE,KAAK;IAChBC,IAAI,EAAE,IAAI;IACVC,MAAM,EAAE;EACV,CAAC,CAAC;EAEF,MAAMC,eAAe,GAAG,MAAAA,CAAOF,IAA0B,EAAEG,MAA4B,KAAK;IAC1FL,kBAAkB,CAAC;MACjBC,SAAS,EAAE,IAAI;MACfC,IAAI;MACJC,MAAM,EAAE;IACV,CAAC,CAAC;IAEF,IAAI;MACF,MAAMA,MAAM,GAAG,MAAMV,aAAa,CAACY,MAAM,CAAC;MAC1CL,kBAAkB,CAAC;QACjBC,SAAS,EAAE,KAAK;QAChBC,IAAI;QACJC;MACF,CAAC,CAAC;IACJ,CAAC,CAAC,OAAOG,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,mBAAmB,EAAEA,KAAK,CAAC;MACzCN,kBAAkB,CAAC;QACjBC,SAAS,EAAE,KAAK;QAChBC,IAAI;QACJC,MAAM,EAAE;UACNK,OAAO,EAAE,KAAK;UACdC,SAAS,EAAE,CAAC;UACZC,QAAQ,EAAE,CAAC;UACXC,MAAM,EAAE,CAAC;UACTC,OAAO,EAAE,CAAC;UACVC,MAAM,EAAE,CAACP,KAAK,YAAYQ,KAAK,GAAGR,KAAK,CAACS,OAAO,GAAG,eAAe,CAAC;UAClEC,OAAO,EAAE;QACX;MACF,CAAC,CAAC;IACJ;EACF,CAAC;EAED,MAAMC,sBAAsB,GAAGA,CAAA,KAAM;IACnCb,eAAe,CAAC,UAAU,EAAEV,iBAAiB,CAACwB,eAAe,CAAC;EAChE,CAAC;EAED,MAAMC,mBAAmB,GAAGA,CAAA,KAAM;IAChCf,eAAe,CAAC,OAAO,EAAEV,iBAAiB,CAAC0B,WAAW,CAAC;EACzD,CAAC;EAED,oBACExB,OAAA;IAAKyB,SAAS,EAAC,uBAAuB;IAAAC,QAAA,gBACpC1B,OAAA;MAAIyB,SAAS,EAAC,uCAAuC;MAAAC,QAAA,EAAC;IAAuB;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eAElF9B,OAAA;MAAKyB,SAAS,EAAC,uDAAuD;MAAAC,QAAA,gBACpE1B,OAAA;QAAIyB,SAAS,EAAC,wCAAwC;QAAAC,QAAA,EAAC;MAAqB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACjF9B,OAAA;QAAKyB,SAAS,EAAC,iCAAiC;QAAAC,QAAA,gBAC9C1B,OAAA;UAAA0B,QAAA,EAAG;QAA4H;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACnI9B,OAAA;UAAA0B,QAAA,gBAAG1B,OAAA;YAAA0B,QAAA,EAAQ;UAAS;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,qFAAiF;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eAClH9B,OAAA;UAAA0B,QAAA,gBAAG1B,OAAA;YAAA0B,QAAA,EAAQ;UAAQ;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,uFAAoE;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN9B,OAAA;MAAKyB,SAAS,EAAC,qCAAqC;MAAAC,QAAA,gBAClD1B,OAAA;QAAIyB,SAAS,EAAC,0CAA0C;QAAAC,QAAA,EAAC;MAAwB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACtF9B,OAAA;QAAGyB,SAAS,EAAC,oBAAoB;QAAAC,QAAA,EAAC;MAGlC;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,eAEJ9B,OAAA;QAAKyB,SAAS,EAAC,kCAAkC;QAAAC,QAAA,eAC/C1B,OAAA;UAAKyB,SAAS,EAAC,uBAAuB;UAAAC,QAAA,gBACpC1B,OAAA;YAAA0B,QAAA,gBAAG1B,OAAA;cAAA0B,QAAA,EAAQ;YAAO;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,yCAAqC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACpE9B,OAAA;YAAA0B,QAAA,gBAAG1B,OAAA;cAAA0B,QAAA,EAAQ;YAAY;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,yBAAqB;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACzD9B,OAAA;YAAA0B,QAAA,gBAAG1B,OAAA;cAAA0B,QAAA,EAAQ;YAAS;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,kBAAc;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5C;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEN9B,OAAA;QACE+B,OAAO,EAAEV,sBAAuB;QAChCW,QAAQ,EAAE7B,eAAe,CAACE,SAAU;QACpCoB,SAAS,EAAC,gGAAgG;QAAAC,QAAA,EAEzGvB,eAAe,CAACE,SAAS,IAAIF,eAAe,CAACG,IAAI,KAAK,UAAU,GAC7D,6BAA6B,GAC7B;MAAwB;QAAAqB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAEtB,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,eAGN9B,OAAA;MAAKyB,SAAS,EAAC,qCAAqC;MAAAC,QAAA,gBAClD1B,OAAA;QAAIyB,SAAS,EAAC,0CAA0C;QAAAC,QAAA,EAAC;MAAoB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAClF9B,OAAA;QAAGyB,SAAS,EAAC,oBAAoB;QAAAC,QAAA,EAAC;MAGlC;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,eAEJ9B,OAAA;QAAKyB,SAAS,EAAC,kCAAkC;QAAAC,QAAA,eAC/C1B,OAAA;UAAKyB,SAAS,EAAC,uBAAuB;UAAAC,QAAA,gBACpC1B,OAAA;YAAA0B,QAAA,gBAAG1B,OAAA;cAAA0B,QAAA,EAAQ;YAAO;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,4DAAwD;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACvF9B,OAAA;YAAA0B,QAAA,gBAAG1B,OAAA;cAAA0B,QAAA,EAAQ;YAAY;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,6BAAyB;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAC7D9B,OAAA;YAAA0B,QAAA,gBAAG1B,OAAA;cAAA0B,QAAA,EAAQ;YAAS;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,mBAAe;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7C;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEN9B,OAAA;QACE+B,OAAO,EAAER,mBAAoB;QAC7BS,QAAQ,EAAE7B,eAAe,CAACE,SAAU;QACpCoB,SAAS,EAAC,mGAAmG;QAAAC,QAAA,EAE5GvB,eAAe,CAACE,SAAS,IAAIF,eAAe,CAACG,IAAI,KAAK,OAAO,GAC1D,0BAA0B,GAC1B;MAAqB;QAAAqB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAEnB,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,EAGL3B,eAAe,CAACI,MAAM,iBACrBP,OAAA;MAAKyB,SAAS,EAAE,uBACdtB,eAAe,CAACI,MAAM,CAACK,OAAO,GAAG,qCAAqC,GAAG,iCAAiC,EACzG;MAAAc,QAAA,gBACD1B,OAAA;QAAIyB,SAAS,EAAE,oBACbtB,eAAe,CAACI,MAAM,CAACK,OAAO,GAAG,gBAAgB,GAAG,cAAc,EACjE;QAAAc,QAAA,GACAvB,eAAe,CAACG,IAAI,KAAK,UAAU,GAAG,eAAe,GAAG,YAAY,EAAC,aAAW,EAACH,eAAe,CAACI,MAAM,CAACK,OAAO,GAAG,WAAW,GAAG,QAAQ;MAAA;QAAAe,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvI,CAAC,eAEL9B,OAAA;QAAKyB,SAAS,EAAE,sDACdtB,eAAe,CAACI,MAAM,CAACK,OAAO,GAAG,gBAAgB,GAAG,cAAc,EACjE;QAAAc,QAAA,gBACD1B,OAAA;UAAA0B,QAAA,gBACE1B,OAAA;YAAGyB,SAAS,EAAC,aAAa;YAAAC,QAAA,EAAC;UAAS;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACxC9B,OAAA;YAAGyB,SAAS,EAAC,SAAS;YAAAC,QAAA,EAAEvB,eAAe,CAACI,MAAM,CAACM;UAAS;YAAAc,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1D,CAAC,eACN9B,OAAA;UAAA0B,QAAA,gBACE1B,OAAA;YAAGyB,SAAS,EAAC,aAAa;YAAAC,QAAA,EAAC;UAAQ;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACvC9B,OAAA;YAAGyB,SAAS,EAAC,SAAS;YAAAC,QAAA,EAAEvB,eAAe,CAACI,MAAM,CAACO;UAAQ;YAAAa,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzD,CAAC,eACN9B,OAAA;UAAA0B,QAAA,gBACE1B,OAAA;YAAGyB,SAAS,EAAC,aAAa;YAAAC,QAAA,EAAC;UAAO;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACtC9B,OAAA;YAAGyB,SAAS,EAAC,SAAS;YAAAC,QAAA,EAAEvB,eAAe,CAACI,MAAM,CAACS;UAAO;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxD,CAAC,eACN9B,OAAA;UAAA0B,QAAA,gBACE1B,OAAA;YAAGyB,SAAS,EAAC,aAAa;YAAAC,QAAA,EAAC;UAAM;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACrC9B,OAAA;YAAGyB,SAAS,EAAC,SAAS;YAAAC,QAAA,EAAEvB,eAAe,CAACI,MAAM,CAACQ;UAAM;YAAAY,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,EAEL3B,eAAe,CAACI,MAAM,CAACU,MAAM,CAACgB,MAAM,GAAG,CAAC,iBACvCjC,OAAA;QAAKyB,SAAS,EAAC,MAAM;QAAAC,QAAA,gBACnB1B,OAAA;UAAGyB,SAAS,EAAC,kBAAkB;UAAAC,QAAA,EAAC;QAAO;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eAC3C9B,OAAA;UAAKyB,SAAS,EAAC,kDAAkD;UAAAC,QAAA,eAC/D1B,OAAA;YAAIyB,SAAS,EAAC,uBAAuB;YAAAC,QAAA,EAClCvB,eAAe,CAACI,MAAM,CAACU,MAAM,CAACiB,GAAG,CAAC,CAACxB,KAAK,EAAEyB,KAAK,kBAC9CnC,OAAA;cAAgByB,SAAS,EAAC,cAAc;cAAAC,QAAA,GACrCS,KAAK,GAAG,CAAC,EAAC,IAAE,EAACzB,KAAK;YAAA,GADZyB,KAAK;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEV,CACL;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,EAEA3B,eAAe,CAACI,MAAM,CAACa,OAAO,CAACa,MAAM,GAAG,CAAC,iBACxCjC,OAAA;QAAKyB,SAAS,EAAC,MAAM;QAAAC,QAAA,gBACnB1B,OAAA;UAAGyB,SAAS,EAAC,kBAAkB;UAAAC,QAAA,EAAC;QAAQ;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eAC5C9B,OAAA;UAAKyB,SAAS,EAAC,kDAAkD;UAAAC,QAAA,eAC/D1B,OAAA;YAAOyB,SAAS,EAAC,oBAAoB;YAAAC,QAAA,gBACnC1B,OAAA;cAAOyB,SAAS,EAAC,YAAY;cAAAC,QAAA,eAC3B1B,OAAA;gBAAA0B,QAAA,gBACE1B,OAAA;kBAAIyB,SAAS,EAAC,qBAAqB;kBAAAC,QAAA,EAAC;gBAAS;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAClD9B,OAAA;kBAAIyB,SAAS,EAAC,qBAAqB;kBAAAC,QAAA,EAAC;gBAAM;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAC/C9B,OAAA;kBAAIyB,SAAS,EAAC,qBAAqB;kBAAAC,QAAA,EAAC;gBAAY;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACrD9B,OAAA;kBAAIyB,SAAS,EAAC,qBAAqB;kBAAAC,QAAA,EAAC;gBAAO;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9C;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC,eACR9B,OAAA;cAAOyB,SAAS,EAAC,0BAA0B;cAAAC,QAAA,EACxCvB,eAAe,CAACI,MAAM,CAACa,OAAO,CAACc,GAAG,CAAC,CAACE,MAAM,EAAED,KAAK,kBAChDnC,OAAA;gBAAA0B,QAAA,gBACE1B,OAAA;kBAAIyB,SAAS,EAAC,qBAAqB;kBAAAC,QAAA,EAAEU,MAAM,CAACC;gBAAQ;kBAAAV,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eAC1D9B,OAAA;kBAAIyB,SAAS,EAAC,WAAW;kBAAAC,QAAA,eACvB1B,OAAA;oBAAMyB,SAAS,EAAE,4DACfW,MAAM,CAACE,MAAM,KAAK,UAAU,GAAG,6BAA6B,GAC5DF,MAAM,CAACE,MAAM,KAAK,QAAQ,GAAG,yBAAyB,GACtD,+BAA+B,EAC9B;oBAAAZ,QAAA,EACAU,MAAM,CAACE;kBAAM;oBAAAX,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACV;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CAAC,eACL9B,OAAA;kBAAIyB,SAAS,EAAC,6BAA6B;kBAACc,KAAK,EAAEH,MAAM,CAACI,WAAY;kBAAAd,QAAA,EACnEU,MAAM,CAACI;gBAAW;kBAAAb,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjB,CAAC,eACL9B,OAAA;kBAAIyB,SAAS,EAAC,6BAA6B;kBAACc,KAAK,EAAEH,MAAM,CAACK,MAAO;kBAAAf,QAAA,EAC9DU,MAAM,CAACK,MAAM,IAAIL,MAAM,CAAC1B,KAAK,IAAI;gBAAG;kBAAAiB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnC,CAAC;cAAA,GAhBEK,KAAK;gBAAAR,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAiBV,CACL;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CACN,eAGD9B,OAAA;MAAKyB,SAAS,EAAC,kDAAkD;MAAAC,QAAA,gBAC/D1B,OAAA;QAAIyB,SAAS,EAAC,wCAAwC;QAAAC,QAAA,EAAC;MAAkB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC9E9B,OAAA;QAAKyB,SAAS,EAAC,iCAAiC;QAAAC,QAAA,gBAC9C1B,OAAA;UAAA0B,QAAA,EAAG;QAAwD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eAC/D9B,OAAA;UAAKyB,SAAS,EAAC,0DAA0D;UAAAC,QAAA,gBACvE1B,OAAA;YAAA0B,QAAA,EAAG;UAAwB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAC/B9B,OAAA;YAAA0B,QAAA,EAAG;UAAsB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAC7B9B,OAAA;YAAA2B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACP9B,OAAA;YAAA0B,QAAA,EAAG;UAAsC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAC7C9B,OAAA;YAAA0B,QAAA,EAAG;UAA2B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/B,CAAC,eACN9B,OAAA;UAAA0B,QAAA,gBAAG1B,OAAA;YAAA0B,QAAA,EAAQ;UAAK;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,4DAAwD;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC5B,EAAA,CA9NID,qBAA+B;AAAAyC,EAAA,GAA/BzC,qBAA+B;AAgOrC,eAAeA,qBAAqB;AAAC,IAAAyC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}