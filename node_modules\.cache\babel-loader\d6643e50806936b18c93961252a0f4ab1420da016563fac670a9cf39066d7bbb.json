{"ast": null, "code": "var __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport { API_VERSIONS, API_VERSION_HEADER_NAME } from './constants';\nimport { expiresAt, looksLikeFetchResponse, parseResponseAPIVersion } from './helpers';\nimport { AuthApiError, AuthRetryableFetchError, AuthWeakPasswordError, AuthUnknownError, AuthSessionMissingError } from './errors';\nconst _getErrorMessage = err => err.msg || err.message || err.error_description || err.error || JSON.stringify(err);\nconst NETWORK_ERROR_CODES = [502, 503, 504];\nexport async function handleError(error) {\n  var _a;\n  if (!looksLikeFetchResponse(error)) {\n    throw new AuthRetryableFetchError(_getErrorMessage(error), 0);\n  }\n  if (NETWORK_ERROR_CODES.includes(error.status)) {\n    // status in 500...599 range - server had an error, request might be retryed.\n    throw new AuthRetryableFetchError(_getErrorMessage(error), error.status);\n  }\n  let data;\n  try {\n    data = await error.json();\n  } catch (e) {\n    throw new AuthUnknownError(_getErrorMessage(e), e);\n  }\n  let errorCode = undefined;\n  const responseAPIVersion = parseResponseAPIVersion(error);\n  if (responseAPIVersion && responseAPIVersion.getTime() >= API_VERSIONS['2024-01-01'].timestamp && typeof data === 'object' && data && typeof data.code === 'string') {\n    errorCode = data.code;\n  } else if (typeof data === 'object' && data && typeof data.error_code === 'string') {\n    errorCode = data.error_code;\n  }\n  if (!errorCode) {\n    // Legacy support for weak password errors, when there were no error codes\n    if (typeof data === 'object' && data && typeof data.weak_password === 'object' && data.weak_password && Array.isArray(data.weak_password.reasons) && data.weak_password.reasons.length && data.weak_password.reasons.reduce((a, i) => a && typeof i === 'string', true)) {\n      throw new AuthWeakPasswordError(_getErrorMessage(data), error.status, data.weak_password.reasons);\n    }\n  } else if (errorCode === 'weak_password') {\n    throw new AuthWeakPasswordError(_getErrorMessage(data), error.status, ((_a = data.weak_password) === null || _a === void 0 ? void 0 : _a.reasons) || []);\n  } else if (errorCode === 'session_not_found') {\n    // The `session_id` inside the JWT does not correspond to a row in the\n    // `sessions` table. This usually means the user has signed out, has been\n    // deleted, or their session has somehow been terminated.\n    throw new AuthSessionMissingError();\n  }\n  throw new AuthApiError(_getErrorMessage(data), error.status || 500, errorCode);\n}\nconst _getRequestParams = (method, options, parameters, body) => {\n  const params = {\n    method,\n    headers: (options === null || options === void 0 ? void 0 : options.headers) || {}\n  };\n  if (method === 'GET') {\n    return params;\n  }\n  params.headers = Object.assign({\n    'Content-Type': 'application/json;charset=UTF-8'\n  }, options === null || options === void 0 ? void 0 : options.headers);\n  params.body = JSON.stringify(body);\n  return Object.assign(Object.assign({}, params), parameters);\n};\nexport async function _request(fetcher, method, url, options) {\n  var _a;\n  const headers = Object.assign({}, options === null || options === void 0 ? void 0 : options.headers);\n  if (!headers[API_VERSION_HEADER_NAME]) {\n    headers[API_VERSION_HEADER_NAME] = API_VERSIONS['2024-01-01'].name;\n  }\n  if (options === null || options === void 0 ? void 0 : options.jwt) {\n    headers['Authorization'] = `Bearer ${options.jwt}`;\n  }\n  const qs = (_a = options === null || options === void 0 ? void 0 : options.query) !== null && _a !== void 0 ? _a : {};\n  if (options === null || options === void 0 ? void 0 : options.redirectTo) {\n    qs['redirect_to'] = options.redirectTo;\n  }\n  const queryString = Object.keys(qs).length ? '?' + new URLSearchParams(qs).toString() : '';\n  const data = await _handleRequest(fetcher, method, url + queryString, {\n    headers,\n    noResolveJson: options === null || options === void 0 ? void 0 : options.noResolveJson\n  }, {}, options === null || options === void 0 ? void 0 : options.body);\n  return (options === null || options === void 0 ? void 0 : options.xform) ? options === null || options === void 0 ? void 0 : options.xform(data) : {\n    data: Object.assign({}, data),\n    error: null\n  };\n}\nasync function _handleRequest(fetcher, method, url, options, parameters, body) {\n  const requestParams = _getRequestParams(method, options, parameters, body);\n  let result;\n  try {\n    result = await fetcher(url, Object.assign({}, requestParams));\n  } catch (e) {\n    console.error(e);\n    // fetch failed, likely due to a network or CORS error\n    throw new AuthRetryableFetchError(_getErrorMessage(e), 0);\n  }\n  if (!result.ok) {\n    await handleError(result);\n  }\n  if (options === null || options === void 0 ? void 0 : options.noResolveJson) {\n    return result;\n  }\n  try {\n    return await result.json();\n  } catch (e) {\n    await handleError(e);\n  }\n}\nexport function _sessionResponse(data) {\n  var _a;\n  let session = null;\n  if (hasSession(data)) {\n    session = Object.assign({}, data);\n    if (!data.expires_at) {\n      session.expires_at = expiresAt(data.expires_in);\n    }\n  }\n  const user = (_a = data.user) !== null && _a !== void 0 ? _a : data;\n  return {\n    data: {\n      session,\n      user\n    },\n    error: null\n  };\n}\nexport function _sessionResponsePassword(data) {\n  const response = _sessionResponse(data);\n  if (!response.error && data.weak_password && typeof data.weak_password === 'object' && Array.isArray(data.weak_password.reasons) && data.weak_password.reasons.length && data.weak_password.message && typeof data.weak_password.message === 'string' && data.weak_password.reasons.reduce((a, i) => a && typeof i === 'string', true)) {\n    response.data.weak_password = data.weak_password;\n  }\n  return response;\n}\nexport function _userResponse(data) {\n  var _a;\n  const user = (_a = data.user) !== null && _a !== void 0 ? _a : data;\n  return {\n    data: {\n      user\n    },\n    error: null\n  };\n}\nexport function _ssoResponse(data) {\n  return {\n    data,\n    error: null\n  };\n}\nexport function _generateLinkResponse(data) {\n  const {\n      action_link,\n      email_otp,\n      hashed_token,\n      redirect_to,\n      verification_type\n    } = data,\n    rest = __rest(data, [\"action_link\", \"email_otp\", \"hashed_token\", \"redirect_to\", \"verification_type\"]);\n  const properties = {\n    action_link,\n    email_otp,\n    hashed_token,\n    redirect_to,\n    verification_type\n  };\n  const user = Object.assign({}, rest);\n  return {\n    data: {\n      properties,\n      user\n    },\n    error: null\n  };\n}\nexport function _noResolveJsonResponse(data) {\n  return data;\n}\n/**\n * hasSession checks if the response object contains a valid session\n * @param data A response object\n * @returns true if a session is in the response\n */\nfunction hasSession(data) {\n  return data.access_token && data.refresh_token && data.expires_in;\n}", "map": {"version": 3, "names": ["API_VERSIONS", "API_VERSION_HEADER_NAME", "expiresAt", "looksLikeFetchResponse", "parseResponseAPIVersion", "AuthApiError", "AuthRetryableFetchError", "AuthWeakPasswordError", "AuthUnknownError", "AuthSessionMissingError", "_getErrorMessage", "err", "msg", "message", "error_description", "error", "JSON", "stringify", "NETWORK_ERROR_CODES", "handleError", "includes", "status", "data", "json", "e", "errorCode", "undefined", "responseAPIVersion", "getTime", "timestamp", "code", "error_code", "weak_password", "Array", "isArray", "reasons", "length", "reduce", "a", "i", "_a", "_getRequestParams", "method", "options", "parameters", "body", "params", "headers", "Object", "assign", "_request", "fetcher", "url", "name", "jwt", "qs", "query", "redirectTo", "queryString", "keys", "URLSearchParams", "toString", "_handleRequest", "noResolveJson", "xform", "requestParams", "result", "console", "ok", "_sessionResponse", "session", "hasSession", "expires_at", "expires_in", "user", "_sessionResponsePassword", "response", "_userResponse", "_ssoResponse", "_generateLinkResponse", "action_link", "email_otp", "hashed_token", "redirect_to", "verification_type", "rest", "__rest", "properties", "_noResolveJsonResponse", "access_token", "refresh_token"], "sources": ["C:\\Users\\<USER>\\lostandfound\\discfinder-app\\node_modules\\@supabase\\auth-js\\src\\lib\\fetch.ts"], "sourcesContent": ["import { API_VERSIONS, API_VERSION_HEADER_NAME } from './constants'\nimport { expiresAt, looksLikeFetchResponse, parseResponseAPIVersion } from './helpers'\nimport {\n  AuthResponse,\n  AuthResponsePassword,\n  SSOResponse,\n  GenerateLinkProperties,\n  GenerateLinkResponse,\n  User,\n  UserResponse,\n} from './types'\nimport {\n  AuthApiError,\n  AuthRetryableFetchError,\n  AuthWeakPasswordError,\n  AuthUnknownError,\n  AuthSessionMissingError,\n} from './errors'\n\nexport type Fetch = typeof fetch\n\nexport interface FetchOptions {\n  headers?: {\n    [key: string]: string\n  }\n  noResolveJson?: boolean\n}\n\nexport interface FetchParameters {\n  signal?: AbortSignal\n}\n\nexport type RequestMethodType = 'GET' | 'POST' | 'PUT' | 'DELETE'\n\nconst _getErrorMessage = (err: any): string =>\n  err.msg || err.message || err.error_description || err.error || JSON.stringify(err)\n\nconst NETWORK_ERROR_CODES = [502, 503, 504]\n\nexport async function handleError(error: unknown) {\n  if (!looksLikeFetchResponse(error)) {\n    throw new AuthRetryableFetchError(_getErrorMessage(error), 0)\n  }\n\n  if (NETWORK_ERROR_CODES.includes(error.status)) {\n    // status in 500...599 range - server had an error, request might be retryed.\n    throw new AuthRetryableFetchError(_getErrorMessage(error), error.status)\n  }\n\n  let data: any\n  try {\n    data = await error.json()\n  } catch (e: any) {\n    throw new AuthUnknownError(_getErrorMessage(e), e)\n  }\n\n  let errorCode: string | undefined = undefined\n\n  const responseAPIVersion = parseResponseAPIVersion(error)\n  if (\n    responseAPIVersion &&\n    responseAPIVersion.getTime() >= API_VERSIONS['2024-01-01'].timestamp &&\n    typeof data === 'object' &&\n    data &&\n    typeof data.code === 'string'\n  ) {\n    errorCode = data.code\n  } else if (typeof data === 'object' && data && typeof data.error_code === 'string') {\n    errorCode = data.error_code\n  }\n\n  if (!errorCode) {\n    // Legacy support for weak password errors, when there were no error codes\n    if (\n      typeof data === 'object' &&\n      data &&\n      typeof data.weak_password === 'object' &&\n      data.weak_password &&\n      Array.isArray(data.weak_password.reasons) &&\n      data.weak_password.reasons.length &&\n      data.weak_password.reasons.reduce((a: boolean, i: any) => a && typeof i === 'string', true)\n    ) {\n      throw new AuthWeakPasswordError(\n        _getErrorMessage(data),\n        error.status,\n        data.weak_password.reasons\n      )\n    }\n  } else if (errorCode === 'weak_password') {\n    throw new AuthWeakPasswordError(\n      _getErrorMessage(data),\n      error.status,\n      data.weak_password?.reasons || []\n    )\n  } else if (errorCode === 'session_not_found') {\n    // The `session_id` inside the JWT does not correspond to a row in the\n    // `sessions` table. This usually means the user has signed out, has been\n    // deleted, or their session has somehow been terminated.\n    throw new AuthSessionMissingError()\n  }\n\n  throw new AuthApiError(_getErrorMessage(data), error.status || 500, errorCode)\n}\n\nconst _getRequestParams = (\n  method: RequestMethodType,\n  options?: FetchOptions,\n  parameters?: FetchParameters,\n  body?: object\n) => {\n  const params: { [k: string]: any } = { method, headers: options?.headers || {} }\n\n  if (method === 'GET') {\n    return params\n  }\n\n  params.headers = { 'Content-Type': 'application/json;charset=UTF-8', ...options?.headers }\n  params.body = JSON.stringify(body)\n  return { ...params, ...parameters }\n}\n\ninterface GotrueRequestOptions extends FetchOptions {\n  jwt?: string\n  redirectTo?: string\n  body?: object\n  query?: { [key: string]: string }\n  /**\n   * Function that transforms api response from gotrue into a desirable / standardised format\n   */\n  xform?: (data: any) => any\n}\n\nexport async function _request(\n  fetcher: Fetch,\n  method: RequestMethodType,\n  url: string,\n  options?: GotrueRequestOptions\n) {\n  const headers = {\n    ...options?.headers,\n  }\n\n  if (!headers[API_VERSION_HEADER_NAME]) {\n    headers[API_VERSION_HEADER_NAME] = API_VERSIONS['2024-01-01'].name\n  }\n\n  if (options?.jwt) {\n    headers['Authorization'] = `Bearer ${options.jwt}`\n  }\n\n  const qs = options?.query ?? {}\n  if (options?.redirectTo) {\n    qs['redirect_to'] = options.redirectTo\n  }\n\n  const queryString = Object.keys(qs).length ? '?' + new URLSearchParams(qs).toString() : ''\n  const data = await _handleRequest(\n    fetcher,\n    method,\n    url + queryString,\n    {\n      headers,\n      noResolveJson: options?.noResolveJson,\n    },\n    {},\n    options?.body\n  )\n  return options?.xform ? options?.xform(data) : { data: { ...data }, error: null }\n}\n\nasync function _handleRequest(\n  fetcher: Fetch,\n  method: RequestMethodType,\n  url: string,\n  options?: FetchOptions,\n  parameters?: FetchParameters,\n  body?: object\n): Promise<any> {\n  const requestParams = _getRequestParams(method, options, parameters, body)\n\n  let result: any\n\n  try {\n    result = await fetcher(url, {\n      ...requestParams,\n    })\n  } catch (e) {\n    console.error(e)\n\n    // fetch failed, likely due to a network or CORS error\n    throw new AuthRetryableFetchError(_getErrorMessage(e), 0)\n  }\n\n  if (!result.ok) {\n    await handleError(result)\n  }\n\n  if (options?.noResolveJson) {\n    return result\n  }\n\n  try {\n    return await result.json()\n  } catch (e: any) {\n    await handleError(e)\n  }\n}\n\nexport function _sessionResponse(data: any): AuthResponse {\n  let session = null\n  if (hasSession(data)) {\n    session = { ...data }\n\n    if (!data.expires_at) {\n      session.expires_at = expiresAt(data.expires_in)\n    }\n  }\n\n  const user: User = data.user ?? (data as User)\n  return { data: { session, user }, error: null }\n}\n\nexport function _sessionResponsePassword(data: any): AuthResponsePassword {\n  const response = _sessionResponse(data) as AuthResponsePassword\n\n  if (\n    !response.error &&\n    data.weak_password &&\n    typeof data.weak_password === 'object' &&\n    Array.isArray(data.weak_password.reasons) &&\n    data.weak_password.reasons.length &&\n    data.weak_password.message &&\n    typeof data.weak_password.message === 'string' &&\n    data.weak_password.reasons.reduce((a: boolean, i: any) => a && typeof i === 'string', true)\n  ) {\n    response.data.weak_password = data.weak_password\n  }\n\n  return response\n}\n\nexport function _userResponse(data: any): UserResponse {\n  const user: User = data.user ?? (data as User)\n  return { data: { user }, error: null }\n}\n\nexport function _ssoResponse(data: any): SSOResponse {\n  return { data, error: null }\n}\n\nexport function _generateLinkResponse(data: any): GenerateLinkResponse {\n  const { action_link, email_otp, hashed_token, redirect_to, verification_type, ...rest } = data\n\n  const properties: GenerateLinkProperties = {\n    action_link,\n    email_otp,\n    hashed_token,\n    redirect_to,\n    verification_type,\n  }\n\n  const user: User = { ...rest }\n  return {\n    data: {\n      properties,\n      user,\n    },\n    error: null,\n  }\n}\n\nexport function _noResolveJsonResponse(data: any): Response {\n  return data\n}\n\n/**\n * hasSession checks if the response object contains a valid session\n * @param data A response object\n * @returns true if a session is in the response\n */\nfunction hasSession(data: any): boolean {\n  return data.access_token && data.refresh_token && data.expires_in\n}\n"], "mappings": ";;;;;;;;AAAA,SAASA,YAAY,EAAEC,uBAAuB,QAAQ,aAAa;AACnE,SAASC,SAAS,EAAEC,sBAAsB,EAAEC,uBAAuB,QAAQ,WAAW;AAUtF,SACEC,YAAY,EACZC,uBAAuB,EACvBC,qBAAqB,EACrBC,gBAAgB,EAChBC,uBAAuB,QAClB,UAAU;AAiBjB,MAAMC,gBAAgB,GAAIC,GAAQ,IAChCA,GAAG,CAACC,GAAG,IAAID,GAAG,CAACE,OAAO,IAAIF,GAAG,CAACG,iBAAiB,IAAIH,GAAG,CAACI,KAAK,IAAIC,IAAI,CAACC,SAAS,CAACN,GAAG,CAAC;AAErF,MAAMO,mBAAmB,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;AAE3C,OAAO,eAAeC,WAAWA,CAACJ,KAAc;;EAC9C,IAAI,CAACZ,sBAAsB,CAACY,KAAK,CAAC,EAAE;IAClC,MAAM,IAAIT,uBAAuB,CAACI,gBAAgB,CAACK,KAAK,CAAC,EAAE,CAAC,CAAC;;EAG/D,IAAIG,mBAAmB,CAACE,QAAQ,CAACL,KAAK,CAACM,MAAM,CAAC,EAAE;IAC9C;IACA,MAAM,IAAIf,uBAAuB,CAACI,gBAAgB,CAACK,KAAK,CAAC,EAAEA,KAAK,CAACM,MAAM,CAAC;;EAG1E,IAAIC,IAAS;EACb,IAAI;IACFA,IAAI,GAAG,MAAMP,KAAK,CAACQ,IAAI,EAAE;GAC1B,CAAC,OAAOC,CAAM,EAAE;IACf,MAAM,IAAIhB,gBAAgB,CAACE,gBAAgB,CAACc,CAAC,CAAC,EAAEA,CAAC,CAAC;;EAGpD,IAAIC,SAAS,GAAuBC,SAAS;EAE7C,MAAMC,kBAAkB,GAAGvB,uBAAuB,CAACW,KAAK,CAAC;EACzD,IACEY,kBAAkB,IAClBA,kBAAkB,CAACC,OAAO,EAAE,IAAI5B,YAAY,CAAC,YAAY,CAAC,CAAC6B,SAAS,IACpE,OAAOP,IAAI,KAAK,QAAQ,IACxBA,IAAI,IACJ,OAAOA,IAAI,CAACQ,IAAI,KAAK,QAAQ,EAC7B;IACAL,SAAS,GAAGH,IAAI,CAACQ,IAAI;GACtB,MAAM,IAAI,OAAOR,IAAI,KAAK,QAAQ,IAAIA,IAAI,IAAI,OAAOA,IAAI,CAACS,UAAU,KAAK,QAAQ,EAAE;IAClFN,SAAS,GAAGH,IAAI,CAACS,UAAU;;EAG7B,IAAI,CAACN,SAAS,EAAE;IACd;IACA,IACE,OAAOH,IAAI,KAAK,QAAQ,IACxBA,IAAI,IACJ,OAAOA,IAAI,CAACU,aAAa,KAAK,QAAQ,IACtCV,IAAI,CAACU,aAAa,IAClBC,KAAK,CAACC,OAAO,CAACZ,IAAI,CAACU,aAAa,CAACG,OAAO,CAAC,IACzCb,IAAI,CAACU,aAAa,CAACG,OAAO,CAACC,MAAM,IACjCd,IAAI,CAACU,aAAa,CAACG,OAAO,CAACE,MAAM,CAAC,CAACC,CAAU,EAAEC,CAAM,KAAKD,CAAC,IAAI,OAAOC,CAAC,KAAK,QAAQ,EAAE,IAAI,CAAC,EAC3F;MACA,MAAM,IAAIhC,qBAAqB,CAC7BG,gBAAgB,CAACY,IAAI,CAAC,EACtBP,KAAK,CAACM,MAAM,EACZC,IAAI,CAACU,aAAa,CAACG,OAAO,CAC3B;;GAEJ,MAAM,IAAIV,SAAS,KAAK,eAAe,EAAE;IACxC,MAAM,IAAIlB,qBAAqB,CAC7BG,gBAAgB,CAACY,IAAI,CAAC,EACtBP,KAAK,CAACM,MAAM,EACZ,EAAAmB,EAAA,GAAAlB,IAAI,CAACU,aAAa,cAAAQ,EAAA,uBAAAA,EAAA,CAAEL,OAAO,KAAI,EAAE,CAClC;GACF,MAAM,IAAIV,SAAS,KAAK,mBAAmB,EAAE;IAC5C;IACA;IACA;IACA,MAAM,IAAIhB,uBAAuB,EAAE;;EAGrC,MAAM,IAAIJ,YAAY,CAACK,gBAAgB,CAACY,IAAI,CAAC,EAAEP,KAAK,CAACM,MAAM,IAAI,GAAG,EAAEI,SAAS,CAAC;AAChF;AAEA,MAAMgB,iBAAiB,GAAGA,CACxBC,MAAyB,EACzBC,OAAsB,EACtBC,UAA4B,EAC5BC,IAAa,KACX;EACF,MAAMC,MAAM,GAAyB;IAAEJ,MAAM;IAAEK,OAAO,EAAE,CAAAJ,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEI,OAAO,KAAI;EAAE,CAAE;EAEhF,IAAIL,MAAM,KAAK,KAAK,EAAE;IACpB,OAAOI,MAAM;;EAGfA,MAAM,CAACC,OAAO,GAAAC,MAAA,CAAAC,MAAA;IAAK,cAAc,EAAE;EAAgC,GAAKN,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEI,OAAO,CAAE;EAC1FD,MAAM,CAACD,IAAI,GAAG7B,IAAI,CAACC,SAAS,CAAC4B,IAAI,CAAC;EAClC,OAAAG,MAAA,CAAAC,MAAA,CAAAD,MAAA,CAAAC,MAAA,KAAYH,MAAM,GAAKF,UAAU;AACnC,CAAC;AAaD,OAAO,eAAeM,QAAQA,CAC5BC,OAAc,EACdT,MAAyB,EACzBU,GAAW,EACXT,OAA8B;;EAE9B,MAAMI,OAAO,GAAAC,MAAA,CAAAC,MAAA,KACRN,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEI,OAAO,CACpB;EAED,IAAI,CAACA,OAAO,CAAC9C,uBAAuB,CAAC,EAAE;IACrC8C,OAAO,CAAC9C,uBAAuB,CAAC,GAAGD,YAAY,CAAC,YAAY,CAAC,CAACqD,IAAI;;EAGpE,IAAIV,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEW,GAAG,EAAE;IAChBP,OAAO,CAAC,eAAe,CAAC,GAAG,UAAUJ,OAAO,CAACW,GAAG,EAAE;;EAGpD,MAAMC,EAAE,GAAG,CAAAf,EAAA,GAAAG,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEa,KAAK,cAAAhB,EAAA,cAAAA,EAAA,GAAI,EAAE;EAC/B,IAAIG,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEc,UAAU,EAAE;IACvBF,EAAE,CAAC,aAAa,CAAC,GAAGZ,OAAO,CAACc,UAAU;;EAGxC,MAAMC,WAAW,GAAGV,MAAM,CAACW,IAAI,CAACJ,EAAE,CAAC,CAACnB,MAAM,GAAG,GAAG,GAAG,IAAIwB,eAAe,CAACL,EAAE,CAAC,CAACM,QAAQ,EAAE,GAAG,EAAE;EAC1F,MAAMvC,IAAI,GAAG,MAAMwC,cAAc,CAC/BX,OAAO,EACPT,MAAM,EACNU,GAAG,GAAGM,WAAW,EACjB;IACEX,OAAO;IACPgB,aAAa,EAAEpB,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEoB;GACzB,EACD,EAAE,EACFpB,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEE,IAAI,CACd;EACD,OAAO,CAAAF,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEqB,KAAK,IAAGrB,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEqB,KAAK,CAAC1C,IAAI,CAAC,GAAG;IAAEA,IAAI,EAAA0B,MAAA,CAAAC,MAAA,KAAO3B,IAAI,CAAE;IAAEP,KAAK,EAAE;EAAI,CAAE;AACnF;AAEA,eAAe+C,cAAcA,CAC3BX,OAAc,EACdT,MAAyB,EACzBU,GAAW,EACXT,OAAsB,EACtBC,UAA4B,EAC5BC,IAAa;EAEb,MAAMoB,aAAa,GAAGxB,iBAAiB,CAACC,MAAM,EAAEC,OAAO,EAAEC,UAAU,EAAEC,IAAI,CAAC;EAE1E,IAAIqB,MAAW;EAEf,IAAI;IACFA,MAAM,GAAG,MAAMf,OAAO,CAACC,GAAG,EAAAJ,MAAA,CAAAC,MAAA,KACrBgB,aAAa,EAChB;GACH,CAAC,OAAOzC,CAAC,EAAE;IACV2C,OAAO,CAACpD,KAAK,CAACS,CAAC,CAAC;IAEhB;IACA,MAAM,IAAIlB,uBAAuB,CAACI,gBAAgB,CAACc,CAAC,CAAC,EAAE,CAAC,CAAC;;EAG3D,IAAI,CAAC0C,MAAM,CAACE,EAAE,EAAE;IACd,MAAMjD,WAAW,CAAC+C,MAAM,CAAC;;EAG3B,IAAIvB,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEoB,aAAa,EAAE;IAC1B,OAAOG,MAAM;;EAGf,IAAI;IACF,OAAO,MAAMA,MAAM,CAAC3C,IAAI,EAAE;GAC3B,CAAC,OAAOC,CAAM,EAAE;IACf,MAAML,WAAW,CAACK,CAAC,CAAC;;AAExB;AAEA,OAAM,SAAU6C,gBAAgBA,CAAC/C,IAAS;;EACxC,IAAIgD,OAAO,GAAG,IAAI;EAClB,IAAIC,UAAU,CAACjD,IAAI,CAAC,EAAE;IACpBgD,OAAO,GAAAtB,MAAA,CAAAC,MAAA,KAAQ3B,IAAI,CAAE;IAErB,IAAI,CAACA,IAAI,CAACkD,UAAU,EAAE;MACpBF,OAAO,CAACE,UAAU,GAAGtE,SAAS,CAACoB,IAAI,CAACmD,UAAU,CAAC;;;EAInD,MAAMC,IAAI,GAAS,CAAAlC,EAAA,GAAAlB,IAAI,CAACoD,IAAI,cAAAlC,EAAA,cAAAA,EAAA,GAAKlB,IAAa;EAC9C,OAAO;IAAEA,IAAI,EAAE;MAAEgD,OAAO;MAAEI;IAAI,CAAE;IAAE3D,KAAK,EAAE;EAAI,CAAE;AACjD;AAEA,OAAM,SAAU4D,wBAAwBA,CAACrD,IAAS;EAChD,MAAMsD,QAAQ,GAAGP,gBAAgB,CAAC/C,IAAI,CAAyB;EAE/D,IACE,CAACsD,QAAQ,CAAC7D,KAAK,IACfO,IAAI,CAACU,aAAa,IAClB,OAAOV,IAAI,CAACU,aAAa,KAAK,QAAQ,IACtCC,KAAK,CAACC,OAAO,CAACZ,IAAI,CAACU,aAAa,CAACG,OAAO,CAAC,IACzCb,IAAI,CAACU,aAAa,CAACG,OAAO,CAACC,MAAM,IACjCd,IAAI,CAACU,aAAa,CAACnB,OAAO,IAC1B,OAAOS,IAAI,CAACU,aAAa,CAACnB,OAAO,KAAK,QAAQ,IAC9CS,IAAI,CAACU,aAAa,CAACG,OAAO,CAACE,MAAM,CAAC,CAACC,CAAU,EAAEC,CAAM,KAAKD,CAAC,IAAI,OAAOC,CAAC,KAAK,QAAQ,EAAE,IAAI,CAAC,EAC3F;IACAqC,QAAQ,CAACtD,IAAI,CAACU,aAAa,GAAGV,IAAI,CAACU,aAAa;;EAGlD,OAAO4C,QAAQ;AACjB;AAEA,OAAM,SAAUC,aAAaA,CAACvD,IAAS;;EACrC,MAAMoD,IAAI,GAAS,CAAAlC,EAAA,GAAAlB,IAAI,CAACoD,IAAI,cAAAlC,EAAA,cAAAA,EAAA,GAAKlB,IAAa;EAC9C,OAAO;IAAEA,IAAI,EAAE;MAAEoD;IAAI,CAAE;IAAE3D,KAAK,EAAE;EAAI,CAAE;AACxC;AAEA,OAAM,SAAU+D,YAAYA,CAACxD,IAAS;EACpC,OAAO;IAAEA,IAAI;IAAEP,KAAK,EAAE;EAAI,CAAE;AAC9B;AAEA,OAAM,SAAUgE,qBAAqBA,CAACzD,IAAS;EAC7C,MAAM;MAAE0D,WAAW;MAAEC,SAAS;MAAEC,YAAY;MAAEC,WAAW;MAAEC;IAAiB,IAAc9D,IAAI;IAAb+D,IAAI,GAAAC,MAAA,CAAKhE,IAAI,EAAxF,gFAAiF,CAAO;EAE9F,MAAMiE,UAAU,GAA2B;IACzCP,WAAW;IACXC,SAAS;IACTC,YAAY;IACZC,WAAW;IACXC;GACD;EAED,MAAMV,IAAI,GAAA1B,MAAA,CAAAC,MAAA,KAAcoC,IAAI,CAAE;EAC9B,OAAO;IACL/D,IAAI,EAAE;MACJiE,UAAU;MACVb;KACD;IACD3D,KAAK,EAAE;GACR;AACH;AAEA,OAAM,SAAUyE,sBAAsBA,CAAClE,IAAS;EAC9C,OAAOA,IAAI;AACb;AAEA;;;;;AAKA,SAASiD,UAAUA,CAACjD,IAAS;EAC3B,OAAOA,IAAI,CAACmE,YAAY,IAAInE,IAAI,CAACoE,aAAa,IAAIpE,IAAI,CAACmD,UAAU;AACnE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}