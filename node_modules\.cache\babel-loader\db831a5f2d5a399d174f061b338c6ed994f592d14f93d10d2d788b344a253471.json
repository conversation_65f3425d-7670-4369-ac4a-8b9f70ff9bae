{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\lostandfound\\\\discfinder-app\\\\src\\\\components\\\\ProfileImportManager.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { importSampleProfiles, getImportedProfilesNeedingSignup } from '../lib/profileImport';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ProfileImportManager = () => {\n  _s();\n  const [isImporting, setIsImporting] = useState(false);\n  const [importResult, setImportResult] = useState(null);\n  const [importedProfiles, setImportedProfiles] = useState([]);\n  const [isLoadingProfiles, setIsLoadingProfiles] = useState(false);\n  const handleImportSample = async () => {\n    setIsImporting(true);\n    setImportResult(null);\n    try {\n      const result = await importSampleProfiles();\n      setImportResult(result);\n\n      // Refresh the imported profiles list\n      if (result.success) {\n        await loadImportedProfiles();\n      }\n    } catch (error) {\n      console.error('Import failed:', error);\n      setImportResult({\n        success: false,\n        imported: 0,\n        failed: 1,\n        errors: [error instanceof Error ? error.message : 'Unknown error']\n      });\n    } finally {\n      setIsImporting(false);\n    }\n  };\n  const loadImportedProfiles = async () => {\n    setIsLoadingProfiles(true);\n    try {\n      const result = await getImportedProfilesNeedingSignup();\n      if (result.success) {\n        setImportedProfiles(result.profiles);\n      } else {\n        console.error('Failed to load imported profiles:', result.error);\n      }\n    } catch (error) {\n      console.error('Error loading imported profiles:', error);\n    } finally {\n      setIsLoadingProfiles(false);\n    }\n  };\n  React.useEffect(() => {\n    loadImportedProfiles();\n  }, []);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"max-w-4xl mx-auto p-6\",\n    children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n      className: \"text-3xl font-bold text-gray-900 mb-6\",\n      children: \"Profile Import Manager\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 74,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white shadow rounded-lg p-6 mb-6\",\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        className: \"text-xl font-semibold text-gray-900 mb-4\",\n        children: \"Import Profiles\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 78,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-gray-600 mb-4\",\n        children: \"Import user profiles from your previous Glide app. This will create profile records that can be linked when users sign up with matching email addresses.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 79,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: handleImportSample,\n        disabled: isImporting,\n        className: \"bg-blue-600 hover:bg-blue-700 disabled:bg-blue-300 text-white px-4 py-2 rounded-md font-medium\",\n        children: isImporting ? 'Importing...' : 'Import Sample Profiles'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 84,\n        columnNumber: 9\n      }, this), importResult && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: `mt-4 p-4 rounded-md ${importResult.success ? 'bg-green-50 border border-green-200' : 'bg-red-50 border border-red-200'}`,\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: `font-medium ${importResult.success ? 'text-green-800' : 'text-red-800'}`,\n          children: [\"Import \", importResult.success ? 'Completed' : 'Failed']\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 97,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: `mt-2 text-sm ${importResult.success ? 'text-green-700' : 'text-red-700'}`,\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            children: [\"Imported: \", importResult.imported]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 105,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: [\"Failed: \", importResult.failed]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 106,\n            columnNumber: 15\n          }, this), importResult.errors.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mt-2\",\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"font-medium\",\n              children: \"Errors:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 109,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n              className: \"list-disc list-inside\",\n              children: importResult.errors.map((error, index) => /*#__PURE__*/_jsxDEV(\"li\", {\n                children: error\n              }, index, false, {\n                fileName: _jsxFileName,\n                lineNumber: 112,\n                columnNumber: 23\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 110,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 108,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 102,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 94,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 77,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white shadow rounded-lg p-6\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex justify-between items-center mb-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-xl font-semibold text-gray-900\",\n          children: \"Imported Profiles\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 125,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: loadImportedProfiles,\n          disabled: isLoadingProfiles,\n          className: \"bg-gray-600 hover:bg-gray-700 disabled:bg-gray-300 text-white px-3 py-1 rounded-md text-sm\",\n          children: isLoadingProfiles ? 'Loading...' : 'Refresh'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 126,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 124,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-gray-600 mb-4\",\n        children: \"These profiles have been imported but the users haven't signed up yet. When they sign up with matching email addresses, their profiles will be automatically linked.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 135,\n        columnNumber: 9\n      }, this), importedProfiles.length === 0 ? /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-gray-500 italic\",\n        children: \"No imported profiles found.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 141,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"overflow-x-auto\",\n        children: /*#__PURE__*/_jsxDEV(\"table\", {\n          className: \"min-w-full divide-y divide-gray-200\",\n          children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n            className: \"bg-gray-50\",\n            children: /*#__PURE__*/_jsxDEV(\"tr\", {\n              children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                children: \"Name\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 147,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                children: \"Email\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 150,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                children: \"Role\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 153,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                children: \"PDGA #\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 156,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                children: \"Status\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 159,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 146,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 145,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n            className: \"bg-white divide-y divide-gray-200\",\n            children: importedProfiles.map(profile => /*#__PURE__*/_jsxDEV(\"tr\", {\n              children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900\",\n                children: profile.full_name || 'N/A'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 167,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n                children: profile.email\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 170,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n                children: /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: `inline-flex px-2 py-1 text-xs font-semibold rounded-full ${profile.role === 'admin' ? 'bg-purple-100 text-purple-800' : profile.role === 'rakerdiver' ? 'bg-blue-100 text-blue-800' : profile.role === 'user' ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'}`,\n                  children: profile.role || 'guest'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 174,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 173,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n                children: profile.pdga_number || 'N/A'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 183,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n                children: /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: `inline-flex px-2 py-1 text-xs font-semibold rounded-full ${profile.needs_signup ? 'bg-yellow-100 text-yellow-800' : 'bg-green-100 text-green-800'}`,\n                  children: profile.needs_signup ? 'Needs Signup' : 'Linked'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 187,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 186,\n                columnNumber: 21\n              }, this)]\n            }, profile.id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 166,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 164,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 144,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 143,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 123,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-blue-50 border border-blue-200 rounded-lg p-6 mt-6\",\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        className: \"text-lg font-medium text-blue-900 mb-2\",\n        children: \"Next Steps\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 203,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-blue-800 text-sm space-y-2\",\n        children: [/*#__PURE__*/_jsxDEV(\"p\", {\n          children: [\"1. \", /*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"Run the database migration:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 205,\n            columnNumber: 17\n          }, this), \" Execute the \", /*#__PURE__*/_jsxDEV(\"code\", {\n            children: \"ADD_PROFILE_IMPORT_FIELDS.sql\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 205,\n            columnNumber: 74\n          }, this), \" file in your Supabase SQL Editor.\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 205,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: [\"2. \", /*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"Import your data:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 206,\n            columnNumber: 17\n          }, this), \" Replace the sample import with your actual Glide API integration.\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 206,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: [\"3. \", /*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"Test the process:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 207,\n            columnNumber: 17\n          }, this), \" Have users sign up with imported email addresses to verify profile linking works.\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 207,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: [\"4. \", /*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"Handle avatars:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 208,\n            columnNumber: 17\n          }, this), \" Implement avatar migration from your previous app's photo URLs.\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 208,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 204,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 202,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 73,\n    columnNumber: 5\n  }, this);\n};\n_s(ProfileImportManager, \"gDiW9scTcQQq1gbn79LmgW6XcYY=\");\n_c = ProfileImportManager;\nexport default ProfileImportManager;\nvar _c;\n$RefreshReg$(_c, \"ProfileImportManager\");", "map": {"version": 3, "names": ["React", "useState", "importSampleProfiles", "getImportedProfilesNeedingSignup", "jsxDEV", "_jsxDEV", "ProfileImportManager", "_s", "isImporting", "setIsImporting", "importResult", "setImportResult", "importedProfiles", "setImportedProfiles", "isLoadingProfiles", "setIsLoadingProfiles", "handleImportSample", "result", "success", "loadImportedProfiles", "error", "console", "imported", "failed", "errors", "Error", "message", "profiles", "useEffect", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "disabled", "length", "map", "index", "profile", "full_name", "email", "role", "pdga_number", "needs_signup", "id", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/lostandfound/discfinder-app/src/components/ProfileImportManager.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { importSampleProfiles, getImportedProfilesNeedingSignup } from '../lib/profileImport';\n\ninterface ImportResult {\n  success: boolean;\n  imported: number;\n  failed: number;\n  errors: string[];\n}\n\ninterface ImportedProfile {\n  id: string;\n  email: string;\n  full_name?: string;\n  role?: string;\n  legacy_row_id?: string;\n  pdga_number?: number;\n  needs_signup: boolean;\n}\n\nconst ProfileImportManager: React.FC = () => {\n  const [isImporting, setIsImporting] = useState(false);\n  const [importResult, setImportResult] = useState<ImportResult | null>(null);\n  const [importedProfiles, setImportedProfiles] = useState<ImportedProfile[]>([]);\n  const [isLoadingProfiles, setIsLoadingProfiles] = useState(false);\n\n  const handleImportSample = async () => {\n    setIsImporting(true);\n    setImportResult(null);\n    \n    try {\n      const result = await importSampleProfiles();\n      setImportResult(result);\n      \n      // Refresh the imported profiles list\n      if (result.success) {\n        await loadImportedProfiles();\n      }\n    } catch (error) {\n      console.error('Import failed:', error);\n      setImportResult({\n        success: false,\n        imported: 0,\n        failed: 1,\n        errors: [error instanceof Error ? error.message : 'Unknown error']\n      });\n    } finally {\n      setIsImporting(false);\n    }\n  };\n\n  const loadImportedProfiles = async () => {\n    setIsLoadingProfiles(true);\n    try {\n      const result = await getImportedProfilesNeedingSignup();\n      if (result.success) {\n        setImportedProfiles(result.profiles);\n      } else {\n        console.error('Failed to load imported profiles:', result.error);\n      }\n    } catch (error) {\n      console.error('Error loading imported profiles:', error);\n    } finally {\n      setIsLoadingProfiles(false);\n    }\n  };\n\n  React.useEffect(() => {\n    loadImportedProfiles();\n  }, []);\n\n  return (\n    <div className=\"max-w-4xl mx-auto p-6\">\n      <h1 className=\"text-3xl font-bold text-gray-900 mb-6\">Profile Import Manager</h1>\n      \n      {/* Import Section */}\n      <div className=\"bg-white shadow rounded-lg p-6 mb-6\">\n        <h2 className=\"text-xl font-semibold text-gray-900 mb-4\">Import Profiles</h2>\n        <p className=\"text-gray-600 mb-4\">\n          Import user profiles from your previous Glide app. This will create profile records\n          that can be linked when users sign up with matching email addresses.\n        </p>\n        \n        <button\n          onClick={handleImportSample}\n          disabled={isImporting}\n          className=\"bg-blue-600 hover:bg-blue-700 disabled:bg-blue-300 text-white px-4 py-2 rounded-md font-medium\"\n        >\n          {isImporting ? 'Importing...' : 'Import Sample Profiles'}\n        </button>\n        \n        {/* Import Results */}\n        {importResult && (\n          <div className={`mt-4 p-4 rounded-md ${\n            importResult.success ? 'bg-green-50 border border-green-200' : 'bg-red-50 border border-red-200'\n          }`}>\n            <h3 className={`font-medium ${\n              importResult.success ? 'text-green-800' : 'text-red-800'\n            }`}>\n              Import {importResult.success ? 'Completed' : 'Failed'}\n            </h3>\n            <div className={`mt-2 text-sm ${\n              importResult.success ? 'text-green-700' : 'text-red-700'\n            }`}>\n              <p>Imported: {importResult.imported}</p>\n              <p>Failed: {importResult.failed}</p>\n              {importResult.errors.length > 0 && (\n                <div className=\"mt-2\">\n                  <p className=\"font-medium\">Errors:</p>\n                  <ul className=\"list-disc list-inside\">\n                    {importResult.errors.map((error, index) => (\n                      <li key={index}>{error}</li>\n                    ))}\n                  </ul>\n                </div>\n              )}\n            </div>\n          </div>\n        )}\n      </div>\n\n      {/* Imported Profiles Section */}\n      <div className=\"bg-white shadow rounded-lg p-6\">\n        <div className=\"flex justify-between items-center mb-4\">\n          <h2 className=\"text-xl font-semibold text-gray-900\">Imported Profiles</h2>\n          <button\n            onClick={loadImportedProfiles}\n            disabled={isLoadingProfiles}\n            className=\"bg-gray-600 hover:bg-gray-700 disabled:bg-gray-300 text-white px-3 py-1 rounded-md text-sm\"\n          >\n            {isLoadingProfiles ? 'Loading...' : 'Refresh'}\n          </button>\n        </div>\n        \n        <p className=\"text-gray-600 mb-4\">\n          These profiles have been imported but the users haven't signed up yet. \n          When they sign up with matching email addresses, their profiles will be automatically linked.\n        </p>\n\n        {importedProfiles.length === 0 ? (\n          <p className=\"text-gray-500 italic\">No imported profiles found.</p>\n        ) : (\n          <div className=\"overflow-x-auto\">\n            <table className=\"min-w-full divide-y divide-gray-200\">\n              <thead className=\"bg-gray-50\">\n                <tr>\n                  <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                    Name\n                  </th>\n                  <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                    Email\n                  </th>\n                  <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                    Role\n                  </th>\n                  <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                    PDGA #\n                  </th>\n                  <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                    Status\n                  </th>\n                </tr>\n              </thead>\n              <tbody className=\"bg-white divide-y divide-gray-200\">\n                {importedProfiles.map((profile) => (\n                  <tr key={profile.id}>\n                    <td className=\"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900\">\n                      {profile.full_name || 'N/A'}\n                    </td>\n                    <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">\n                      {profile.email}\n                    </td>\n                    <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">\n                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${\n                        profile.role === 'admin' ? 'bg-purple-100 text-purple-800' :\n                        profile.role === 'rakerdiver' ? 'bg-blue-100 text-blue-800' :\n                        profile.role === 'user' ? 'bg-green-100 text-green-800' :\n                        'bg-gray-100 text-gray-800'\n                      }`}>\n                        {profile.role || 'guest'}\n                      </span>\n                    </td>\n                    <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">\n                      {profile.pdga_number || 'N/A'}\n                    </td>\n                    <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">\n                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${\n                        profile.needs_signup ? 'bg-yellow-100 text-yellow-800' : 'bg-green-100 text-green-800'\n                      }`}>\n                        {profile.needs_signup ? 'Needs Signup' : 'Linked'}\n                      </span>\n                    </td>\n                  </tr>\n                ))}\n              </tbody>\n            </table>\n          </div>\n        )}\n      </div>\n\n      {/* Instructions */}\n      <div className=\"bg-blue-50 border border-blue-200 rounded-lg p-6 mt-6\">\n        <h3 className=\"text-lg font-medium text-blue-900 mb-2\">Next Steps</h3>\n        <div className=\"text-blue-800 text-sm space-y-2\">\n          <p>1. <strong>Run the database migration:</strong> Execute the <code>ADD_PROFILE_IMPORT_FIELDS.sql</code> file in your Supabase SQL Editor.</p>\n          <p>2. <strong>Import your data:</strong> Replace the sample import with your actual Glide API integration.</p>\n          <p>3. <strong>Test the process:</strong> Have users sign up with imported email addresses to verify profile linking works.</p>\n          <p>4. <strong>Handle avatars:</strong> Implement avatar migration from your previous app's photo URLs.</p>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default ProfileImportManager;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,oBAAoB,EAAEC,gCAAgC,QAAQ,sBAAsB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAmB9F,MAAMC,oBAA8B,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC3C,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGR,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACS,YAAY,EAAEC,eAAe,CAAC,GAAGV,QAAQ,CAAsB,IAAI,CAAC;EAC3E,MAAM,CAACW,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGZ,QAAQ,CAAoB,EAAE,CAAC;EAC/E,MAAM,CAACa,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGd,QAAQ,CAAC,KAAK,CAAC;EAEjE,MAAMe,kBAAkB,GAAG,MAAAA,CAAA,KAAY;IACrCP,cAAc,CAAC,IAAI,CAAC;IACpBE,eAAe,CAAC,IAAI,CAAC;IAErB,IAAI;MACF,MAAMM,MAAM,GAAG,MAAMf,oBAAoB,CAAC,CAAC;MAC3CS,eAAe,CAACM,MAAM,CAAC;;MAEvB;MACA,IAAIA,MAAM,CAACC,OAAO,EAAE;QAClB,MAAMC,oBAAoB,CAAC,CAAC;MAC9B;IACF,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,gBAAgB,EAAEA,KAAK,CAAC;MACtCT,eAAe,CAAC;QACdO,OAAO,EAAE,KAAK;QACdI,QAAQ,EAAE,CAAC;QACXC,MAAM,EAAE,CAAC;QACTC,MAAM,EAAE,CAACJ,KAAK,YAAYK,KAAK,GAAGL,KAAK,CAACM,OAAO,GAAG,eAAe;MACnE,CAAC,CAAC;IACJ,CAAC,SAAS;MACRjB,cAAc,CAAC,KAAK,CAAC;IACvB;EACF,CAAC;EAED,MAAMU,oBAAoB,GAAG,MAAAA,CAAA,KAAY;IACvCJ,oBAAoB,CAAC,IAAI,CAAC;IAC1B,IAAI;MACF,MAAME,MAAM,GAAG,MAAMd,gCAAgC,CAAC,CAAC;MACvD,IAAIc,MAAM,CAACC,OAAO,EAAE;QAClBL,mBAAmB,CAACI,MAAM,CAACU,QAAQ,CAAC;MACtC,CAAC,MAAM;QACLN,OAAO,CAACD,KAAK,CAAC,mCAAmC,EAAEH,MAAM,CAACG,KAAK,CAAC;MAClE;IACF,CAAC,CAAC,OAAOA,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;IAC1D,CAAC,SAAS;MACRL,oBAAoB,CAAC,KAAK,CAAC;IAC7B;EACF,CAAC;EAEDf,KAAK,CAAC4B,SAAS,CAAC,MAAM;IACpBT,oBAAoB,CAAC,CAAC;EACxB,CAAC,EAAE,EAAE,CAAC;EAEN,oBACEd,OAAA;IAAKwB,SAAS,EAAC,uBAAuB;IAAAC,QAAA,gBACpCzB,OAAA;MAAIwB,SAAS,EAAC,uCAAuC;MAAAC,QAAA,EAAC;IAAsB;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eAGjF7B,OAAA;MAAKwB,SAAS,EAAC,qCAAqC;MAAAC,QAAA,gBAClDzB,OAAA;QAAIwB,SAAS,EAAC,0CAA0C;QAAAC,QAAA,EAAC;MAAe;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC7E7B,OAAA;QAAGwB,SAAS,EAAC,oBAAoB;QAAAC,QAAA,EAAC;MAGlC;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,eAEJ7B,OAAA;QACE8B,OAAO,EAAEnB,kBAAmB;QAC5BoB,QAAQ,EAAE5B,WAAY;QACtBqB,SAAS,EAAC,gGAAgG;QAAAC,QAAA,EAEzGtB,WAAW,GAAG,cAAc,GAAG;MAAwB;QAAAuB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClD,CAAC,EAGRxB,YAAY,iBACXL,OAAA;QAAKwB,SAAS,EAAE,uBACdnB,YAAY,CAACQ,OAAO,GAAG,qCAAqC,GAAG,iCAAiC,EAC/F;QAAAY,QAAA,gBACDzB,OAAA;UAAIwB,SAAS,EAAE,eACbnB,YAAY,CAACQ,OAAO,GAAG,gBAAgB,GAAG,cAAc,EACvD;UAAAY,QAAA,GAAC,SACK,EAACpB,YAAY,CAACQ,OAAO,GAAG,WAAW,GAAG,QAAQ;QAAA;UAAAa,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnD,CAAC,eACL7B,OAAA;UAAKwB,SAAS,EAAE,gBACdnB,YAAY,CAACQ,OAAO,GAAG,gBAAgB,GAAG,cAAc,EACvD;UAAAY,QAAA,gBACDzB,OAAA;YAAAyB,QAAA,GAAG,YAAU,EAACpB,YAAY,CAACY,QAAQ;UAAA;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACxC7B,OAAA;YAAAyB,QAAA,GAAG,UAAQ,EAACpB,YAAY,CAACa,MAAM;UAAA;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,EACnCxB,YAAY,CAACc,MAAM,CAACa,MAAM,GAAG,CAAC,iBAC7BhC,OAAA;YAAKwB,SAAS,EAAC,MAAM;YAAAC,QAAA,gBACnBzB,OAAA;cAAGwB,SAAS,EAAC,aAAa;cAAAC,QAAA,EAAC;YAAO;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACtC7B,OAAA;cAAIwB,SAAS,EAAC,uBAAuB;cAAAC,QAAA,EAClCpB,YAAY,CAACc,MAAM,CAACc,GAAG,CAAC,CAAClB,KAAK,EAAEmB,KAAK,kBACpClC,OAAA;gBAAAyB,QAAA,EAAiBV;cAAK,GAAbmB,KAAK;gBAAAR,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAC5B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAGN7B,OAAA;MAAKwB,SAAS,EAAC,gCAAgC;MAAAC,QAAA,gBAC7CzB,OAAA;QAAKwB,SAAS,EAAC,wCAAwC;QAAAC,QAAA,gBACrDzB,OAAA;UAAIwB,SAAS,EAAC,qCAAqC;UAAAC,QAAA,EAAC;QAAiB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC1E7B,OAAA;UACE8B,OAAO,EAAEhB,oBAAqB;UAC9BiB,QAAQ,EAAEtB,iBAAkB;UAC5Be,SAAS,EAAC,4FAA4F;UAAAC,QAAA,EAErGhB,iBAAiB,GAAG,YAAY,GAAG;QAAS;UAAAiB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eAEN7B,OAAA;QAAGwB,SAAS,EAAC,oBAAoB;QAAAC,QAAA,EAAC;MAGlC;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,EAEHtB,gBAAgB,CAACyB,MAAM,KAAK,CAAC,gBAC5BhC,OAAA;QAAGwB,SAAS,EAAC,sBAAsB;QAAAC,QAAA,EAAC;MAA2B;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,gBAEnE7B,OAAA;QAAKwB,SAAS,EAAC,iBAAiB;QAAAC,QAAA,eAC9BzB,OAAA;UAAOwB,SAAS,EAAC,qCAAqC;UAAAC,QAAA,gBACpDzB,OAAA;YAAOwB,SAAS,EAAC,YAAY;YAAAC,QAAA,eAC3BzB,OAAA;cAAAyB,QAAA,gBACEzB,OAAA;gBAAIwB,SAAS,EAAC,gFAAgF;gBAAAC,QAAA,EAAC;cAE/F;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACL7B,OAAA;gBAAIwB,SAAS,EAAC,gFAAgF;gBAAAC,QAAA,EAAC;cAE/F;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACL7B,OAAA;gBAAIwB,SAAS,EAAC,gFAAgF;gBAAAC,QAAA,EAAC;cAE/F;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACL7B,OAAA;gBAAIwB,SAAS,EAAC,gFAAgF;gBAAAC,QAAA,EAAC;cAE/F;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACL7B,OAAA;gBAAIwB,SAAS,EAAC,gFAAgF;gBAAAC,QAAA,EAAC;cAE/F;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC,eACR7B,OAAA;YAAOwB,SAAS,EAAC,mCAAmC;YAAAC,QAAA,EACjDlB,gBAAgB,CAAC0B,GAAG,CAAEE,OAAO,iBAC5BnC,OAAA;cAAAyB,QAAA,gBACEzB,OAAA;gBAAIwB,SAAS,EAAC,+DAA+D;gBAAAC,QAAA,EAC1EU,OAAO,CAACC,SAAS,IAAI;cAAK;gBAAAV,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzB,CAAC,eACL7B,OAAA;gBAAIwB,SAAS,EAAC,mDAAmD;gBAAAC,QAAA,EAC9DU,OAAO,CAACE;cAAK;gBAAAX,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACZ,CAAC,eACL7B,OAAA;gBAAIwB,SAAS,EAAC,mDAAmD;gBAAAC,QAAA,eAC/DzB,OAAA;kBAAMwB,SAAS,EAAE,4DACfW,OAAO,CAACG,IAAI,KAAK,OAAO,GAAG,+BAA+B,GAC1DH,OAAO,CAACG,IAAI,KAAK,YAAY,GAAG,2BAA2B,GAC3DH,OAAO,CAACG,IAAI,KAAK,MAAM,GAAG,6BAA6B,GACvD,2BAA2B,EAC1B;kBAAAb,QAAA,EACAU,OAAO,CAACG,IAAI,IAAI;gBAAO;kBAAAZ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpB;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC,eACL7B,OAAA;gBAAIwB,SAAS,EAAC,mDAAmD;gBAAAC,QAAA,EAC9DU,OAAO,CAACI,WAAW,IAAI;cAAK;gBAAAb,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3B,CAAC,eACL7B,OAAA;gBAAIwB,SAAS,EAAC,mDAAmD;gBAAAC,QAAA,eAC/DzB,OAAA;kBAAMwB,SAAS,EAAE,4DACfW,OAAO,CAACK,YAAY,GAAG,+BAA+B,GAAG,6BAA6B,EACrF;kBAAAf,QAAA,EACAU,OAAO,CAACK,YAAY,GAAG,cAAc,GAAG;gBAAQ;kBAAAd,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7C;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC;YAAA,GA1BEM,OAAO,CAACM,EAAE;cAAAf,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OA2Bf,CACL;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAGN7B,OAAA;MAAKwB,SAAS,EAAC,uDAAuD;MAAAC,QAAA,gBACpEzB,OAAA;QAAIwB,SAAS,EAAC,wCAAwC;QAAAC,QAAA,EAAC;MAAU;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACtE7B,OAAA;QAAKwB,SAAS,EAAC,iCAAiC;QAAAC,QAAA,gBAC9CzB,OAAA;UAAAyB,QAAA,GAAG,KAAG,eAAAzB,OAAA;YAAAyB,QAAA,EAAQ;UAA2B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,iBAAa,eAAA7B,OAAA;YAAAyB,QAAA,EAAM;UAA6B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,sCAAkC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eAC/I7B,OAAA;UAAAyB,QAAA,GAAG,KAAG,eAAAzB,OAAA;YAAAyB,QAAA,EAAQ;UAAiB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,sEAAkE;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eAC9G7B,OAAA;UAAAyB,QAAA,GAAG,KAAG,eAAAzB,OAAA;YAAAyB,QAAA,EAAQ;UAAiB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,sFAAkF;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eAC9H7B,OAAA;UAAAyB,QAAA,GAAG,KAAG,eAAAzB,OAAA;YAAAyB,QAAA,EAAQ;UAAe;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,oEAAgE;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC3B,EAAA,CAhMID,oBAA8B;AAAAyC,EAAA,GAA9BzC,oBAA8B;AAkMpC,eAAeA,oBAAoB;AAAC,IAAAyC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}