{"ast": null, "code": "import { constructFrom } from \"./constructFrom.js\";\nimport { constructNow } from \"./constructNow.js\";\nimport { isSameDay } from \"./isSameDay.js\";\nimport { subDays } from \"./subDays.js\";\n\n/**\n * The {@link isYesterday} function options.\n */\n\n/**\n * @name isYesterday\n * @category Day Helpers\n * @summary Is the given date yesterday?\n * @pure false\n *\n * @description\n * Is the given date yesterday?\n *\n * @param date - The date to check\n * @param options - An object with options\n *\n * @returns The date is yesterday\n *\n * @example\n * // If today is 6 October 2014, is 5 October 14:00:00 yesterday?\n * const result = isYesterday(new Date(2014, 9, 5, 14, 0))\n * //=> true\n */\nexport function isYesterday(date, options) {\n  return isSameDay(constructFrom(options?.in || date, date), subDays(constructNow(options?.in || date), 1));\n}\n\n// Fallback for modularized imports:\nexport default isYesterday;", "map": {"version": 3, "names": ["constructFrom", "constructNow", "isSameDay", "subDays", "isYesterday", "date", "options", "in"], "sources": ["C:/Users/<USER>/node_modules/date-fns/isYesterday.js"], "sourcesContent": ["import { constructFrom } from \"./constructFrom.js\";\nimport { constructNow } from \"./constructNow.js\";\nimport { isSameDay } from \"./isSameDay.js\";\nimport { subDays } from \"./subDays.js\";\n\n/**\n * The {@link isYesterday} function options.\n */\n\n/**\n * @name isYesterday\n * @category Day Helpers\n * @summary Is the given date yesterday?\n * @pure false\n *\n * @description\n * Is the given date yesterday?\n *\n * @param date - The date to check\n * @param options - An object with options\n *\n * @returns The date is yesterday\n *\n * @example\n * // If today is 6 October 2014, is 5 October 14:00:00 yesterday?\n * const result = isYesterday(new Date(2014, 9, 5, 14, 0))\n * //=> true\n */\nexport function isYesterday(date, options) {\n  return isSameDay(\n    constructFrom(options?.in || date, date),\n    subDays(constructNow(options?.in || date), 1),\n  );\n}\n\n// Fallback for modularized imports:\nexport default isYesterday;\n"], "mappings": "AAAA,SAASA,aAAa,QAAQ,oBAAoB;AAClD,SAASC,YAAY,QAAQ,mBAAmB;AAChD,SAASC,SAAS,QAAQ,gBAAgB;AAC1C,SAASC,OAAO,QAAQ,cAAc;;AAEtC;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,WAAWA,CAACC,IAAI,EAAEC,OAAO,EAAE;EACzC,OAAOJ,SAAS,CACdF,aAAa,CAACM,OAAO,EAAEC,EAAE,IAAIF,IAAI,EAAEA,IAAI,CAAC,EACxCF,OAAO,CAACF,YAAY,CAACK,OAAO,EAAEC,EAAE,IAAIF,IAAI,CAAC,EAAE,CAAC,CAC9C,CAAC;AACH;;AAEA;AACA,eAAeD,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}