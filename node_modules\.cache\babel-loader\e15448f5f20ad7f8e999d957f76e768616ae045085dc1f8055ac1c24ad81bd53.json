{"ast": null, "code": "import { toDate } from \"./toDate.js\";\n\n/**\n * The {@link getMonth} function options.\n */\n\n/**\n * @name getMonth\n * @category Month Helpers\n * @summary Get the month of the given date.\n *\n * @description\n * Get the month of the given date.\n *\n * @param date - The given date\n * @param options - An object with options\n *\n * @returns The month index (0-11)\n *\n * @example\n * // Which month is 29 February 2012?\n * const result = getMonth(new Date(2012, 1, 29))\n * //=> 1\n */\nexport function getMonth(date, options) {\n  return toDate(date, options?.in).getMonth();\n}\n\n// Fallback for modularized imports:\nexport default getMonth;", "map": {"version": 3, "names": ["toDate", "getMonth", "date", "options", "in"], "sources": ["C:/Users/<USER>/node_modules/date-fns/getMonth.js"], "sourcesContent": ["import { toDate } from \"./toDate.js\";\n\n/**\n * The {@link getMonth} function options.\n */\n\n/**\n * @name getMonth\n * @category Month Helpers\n * @summary Get the month of the given date.\n *\n * @description\n * Get the month of the given date.\n *\n * @param date - The given date\n * @param options - An object with options\n *\n * @returns The month index (0-11)\n *\n * @example\n * // Which month is 29 February 2012?\n * const result = getMonth(new Date(2012, 1, 29))\n * //=> 1\n */\nexport function getMonth(date, options) {\n  return toDate(date, options?.in).getMonth();\n}\n\n// Fallback for modularized imports:\nexport default getMonth;\n"], "mappings": "AAAA,SAASA,MAAM,QAAQ,aAAa;;AAEpC;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,QAAQA,CAACC,IAAI,EAAEC,OAAO,EAAE;EACtC,OAAOH,MAAM,CAACE,IAAI,EAAEC,OAAO,EAAEC,EAAE,CAAC,CAACH,QAAQ,CAAC,CAAC;AAC7C;;AAEA;AACA,eAAeA,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}