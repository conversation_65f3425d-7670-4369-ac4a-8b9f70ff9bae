{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\lostandfound\\\\discfinder-app\\\\src\\\\App.tsx\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction App() {\n  _s();\n  const [currentPage, setCurrentPage] = useState('home');\n  const renderPage = () => {\n    switch (currentPage) {\n      case 'home':\n        return /*#__PURE__*/_jsxDEV(Home, {\n          onNavigate: setCurrentPage\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 11,\n          columnNumber: 16\n        }, this);\n      case 'report-found':\n        return /*#__PURE__*/_jsxDEV(ReportFound, {\n          onNavigate: setCurrentPage\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 13,\n          columnNumber: 16\n        }, this);\n      case 'search-lost':\n        return /*#__PURE__*/_jsxDEV(SearchLost, {\n          onNavigate: setCurrentPage\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 15,\n          columnNumber: 16\n        }, this);\n      case 'login':\n        return /*#__PURE__*/_jsxDEV(Login, {\n          onNavigate: setCurrentPage\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 17,\n          columnNumber: 16\n        }, this);\n      default:\n        return /*#__PURE__*/_jsxDEV(Home, {\n          onNavigate: setCurrentPage\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 19,\n          columnNumber: 16\n        }, this);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"app\",\n    children: [/*#__PURE__*/_jsxDEV(\"nav\", {\n      className: \"navbar\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"nav-container\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"logo\",\n          onClick: () => setCurrentPage('home'),\n          style: {\n            cursor: 'pointer'\n          },\n          children: \"DiscFinder\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 27,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"nav-buttons\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"nav-button\",\n            onClick: () => setCurrentPage('report-found'),\n            children: \"Report Found\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 31,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"nav-button\",\n            onClick: () => setCurrentPage('search-lost'),\n            children: \"Search Lost\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 34,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"nav-button primary\",\n            onClick: () => setCurrentPage('login'),\n            children: \"Sign Up\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 37,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 30,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 26,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 25,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"main\", {\n      className: \"main-container\",\n      children: renderPage()\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 44,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 24,\n    columnNumber: 5\n  }, this);\n}\n_s(App, \"KLlrbvIFn6o4dTsrFf/Szg7G3bM=\");\n_c = App;\nfunction Home({\n  onNavigate\n}) {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"hero\",\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        children: \"Lost Your Disc?\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 59,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"DiscFinder helps disc golf players reunite with their lost discs. Report found discs or search for your lost ones in our community database.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 60,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"hero-buttons\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"hero-button primary\",\n          onClick: () => onNavigate('report-found'),\n          children: \"Report Found Disc\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 66,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"hero-button secondary\",\n          onClick: () => onNavigate('search-lost'),\n          children: \"Search Lost Discs\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 69,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 65,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 58,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"features\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"feature-card\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"feature-icon\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            children: \"\\uD83D\\uDD0D\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 78,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 77,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n          children: \"Smart Matching\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 80,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Our intelligent system matches found and lost discs based on brand, model, color, and location.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 81,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 76,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"feature-card\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"feature-icon\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            children: \"\\uD83D\\uDCCD\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 88,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 87,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n          children: \"Location Based\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 90,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Find discs near where you lost them with our location-based search and matching.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 91,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 86,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"feature-card\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"feature-icon\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            children: \"\\uD83D\\uDCAC\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 98,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 97,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n          children: \"Easy Communication\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 100,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Connect directly with finders and owners through our secure messaging system.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 101,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 96,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 75,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"stats\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"stats-grid\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-item\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"stat-number\",\n            children: \"500+\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 110,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"stat-label\",\n            children: \"Discs Reunited\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 111,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 109,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-item\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"stat-number\",\n            children: \"1,200+\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 114,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"stat-label\",\n            children: \"Active Users\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 115,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 113,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-item\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"stat-number\",\n            children: \"95%\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 118,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"stat-label\",\n            children: \"Success Rate\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 119,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 117,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 108,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 107,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"cta\",\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        children: \"Join the Community\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 125,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"Create an account to report found discs, search for lost ones, and help fellow disc golfers.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 126,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        className: \"cta-button\",\n        onClick: () => onNavigate('login'),\n        children: \"Sign Up Now\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 129,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 124,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 57,\n    columnNumber: 5\n  }, this);\n}\n_c2 = Home;\nfunction ReportFound({\n  onNavigate\n}) {\n  _s2();\n  const [formData, setFormData] = useState({\n    brand: '',\n    model: '',\n    discType: '',\n    color: '',\n    weight: '',\n    condition: 'good',\n    plasticType: '',\n    stampText: '',\n    phoneNumber: '',\n    nameOnDisc: '',\n    locationFound: '',\n    foundDate: '',\n    description: ''\n  });\n  const handleInputChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: value\n    }));\n  };\n  const handleSubmit = e => {\n    e.preventDefault();\n    // TODO: Submit to Supabase\n    alert('Found disc reported successfully! (Demo mode)');\n    console.log('Form data:', formData);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"form-container\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"form-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n        className: \"back-button\",\n        onClick: () => onNavigate('home'),\n        children: \"\\u2190 Back to Home\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 172,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n        children: \"Report Found Disc\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 175,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"Help reunite a disc with its owner by providing details about the disc you found.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 176,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 171,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n      className: \"disc-form\",\n      onSubmit: handleSubmit,\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"form-section\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          children: \"Disc Information\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 181,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-row\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"brand\",\n              children: \"Brand *\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 184,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              id: \"brand\",\n              name: \"brand\",\n              value: formData.brand,\n              onChange: handleInputChange,\n              required: true,\n              placeholder: \"e.g., Innova, Discraft, Dynamic Discs\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 185,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 183,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"model\",\n              children: \"Model\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 196,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              id: \"model\",\n              name: \"model\",\n              value: formData.model,\n              onChange: handleInputChange,\n              placeholder: \"e.g., Destroyer, Buzzz, Judge\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 197,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 195,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 182,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-row\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"discType\",\n              children: \"Disc Type\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 210,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n              id: \"discType\",\n              name: \"discType\",\n              value: formData.discType,\n              onChange: handleInputChange,\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"\",\n                children: \"Select type\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 217,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"putter\",\n                children: \"Putter\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 218,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"midrange\",\n                children: \"Midrange\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 219,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"fairway_driver\",\n                children: \"Fairway Driver\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 220,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"distance_driver\",\n                children: \"Distance Driver\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 221,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"approach\",\n                children: \"Approach\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 222,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 211,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 209,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"color\",\n              children: \"Color *\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 226,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              id: \"color\",\n              name: \"color\",\n              value: formData.color,\n              onChange: handleInputChange,\n              required: true,\n              placeholder: \"e.g., Blue, Red, Orange\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 227,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 225,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 208,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-row\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"weight\",\n              children: \"Weight (grams)\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 241,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"number\",\n              id: \"weight\",\n              name: \"weight\",\n              value: formData.weight,\n              onChange: handleInputChange,\n              placeholder: \"e.g., 175\",\n              min: \"100\",\n              max: \"200\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 242,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 240,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"condition\",\n              children: \"Condition\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 254,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n              id: \"condition\",\n              name: \"condition\",\n              value: formData.condition,\n              onChange: handleInputChange,\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"new\",\n                children: \"New\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 261,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"excellent\",\n                children: \"Excellent\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 262,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"good\",\n                children: \"Good\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 263,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"fair\",\n                children: \"Fair\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 264,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"poor\",\n                children: \"Poor\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 265,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 255,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 253,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 239,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 180,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"form-section\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          children: \"Additional Details\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 272,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-row\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"plasticType\",\n              children: \"Plastic Type\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 275,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              id: \"plasticType\",\n              name: \"plasticType\",\n              value: formData.plasticType,\n              onChange: handleInputChange,\n              placeholder: \"e.g., Champion, ESP, Lucid\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 276,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 274,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"stampText\",\n              children: \"Stamp/Text\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 286,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              id: \"stampText\",\n              name: \"stampText\",\n              value: formData.stampText,\n              onChange: handleInputChange,\n              placeholder: \"Any text or stamps on the disc\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 287,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 285,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 273,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-row\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"phoneNumber\",\n              children: \"Phone Number on Disc\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 300,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"tel\",\n              id: \"phoneNumber\",\n              name: \"phoneNumber\",\n              value: formData.phoneNumber,\n              onChange: handleInputChange,\n              placeholder: \"Phone number written on disc\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 301,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 299,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"nameOnDisc\",\n              children: \"Name on Disc\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 311,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              id: \"nameOnDisc\",\n              name: \"nameOnDisc\",\n              value: formData.nameOnDisc,\n              onChange: handleInputChange,\n              placeholder: \"Name written on disc\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 312,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 310,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 298,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 271,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"form-section\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          children: \"Location & Date\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 325,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-row\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"locationFound\",\n              children: \"Location Found *\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 328,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              id: \"locationFound\",\n              name: \"locationFound\",\n              value: formData.locationFound,\n              onChange: handleInputChange,\n              required: true,\n              placeholder: \"e.g., Maple Hill Disc Golf Course, Hole 7\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 329,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 327,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"foundDate\",\n              children: \"Date Found *\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 340,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"date\",\n              id: \"foundDate\",\n              name: \"foundDate\",\n              value: formData.foundDate,\n              onChange: handleInputChange,\n              required: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 341,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 339,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 326,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            htmlFor: \"description\",\n            children: \"Additional Description\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 353,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n            id: \"description\",\n            name: \"description\",\n            value: formData.description,\n            onChange: handleInputChange,\n            rows: 4,\n            placeholder: \"Any additional details about where or how you found the disc...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 354,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 352,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 324,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"form-actions\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          type: \"button\",\n          className: \"button secondary\",\n          onClick: () => onNavigate('home'),\n          children: \"Cancel\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 366,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          type: \"submit\",\n          className: \"button primary\",\n          children: \"Report Found Disc\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 369,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 365,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 179,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 170,\n    columnNumber: 5\n  }, this);\n}\n_s2(ReportFound, \"jGtxyj6n0uNZxFHbbvqY6kBd9Jk=\");\n_c3 = ReportFound;\nfunction SearchLost({\n  onNavigate\n}) {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"page-container\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"page-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n        className: \"back-button\",\n        onClick: () => onNavigate('home'),\n        children: \"\\u2190 Back to Home\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 382,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n        children: \"Search Lost Discs\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 385,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"Search through reported found discs to see if someone has found your lost disc.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 386,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 381,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"coming-soon\",\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        children: \"Coming Soon!\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 389,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"The search functionality will be implemented next.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 390,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 388,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 380,\n    columnNumber: 5\n  }, this);\n}\n_c4 = SearchLost;\nfunction Login({\n  onNavigate\n}) {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"page-container\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"page-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n        className: \"back-button\",\n        onClick: () => onNavigate('home'),\n        children: \"\\u2190 Back to Home\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 400,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n        children: \"Sign Up / Login\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 403,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"Create an account or sign in to manage your disc reports.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 404,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 399,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"coming-soon\",\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        children: \"Coming Soon!\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 407,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"Authentication will be implemented next.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 408,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 406,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 398,\n    columnNumber: 5\n  }, this);\n}\n_c5 = Login;\nexport default App;\nvar _c, _c2, _c3, _c4, _c5;\n$RefreshReg$(_c, \"App\");\n$RefreshReg$(_c2, \"Home\");\n$RefreshReg$(_c3, \"ReportFound\");\n$RefreshReg$(_c4, \"SearchLost\");\n$RefreshReg$(_c5, \"Login\");", "map": {"version": 3, "names": ["React", "useState", "jsxDEV", "_jsxDEV", "App", "_s", "currentPage", "setCurrentPage", "renderPage", "Home", "onNavigate", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "ReportFound", "SearchLost", "<PERSON><PERSON>", "className", "children", "onClick", "style", "cursor", "_c", "_c2", "_s2", "formData", "setFormData", "brand", "model", "discType", "color", "weight", "condition", "plasticType", "stampText", "phoneNumber", "nameOnDisc", "locationFound", "foundDate", "description", "handleInputChange", "e", "name", "value", "target", "prev", "handleSubmit", "preventDefault", "alert", "console", "log", "onSubmit", "htmlFor", "type", "id", "onChange", "required", "placeholder", "min", "max", "rows", "_c3", "_c4", "_c5", "$RefreshReg$"], "sources": ["C:/Users/<USER>/lostandfound/discfinder-app/src/App.tsx"], "sourcesContent": ["import React, { useState } from 'react';\n\ntype Page = 'home' | 'report-found' | 'search-lost' | 'login';\n\nfunction App() {\n  const [currentPage, setCurrentPage] = useState<Page>('home');\n\n  const renderPage = () => {\n    switch (currentPage) {\n      case 'home':\n        return <Home onNavigate={setCurrentPage} />;\n      case 'report-found':\n        return <ReportFound onNavigate={setCurrentPage} />;\n      case 'search-lost':\n        return <SearchLost onNavigate={setCurrentPage} />;\n      case 'login':\n        return <Login onNavigate={setCurrentPage} />;\n      default:\n        return <Home onNavigate={setCurrentPage} />;\n    }\n  };\n\n  return (\n    <div className=\"app\">\n      <nav className=\"navbar\">\n        <div className=\"nav-container\">\n          <div className=\"logo\" onClick={() => setCurrentPage('home')} style={{ cursor: 'pointer' }}>\n            DiscFinder\n          </div>\n          <div className=\"nav-buttons\">\n            <button className=\"nav-button\" onClick={() => setCurrentPage('report-found')}>\n              Report Found\n            </button>\n            <button className=\"nav-button\" onClick={() => setCurrentPage('search-lost')}>\n              Search Lost\n            </button>\n            <button className=\"nav-button primary\" onClick={() => setCurrentPage('login')}>\n              Sign Up\n            </button>\n          </div>\n        </div>\n      </nav>\n\n      <main className=\"main-container\">\n        {renderPage()}\n      </main>\n    </div>\n  );\n}\n\ninterface PageProps {\n  onNavigate: (page: Page) => void;\n}\n\nfunction Home({ onNavigate }: PageProps) {\n  return (\n    <div>\n      <div className=\"hero\">\n        <h1>Lost Your Disc?</h1>\n        <p>\n          DiscFinder helps disc golf players reunite with their lost discs.\n          Report found discs or search for your lost ones in our community database.\n        </p>\n\n        <div className=\"hero-buttons\">\n          <button className=\"hero-button primary\" onClick={() => onNavigate('report-found')}>\n            Report Found Disc\n          </button>\n          <button className=\"hero-button secondary\" onClick={() => onNavigate('search-lost')}>\n            Search Lost Discs\n          </button>\n        </div>\n      </div>\n\n      <div className=\"features\">\n        <div className=\"feature-card\">\n          <div className=\"feature-icon\">\n            <div>🔍</div>\n          </div>\n          <h3>Smart Matching</h3>\n          <p>\n            Our intelligent system matches found and lost discs based on brand, model, color, and location.\n          </p>\n        </div>\n\n        <div className=\"feature-card\">\n          <div className=\"feature-icon\">\n            <div>📍</div>\n          </div>\n          <h3>Location Based</h3>\n          <p>\n            Find discs near where you lost them with our location-based search and matching.\n          </p>\n        </div>\n\n        <div className=\"feature-card\">\n          <div className=\"feature-icon\">\n            <div>💬</div>\n          </div>\n          <h3>Easy Communication</h3>\n          <p>\n            Connect directly with finders and owners through our secure messaging system.\n          </p>\n        </div>\n      </div>\n\n      <div className=\"stats\">\n        <div className=\"stats-grid\">\n          <div className=\"stat-item\">\n            <div className=\"stat-number\">500+</div>\n            <div className=\"stat-label\">Discs Reunited</div>\n          </div>\n          <div className=\"stat-item\">\n            <div className=\"stat-number\">1,200+</div>\n            <div className=\"stat-label\">Active Users</div>\n          </div>\n          <div className=\"stat-item\">\n            <div className=\"stat-number\">95%</div>\n            <div className=\"stat-label\">Success Rate</div>\n          </div>\n        </div>\n      </div>\n\n      <div className=\"cta\">\n        <h2>Join the Community</h2>\n        <p>\n          Create an account to report found discs, search for lost ones, and help fellow disc golfers.\n        </p>\n        <button className=\"cta-button\" onClick={() => onNavigate('login')}>\n          Sign Up Now\n        </button>\n      </div>\n    </div>\n  );\n}\n\nfunction ReportFound({ onNavigate }: PageProps) {\n  const [formData, setFormData] = useState({\n    brand: '',\n    model: '',\n    discType: '',\n    color: '',\n    weight: '',\n    condition: 'good',\n    plasticType: '',\n    stampText: '',\n    phoneNumber: '',\n    nameOnDisc: '',\n    locationFound: '',\n    foundDate: '',\n    description: '',\n  });\n\n  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {\n    const { name, value } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: value\n    }));\n  };\n\n  const handleSubmit = (e: React.FormEvent) => {\n    e.preventDefault();\n    // TODO: Submit to Supabase\n    alert('Found disc reported successfully! (Demo mode)');\n    console.log('Form data:', formData);\n  };\n\n  return (\n    <div className=\"form-container\">\n      <div className=\"form-header\">\n        <button className=\"back-button\" onClick={() => onNavigate('home')}>\n          ← Back to Home\n        </button>\n        <h1>Report Found Disc</h1>\n        <p>Help reunite a disc with its owner by providing details about the disc you found.</p>\n      </div>\n\n      <form className=\"disc-form\" onSubmit={handleSubmit}>\n        <div className=\"form-section\">\n          <h3>Disc Information</h3>\n          <div className=\"form-row\">\n            <div className=\"form-group\">\n              <label htmlFor=\"brand\">Brand *</label>\n              <input\n                type=\"text\"\n                id=\"brand\"\n                name=\"brand\"\n                value={formData.brand}\n                onChange={handleInputChange}\n                required\n                placeholder=\"e.g., Innova, Discraft, Dynamic Discs\"\n              />\n            </div>\n            <div className=\"form-group\">\n              <label htmlFor=\"model\">Model</label>\n              <input\n                type=\"text\"\n                id=\"model\"\n                name=\"model\"\n                value={formData.model}\n                onChange={handleInputChange}\n                placeholder=\"e.g., Destroyer, Buzzz, Judge\"\n              />\n            </div>\n          </div>\n\n          <div className=\"form-row\">\n            <div className=\"form-group\">\n              <label htmlFor=\"discType\">Disc Type</label>\n              <select\n                id=\"discType\"\n                name=\"discType\"\n                value={formData.discType}\n                onChange={handleInputChange}\n              >\n                <option value=\"\">Select type</option>\n                <option value=\"putter\">Putter</option>\n                <option value=\"midrange\">Midrange</option>\n                <option value=\"fairway_driver\">Fairway Driver</option>\n                <option value=\"distance_driver\">Distance Driver</option>\n                <option value=\"approach\">Approach</option>\n              </select>\n            </div>\n            <div className=\"form-group\">\n              <label htmlFor=\"color\">Color *</label>\n              <input\n                type=\"text\"\n                id=\"color\"\n                name=\"color\"\n                value={formData.color}\n                onChange={handleInputChange}\n                required\n                placeholder=\"e.g., Blue, Red, Orange\"\n              />\n            </div>\n          </div>\n\n          <div className=\"form-row\">\n            <div className=\"form-group\">\n              <label htmlFor=\"weight\">Weight (grams)</label>\n              <input\n                type=\"number\"\n                id=\"weight\"\n                name=\"weight\"\n                value={formData.weight}\n                onChange={handleInputChange}\n                placeholder=\"e.g., 175\"\n                min=\"100\"\n                max=\"200\"\n              />\n            </div>\n            <div className=\"form-group\">\n              <label htmlFor=\"condition\">Condition</label>\n              <select\n                id=\"condition\"\n                name=\"condition\"\n                value={formData.condition}\n                onChange={handleInputChange}\n              >\n                <option value=\"new\">New</option>\n                <option value=\"excellent\">Excellent</option>\n                <option value=\"good\">Good</option>\n                <option value=\"fair\">Fair</option>\n                <option value=\"poor\">Poor</option>\n              </select>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"form-section\">\n          <h3>Additional Details</h3>\n          <div className=\"form-row\">\n            <div className=\"form-group\">\n              <label htmlFor=\"plasticType\">Plastic Type</label>\n              <input\n                type=\"text\"\n                id=\"plasticType\"\n                name=\"plasticType\"\n                value={formData.plasticType}\n                onChange={handleInputChange}\n                placeholder=\"e.g., Champion, ESP, Lucid\"\n              />\n            </div>\n            <div className=\"form-group\">\n              <label htmlFor=\"stampText\">Stamp/Text</label>\n              <input\n                type=\"text\"\n                id=\"stampText\"\n                name=\"stampText\"\n                value={formData.stampText}\n                onChange={handleInputChange}\n                placeholder=\"Any text or stamps on the disc\"\n              />\n            </div>\n          </div>\n\n          <div className=\"form-row\">\n            <div className=\"form-group\">\n              <label htmlFor=\"phoneNumber\">Phone Number on Disc</label>\n              <input\n                type=\"tel\"\n                id=\"phoneNumber\"\n                name=\"phoneNumber\"\n                value={formData.phoneNumber}\n                onChange={handleInputChange}\n                placeholder=\"Phone number written on disc\"\n              />\n            </div>\n            <div className=\"form-group\">\n              <label htmlFor=\"nameOnDisc\">Name on Disc</label>\n              <input\n                type=\"text\"\n                id=\"nameOnDisc\"\n                name=\"nameOnDisc\"\n                value={formData.nameOnDisc}\n                onChange={handleInputChange}\n                placeholder=\"Name written on disc\"\n              />\n            </div>\n          </div>\n        </div>\n\n        <div className=\"form-section\">\n          <h3>Location & Date</h3>\n          <div className=\"form-row\">\n            <div className=\"form-group\">\n              <label htmlFor=\"locationFound\">Location Found *</label>\n              <input\n                type=\"text\"\n                id=\"locationFound\"\n                name=\"locationFound\"\n                value={formData.locationFound}\n                onChange={handleInputChange}\n                required\n                placeholder=\"e.g., Maple Hill Disc Golf Course, Hole 7\"\n              />\n            </div>\n            <div className=\"form-group\">\n              <label htmlFor=\"foundDate\">Date Found *</label>\n              <input\n                type=\"date\"\n                id=\"foundDate\"\n                name=\"foundDate\"\n                value={formData.foundDate}\n                onChange={handleInputChange}\n                required\n              />\n            </div>\n          </div>\n\n          <div className=\"form-group\">\n            <label htmlFor=\"description\">Additional Description</label>\n            <textarea\n              id=\"description\"\n              name=\"description\"\n              value={formData.description}\n              onChange={handleInputChange}\n              rows={4}\n              placeholder=\"Any additional details about where or how you found the disc...\"\n            />\n          </div>\n        </div>\n\n        <div className=\"form-actions\">\n          <button type=\"button\" className=\"button secondary\" onClick={() => onNavigate('home')}>\n            Cancel\n          </button>\n          <button type=\"submit\" className=\"button primary\">\n            Report Found Disc\n          </button>\n        </div>\n      </form>\n    </div>\n  );\n}\n\nfunction SearchLost({ onNavigate }: PageProps) {\n  return (\n    <div className=\"page-container\">\n      <div className=\"page-header\">\n        <button className=\"back-button\" onClick={() => onNavigate('home')}>\n          ← Back to Home\n        </button>\n        <h1>Search Lost Discs</h1>\n        <p>Search through reported found discs to see if someone has found your lost disc.</p>\n      </div>\n      <div className=\"coming-soon\">\n        <h2>Coming Soon!</h2>\n        <p>The search functionality will be implemented next.</p>\n      </div>\n    </div>\n  );\n}\n\nfunction Login({ onNavigate }: PageProps) {\n  return (\n    <div className=\"page-container\">\n      <div className=\"page-header\">\n        <button className=\"back-button\" onClick={() => onNavigate('home')}>\n          ← Back to Home\n        </button>\n        <h1>Sign Up / Login</h1>\n        <p>Create an account or sign in to manage your disc reports.</p>\n      </div>\n      <div className=\"coming-soon\">\n        <h2>Coming Soon!</h2>\n        <p>Authentication will be implemented next.</p>\n      </div>\n    </div>\n  );\n}\n\nexport default App;\n"], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAIxC,SAASC,GAAGA,CAAA,EAAG;EAAAC,EAAA;EACb,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGN,QAAQ,CAAO,MAAM,CAAC;EAE5D,MAAMO,UAAU,GAAGA,CAAA,KAAM;IACvB,QAAQF,WAAW;MACjB,KAAK,MAAM;QACT,oBAAOH,OAAA,CAACM,IAAI;UAACC,UAAU,EAAEH;QAAe;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAC7C,KAAK,cAAc;QACjB,oBAAOX,OAAA,CAACY,WAAW;UAACL,UAAU,EAAEH;QAAe;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACpD,KAAK,aAAa;QAChB,oBAAOX,OAAA,CAACa,UAAU;UAACN,UAAU,EAAEH;QAAe;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACnD,KAAK,OAAO;QACV,oBAAOX,OAAA,CAACc,KAAK;UAACP,UAAU,EAAEH;QAAe;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAC9C;QACE,oBAAOX,OAAA,CAACM,IAAI;UAACC,UAAU,EAAEH;QAAe;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;IAC/C;EACF,CAAC;EAED,oBACEX,OAAA;IAAKe,SAAS,EAAC,KAAK;IAAAC,QAAA,gBAClBhB,OAAA;MAAKe,SAAS,EAAC,QAAQ;MAAAC,QAAA,eACrBhB,OAAA;QAAKe,SAAS,EAAC,eAAe;QAAAC,QAAA,gBAC5BhB,OAAA;UAAKe,SAAS,EAAC,MAAM;UAACE,OAAO,EAAEA,CAAA,KAAMb,cAAc,CAAC,MAAM,CAAE;UAACc,KAAK,EAAE;YAAEC,MAAM,EAAE;UAAU,CAAE;UAAAH,QAAA,EAAC;QAE3F;UAAAR,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACNX,OAAA;UAAKe,SAAS,EAAC,aAAa;UAAAC,QAAA,gBAC1BhB,OAAA;YAAQe,SAAS,EAAC,YAAY;YAACE,OAAO,EAAEA,CAAA,KAAMb,cAAc,CAAC,cAAc,CAAE;YAAAY,QAAA,EAAC;UAE9E;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTX,OAAA;YAAQe,SAAS,EAAC,YAAY;YAACE,OAAO,EAAEA,CAAA,KAAMb,cAAc,CAAC,aAAa,CAAE;YAAAY,QAAA,EAAC;UAE7E;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTX,OAAA;YAAQe,SAAS,EAAC,oBAAoB;YAACE,OAAO,EAAEA,CAAA,KAAMb,cAAc,CAAC,OAAO,CAAE;YAAAY,QAAA,EAAC;UAE/E;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAENX,OAAA;MAAMe,SAAS,EAAC,gBAAgB;MAAAC,QAAA,EAC7BX,UAAU,CAAC;IAAC;MAAAG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACT,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEV;AAACT,EAAA,CA5CQD,GAAG;AAAAmB,EAAA,GAAHnB,GAAG;AAkDZ,SAASK,IAAIA,CAAC;EAAEC;AAAsB,CAAC,EAAE;EACvC,oBACEP,OAAA;IAAAgB,QAAA,gBACEhB,OAAA;MAAKe,SAAS,EAAC,MAAM;MAAAC,QAAA,gBACnBhB,OAAA;QAAAgB,QAAA,EAAI;MAAe;QAAAR,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACxBX,OAAA;QAAAgB,QAAA,EAAG;MAGH;QAAAR,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,eAEJX,OAAA;QAAKe,SAAS,EAAC,cAAc;QAAAC,QAAA,gBAC3BhB,OAAA;UAAQe,SAAS,EAAC,qBAAqB;UAACE,OAAO,EAAEA,CAAA,KAAMV,UAAU,CAAC,cAAc,CAAE;UAAAS,QAAA,EAAC;QAEnF;UAAAR,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTX,OAAA;UAAQe,SAAS,EAAC,uBAAuB;UAACE,OAAO,EAAEA,CAAA,KAAMV,UAAU,CAAC,aAAa,CAAE;UAAAS,QAAA,EAAC;QAEpF;UAAAR,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAENX,OAAA;MAAKe,SAAS,EAAC,UAAU;MAAAC,QAAA,gBACvBhB,OAAA;QAAKe,SAAS,EAAC,cAAc;QAAAC,QAAA,gBAC3BhB,OAAA;UAAKe,SAAS,EAAC,cAAc;UAAAC,QAAA,eAC3BhB,OAAA;YAAAgB,QAAA,EAAK;UAAE;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,eACNX,OAAA;UAAAgB,QAAA,EAAI;QAAc;UAAAR,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACvBX,OAAA;UAAAgB,QAAA,EAAG;QAEH;UAAAR,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eAENX,OAAA;QAAKe,SAAS,EAAC,cAAc;QAAAC,QAAA,gBAC3BhB,OAAA;UAAKe,SAAS,EAAC,cAAc;UAAAC,QAAA,eAC3BhB,OAAA;YAAAgB,QAAA,EAAK;UAAE;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,eACNX,OAAA;UAAAgB,QAAA,EAAI;QAAc;UAAAR,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACvBX,OAAA;UAAAgB,QAAA,EAAG;QAEH;UAAAR,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eAENX,OAAA;QAAKe,SAAS,EAAC,cAAc;QAAAC,QAAA,gBAC3BhB,OAAA;UAAKe,SAAS,EAAC,cAAc;UAAAC,QAAA,eAC3BhB,OAAA;YAAAgB,QAAA,EAAK;UAAE;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,eACNX,OAAA;UAAAgB,QAAA,EAAI;QAAkB;UAAAR,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC3BX,OAAA;UAAAgB,QAAA,EAAG;QAEH;UAAAR,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAENX,OAAA;MAAKe,SAAS,EAAC,OAAO;MAAAC,QAAA,eACpBhB,OAAA;QAAKe,SAAS,EAAC,YAAY;QAAAC,QAAA,gBACzBhB,OAAA;UAAKe,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxBhB,OAAA;YAAKe,SAAS,EAAC,aAAa;YAAAC,QAAA,EAAC;UAAI;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACvCX,OAAA;YAAKe,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAC;UAAc;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7C,CAAC,eACNX,OAAA;UAAKe,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxBhB,OAAA;YAAKe,SAAS,EAAC,aAAa;YAAAC,QAAA,EAAC;UAAM;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACzCX,OAAA;YAAKe,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAC;UAAY;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3C,CAAC,eACNX,OAAA;UAAKe,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxBhB,OAAA;YAAKe,SAAS,EAAC,aAAa;YAAAC,QAAA,EAAC;UAAG;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACtCX,OAAA;YAAKe,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAC;UAAY;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3C,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAENX,OAAA;MAAKe,SAAS,EAAC,KAAK;MAAAC,QAAA,gBAClBhB,OAAA;QAAAgB,QAAA,EAAI;MAAkB;QAAAR,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC3BX,OAAA;QAAAgB,QAAA,EAAG;MAEH;QAAAR,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,eACJX,OAAA;QAAQe,SAAS,EAAC,YAAY;QAACE,OAAO,EAAEA,CAAA,KAAMV,UAAU,CAAC,OAAO,CAAE;QAAAS,QAAA,EAAC;MAEnE;QAAAR,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV;AAACU,GAAA,GAhFQf,IAAI;AAkFb,SAASM,WAAWA,CAAC;EAAEL;AAAsB,CAAC,EAAE;EAAAe,GAAA;EAC9C,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAG1B,QAAQ,CAAC;IACvC2B,KAAK,EAAE,EAAE;IACTC,KAAK,EAAE,EAAE;IACTC,QAAQ,EAAE,EAAE;IACZC,KAAK,EAAE,EAAE;IACTC,MAAM,EAAE,EAAE;IACVC,SAAS,EAAE,MAAM;IACjBC,WAAW,EAAE,EAAE;IACfC,SAAS,EAAE,EAAE;IACbC,WAAW,EAAE,EAAE;IACfC,UAAU,EAAE,EAAE;IACdC,aAAa,EAAE,EAAE;IACjBC,SAAS,EAAE,EAAE;IACbC,WAAW,EAAE;EACf,CAAC,CAAC;EAEF,MAAMC,iBAAiB,GAAIC,CAAgF,IAAK;IAC9G,MAAM;MAAEC,IAAI;MAAEC;IAAM,CAAC,GAAGF,CAAC,CAACG,MAAM;IAChClB,WAAW,CAACmB,IAAI,KAAK;MACnB,GAAGA,IAAI;MACP,CAACH,IAAI,GAAGC;IACV,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAMG,YAAY,GAAIL,CAAkB,IAAK;IAC3CA,CAAC,CAACM,cAAc,CAAC,CAAC;IAClB;IACAC,KAAK,CAAC,+CAA+C,CAAC;IACtDC,OAAO,CAACC,GAAG,CAAC,YAAY,EAAEzB,QAAQ,CAAC;EACrC,CAAC;EAED,oBACEvB,OAAA;IAAKe,SAAS,EAAC,gBAAgB;IAAAC,QAAA,gBAC7BhB,OAAA;MAAKe,SAAS,EAAC,aAAa;MAAAC,QAAA,gBAC1BhB,OAAA;QAAQe,SAAS,EAAC,aAAa;QAACE,OAAO,EAAEA,CAAA,KAAMV,UAAU,CAAC,MAAM,CAAE;QAAAS,QAAA,EAAC;MAEnE;QAAAR,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACTX,OAAA;QAAAgB,QAAA,EAAI;MAAiB;QAAAR,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC1BX,OAAA;QAAAgB,QAAA,EAAG;MAAiF;QAAAR,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACrF,CAAC,eAENX,OAAA;MAAMe,SAAS,EAAC,WAAW;MAACkC,QAAQ,EAAEL,YAAa;MAAA5B,QAAA,gBACjDhB,OAAA;QAAKe,SAAS,EAAC,cAAc;QAAAC,QAAA,gBAC3BhB,OAAA;UAAAgB,QAAA,EAAI;QAAgB;UAAAR,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACzBX,OAAA;UAAKe,SAAS,EAAC,UAAU;UAAAC,QAAA,gBACvBhB,OAAA;YAAKe,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzBhB,OAAA;cAAOkD,OAAO,EAAC,OAAO;cAAAlC,QAAA,EAAC;YAAO;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACtCX,OAAA;cACEmD,IAAI,EAAC,MAAM;cACXC,EAAE,EAAC,OAAO;cACVZ,IAAI,EAAC,OAAO;cACZC,KAAK,EAAElB,QAAQ,CAACE,KAAM;cACtB4B,QAAQ,EAAEf,iBAAkB;cAC5BgB,QAAQ;cACRC,WAAW,EAAC;YAAuC;cAAA/C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eACNX,OAAA;YAAKe,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzBhB,OAAA;cAAOkD,OAAO,EAAC,OAAO;cAAAlC,QAAA,EAAC;YAAK;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACpCX,OAAA;cACEmD,IAAI,EAAC,MAAM;cACXC,EAAE,EAAC,OAAO;cACVZ,IAAI,EAAC,OAAO;cACZC,KAAK,EAAElB,QAAQ,CAACG,KAAM;cACtB2B,QAAQ,EAAEf,iBAAkB;cAC5BiB,WAAW,EAAC;YAA+B;cAAA/C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5C,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENX,OAAA;UAAKe,SAAS,EAAC,UAAU;UAAAC,QAAA,gBACvBhB,OAAA;YAAKe,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzBhB,OAAA;cAAOkD,OAAO,EAAC,UAAU;cAAAlC,QAAA,EAAC;YAAS;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC3CX,OAAA;cACEoD,EAAE,EAAC,UAAU;cACbZ,IAAI,EAAC,UAAU;cACfC,KAAK,EAAElB,QAAQ,CAACI,QAAS;cACzB0B,QAAQ,EAAEf,iBAAkB;cAAAtB,QAAA,gBAE5BhB,OAAA;gBAAQyC,KAAK,EAAC,EAAE;gBAAAzB,QAAA,EAAC;cAAW;gBAAAR,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACrCX,OAAA;gBAAQyC,KAAK,EAAC,QAAQ;gBAAAzB,QAAA,EAAC;cAAM;gBAAAR,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACtCX,OAAA;gBAAQyC,KAAK,EAAC,UAAU;gBAAAzB,QAAA,EAAC;cAAQ;gBAAAR,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC1CX,OAAA;gBAAQyC,KAAK,EAAC,gBAAgB;gBAAAzB,QAAA,EAAC;cAAc;gBAAAR,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACtDX,OAAA;gBAAQyC,KAAK,EAAC,iBAAiB;gBAAAzB,QAAA,EAAC;cAAe;gBAAAR,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACxDX,OAAA;gBAAQyC,KAAK,EAAC,UAAU;gBAAAzB,QAAA,EAAC;cAAQ;gBAAAR,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eACNX,OAAA;YAAKe,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzBhB,OAAA;cAAOkD,OAAO,EAAC,OAAO;cAAAlC,QAAA,EAAC;YAAO;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACtCX,OAAA;cACEmD,IAAI,EAAC,MAAM;cACXC,EAAE,EAAC,OAAO;cACVZ,IAAI,EAAC,OAAO;cACZC,KAAK,EAAElB,QAAQ,CAACK,KAAM;cACtByB,QAAQ,EAAEf,iBAAkB;cAC5BgB,QAAQ;cACRC,WAAW,EAAC;YAAyB;cAAA/C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENX,OAAA;UAAKe,SAAS,EAAC,UAAU;UAAAC,QAAA,gBACvBhB,OAAA;YAAKe,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzBhB,OAAA;cAAOkD,OAAO,EAAC,QAAQ;cAAAlC,QAAA,EAAC;YAAc;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC9CX,OAAA;cACEmD,IAAI,EAAC,QAAQ;cACbC,EAAE,EAAC,QAAQ;cACXZ,IAAI,EAAC,QAAQ;cACbC,KAAK,EAAElB,QAAQ,CAACM,MAAO;cACvBwB,QAAQ,EAAEf,iBAAkB;cAC5BiB,WAAW,EAAC,WAAW;cACvBC,GAAG,EAAC,KAAK;cACTC,GAAG,EAAC;YAAK;cAAAjD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eACNX,OAAA;YAAKe,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzBhB,OAAA;cAAOkD,OAAO,EAAC,WAAW;cAAAlC,QAAA,EAAC;YAAS;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC5CX,OAAA;cACEoD,EAAE,EAAC,WAAW;cACdZ,IAAI,EAAC,WAAW;cAChBC,KAAK,EAAElB,QAAQ,CAACO,SAAU;cAC1BuB,QAAQ,EAAEf,iBAAkB;cAAAtB,QAAA,gBAE5BhB,OAAA;gBAAQyC,KAAK,EAAC,KAAK;gBAAAzB,QAAA,EAAC;cAAG;gBAAAR,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAChCX,OAAA;gBAAQyC,KAAK,EAAC,WAAW;gBAAAzB,QAAA,EAAC;cAAS;gBAAAR,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC5CX,OAAA;gBAAQyC,KAAK,EAAC,MAAM;gBAAAzB,QAAA,EAAC;cAAI;gBAAAR,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAClCX,OAAA;gBAAQyC,KAAK,EAAC,MAAM;gBAAAzB,QAAA,EAAC;cAAI;gBAAAR,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAClCX,OAAA;gBAAQyC,KAAK,EAAC,MAAM;gBAAAzB,QAAA,EAAC;cAAI;gBAAAR,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5B,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENX,OAAA;QAAKe,SAAS,EAAC,cAAc;QAAAC,QAAA,gBAC3BhB,OAAA;UAAAgB,QAAA,EAAI;QAAkB;UAAAR,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC3BX,OAAA;UAAKe,SAAS,EAAC,UAAU;UAAAC,QAAA,gBACvBhB,OAAA;YAAKe,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzBhB,OAAA;cAAOkD,OAAO,EAAC,aAAa;cAAAlC,QAAA,EAAC;YAAY;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACjDX,OAAA;cACEmD,IAAI,EAAC,MAAM;cACXC,EAAE,EAAC,aAAa;cAChBZ,IAAI,EAAC,aAAa;cAClBC,KAAK,EAAElB,QAAQ,CAACQ,WAAY;cAC5BsB,QAAQ,EAAEf,iBAAkB;cAC5BiB,WAAW,EAAC;YAA4B;cAAA/C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eACNX,OAAA;YAAKe,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzBhB,OAAA;cAAOkD,OAAO,EAAC,WAAW;cAAAlC,QAAA,EAAC;YAAU;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC7CX,OAAA;cACEmD,IAAI,EAAC,MAAM;cACXC,EAAE,EAAC,WAAW;cACdZ,IAAI,EAAC,WAAW;cAChBC,KAAK,EAAElB,QAAQ,CAACS,SAAU;cAC1BqB,QAAQ,EAAEf,iBAAkB;cAC5BiB,WAAW,EAAC;YAAgC;cAAA/C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7C,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENX,OAAA;UAAKe,SAAS,EAAC,UAAU;UAAAC,QAAA,gBACvBhB,OAAA;YAAKe,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzBhB,OAAA;cAAOkD,OAAO,EAAC,aAAa;cAAAlC,QAAA,EAAC;YAAoB;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACzDX,OAAA;cACEmD,IAAI,EAAC,KAAK;cACVC,EAAE,EAAC,aAAa;cAChBZ,IAAI,EAAC,aAAa;cAClBC,KAAK,EAAElB,QAAQ,CAACU,WAAY;cAC5BoB,QAAQ,EAAEf,iBAAkB;cAC5BiB,WAAW,EAAC;YAA8B;cAAA/C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3C,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eACNX,OAAA;YAAKe,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzBhB,OAAA;cAAOkD,OAAO,EAAC,YAAY;cAAAlC,QAAA,EAAC;YAAY;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAChDX,OAAA;cACEmD,IAAI,EAAC,MAAM;cACXC,EAAE,EAAC,YAAY;cACfZ,IAAI,EAAC,YAAY;cACjBC,KAAK,EAAElB,QAAQ,CAACW,UAAW;cAC3BmB,QAAQ,EAAEf,iBAAkB;cAC5BiB,WAAW,EAAC;YAAsB;cAAA/C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENX,OAAA;QAAKe,SAAS,EAAC,cAAc;QAAAC,QAAA,gBAC3BhB,OAAA;UAAAgB,QAAA,EAAI;QAAe;UAAAR,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACxBX,OAAA;UAAKe,SAAS,EAAC,UAAU;UAAAC,QAAA,gBACvBhB,OAAA;YAAKe,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzBhB,OAAA;cAAOkD,OAAO,EAAC,eAAe;cAAAlC,QAAA,EAAC;YAAgB;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACvDX,OAAA;cACEmD,IAAI,EAAC,MAAM;cACXC,EAAE,EAAC,eAAe;cAClBZ,IAAI,EAAC,eAAe;cACpBC,KAAK,EAAElB,QAAQ,CAACY,aAAc;cAC9BkB,QAAQ,EAAEf,iBAAkB;cAC5BgB,QAAQ;cACRC,WAAW,EAAC;YAA2C;cAAA/C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eACNX,OAAA;YAAKe,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzBhB,OAAA;cAAOkD,OAAO,EAAC,WAAW;cAAAlC,QAAA,EAAC;YAAY;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC/CX,OAAA;cACEmD,IAAI,EAAC,MAAM;cACXC,EAAE,EAAC,WAAW;cACdZ,IAAI,EAAC,WAAW;cAChBC,KAAK,EAAElB,QAAQ,CAACa,SAAU;cAC1BiB,QAAQ,EAAEf,iBAAkB;cAC5BgB,QAAQ;YAAA;cAAA9C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENX,OAAA;UAAKe,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzBhB,OAAA;YAAOkD,OAAO,EAAC,aAAa;YAAAlC,QAAA,EAAC;UAAsB;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAC3DX,OAAA;YACEoD,EAAE,EAAC,aAAa;YAChBZ,IAAI,EAAC,aAAa;YAClBC,KAAK,EAAElB,QAAQ,CAACc,WAAY;YAC5BgB,QAAQ,EAAEf,iBAAkB;YAC5BoB,IAAI,EAAE,CAAE;YACRH,WAAW,EAAC;UAAiE;YAAA/C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9E,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENX,OAAA;QAAKe,SAAS,EAAC,cAAc;QAAAC,QAAA,gBAC3BhB,OAAA;UAAQmD,IAAI,EAAC,QAAQ;UAACpC,SAAS,EAAC,kBAAkB;UAACE,OAAO,EAAEA,CAAA,KAAMV,UAAU,CAAC,MAAM,CAAE;UAAAS,QAAA,EAAC;QAEtF;UAAAR,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTX,OAAA;UAAQmD,IAAI,EAAC,QAAQ;UAACpC,SAAS,EAAC,gBAAgB;UAAAC,QAAA,EAAC;QAEjD;UAAAR,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEV;AAACW,GAAA,CA/OQV,WAAW;AAAA+C,GAAA,GAAX/C,WAAW;AAiPpB,SAASC,UAAUA,CAAC;EAAEN;AAAsB,CAAC,EAAE;EAC7C,oBACEP,OAAA;IAAKe,SAAS,EAAC,gBAAgB;IAAAC,QAAA,gBAC7BhB,OAAA;MAAKe,SAAS,EAAC,aAAa;MAAAC,QAAA,gBAC1BhB,OAAA;QAAQe,SAAS,EAAC,aAAa;QAACE,OAAO,EAAEA,CAAA,KAAMV,UAAU,CAAC,MAAM,CAAE;QAAAS,QAAA,EAAC;MAEnE;QAAAR,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACTX,OAAA;QAAAgB,QAAA,EAAI;MAAiB;QAAAR,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC1BX,OAAA;QAAAgB,QAAA,EAAG;MAA+E;QAAAR,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACnF,CAAC,eACNX,OAAA;MAAKe,SAAS,EAAC,aAAa;MAAAC,QAAA,gBAC1BhB,OAAA;QAAAgB,QAAA,EAAI;MAAY;QAAAR,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACrBX,OAAA;QAAAgB,QAAA,EAAG;MAAkD;QAAAR,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACtD,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV;AAACiD,GAAA,GAhBQ/C,UAAU;AAkBnB,SAASC,KAAKA,CAAC;EAAEP;AAAsB,CAAC,EAAE;EACxC,oBACEP,OAAA;IAAKe,SAAS,EAAC,gBAAgB;IAAAC,QAAA,gBAC7BhB,OAAA;MAAKe,SAAS,EAAC,aAAa;MAAAC,QAAA,gBAC1BhB,OAAA;QAAQe,SAAS,EAAC,aAAa;QAACE,OAAO,EAAEA,CAAA,KAAMV,UAAU,CAAC,MAAM,CAAE;QAAAS,QAAA,EAAC;MAEnE;QAAAR,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACTX,OAAA;QAAAgB,QAAA,EAAI;MAAe;QAAAR,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACxBX,OAAA;QAAAgB,QAAA,EAAG;MAAyD;QAAAR,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC7D,CAAC,eACNX,OAAA;MAAKe,SAAS,EAAC,aAAa;MAAAC,QAAA,gBAC1BhB,OAAA;QAAAgB,QAAA,EAAI;MAAY;QAAAR,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACrBX,OAAA;QAAAgB,QAAA,EAAG;MAAwC;QAAAR,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC5C,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV;AAACkD,GAAA,GAhBQ/C,KAAK;AAkBd,eAAeb,GAAG;AAAC,IAAAmB,EAAA,EAAAC,GAAA,EAAAsC,GAAA,EAAAC,GAAA,EAAAC,GAAA;AAAAC,YAAA,CAAA1C,EAAA;AAAA0C,YAAA,CAAAzC,GAAA;AAAAyC,YAAA,CAAAH,GAAA;AAAAG,YAAA,CAAAF,GAAA;AAAAE,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}