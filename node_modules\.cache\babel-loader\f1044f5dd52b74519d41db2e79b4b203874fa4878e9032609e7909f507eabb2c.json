{"ast": null, "code": "/**\n * Creates a timer that accepts a `timerCalc` function to perform calculated timeout retries, such as exponential backoff.\n *\n * @example\n *    let reconnectTimer = new Timer(() => this.connect(), function(tries){\n *      return [1000, 5000, 10000][tries - 1] || 10000\n *    })\n *    reconnectTimer.scheduleTimeout() // fires after 1000\n *    reconnectTimer.scheduleTimeout() // fires after 5000\n *    reconnectTimer.reset()\n *    reconnectTimer.scheduleTimeout() // fires after 1000\n */\nexport default class Timer {\n  constructor(callback, timerCalc) {\n    this.callback = callback;\n    this.timerCalc = timerCalc;\n    this.timer = undefined;\n    this.tries = 0;\n    this.callback = callback;\n    this.timerCalc = timerCalc;\n  }\n  reset() {\n    this.tries = 0;\n    clearTimeout(this.timer);\n  }\n  // Cancels any previous scheduleTimeout and schedules callback\n  scheduleTimeout() {\n    clearTimeout(this.timer);\n    this.timer = setTimeout(() => {\n      this.tries = this.tries + 1;\n      this.callback();\n    }, this.timerCalc(this.tries + 1));\n  }\n}", "map": {"version": 3, "names": ["Timer", "constructor", "callback", "timerCalc", "timer", "undefined", "tries", "reset", "clearTimeout", "scheduleTimeout", "setTimeout"], "sources": ["C:\\Users\\<USER>\\lostandfound\\node_modules\\@supabase\\realtime-js\\src\\lib\\timer.ts"], "sourcesContent": ["/**\n * Creates a timer that accepts a `timerCalc` function to perform calculated timeout retries, such as exponential backoff.\n *\n * @example\n *    let reconnectTimer = new Timer(() => this.connect(), function(tries){\n *      return [1000, 5000, 10000][tries - 1] || 10000\n *    })\n *    reconnectTimer.scheduleTimeout() // fires after 1000\n *    reconnectTimer.scheduleTimeout() // fires after 5000\n *    reconnectTimer.reset()\n *    reconnectTimer.scheduleTimeout() // fires after 1000\n */\nexport default class Timer {\n  timer: number | undefined = undefined\n  tries: number = 0\n\n  constructor(public callback: Function, public timerCalc: Function) {\n    this.callback = callback\n    this.timerCalc = timerCalc\n  }\n\n  reset() {\n    this.tries = 0\n    clearTimeout(this.timer)\n  }\n\n  // Cancels any previous scheduleTimeout and schedules callback\n  scheduleTimeout() {\n    clearTimeout(this.timer)\n\n    this.timer = <any>setTimeout(() => {\n      this.tries = this.tries + 1\n      this.callback()\n    }, this.timerCalc(this.tries + 1))\n  }\n}\n"], "mappings": "AAAA;;;;;;;;;;;;AAYA,eAAc,MAAOA,KAAK;EAIxBC,YAAmBC,QAAkB,EAASC,SAAmB;IAA9C,KAAAD,QAAQ,GAARA,QAAQ;IAAmB,KAAAC,SAAS,GAATA,SAAS;IAHvD,KAAAC,KAAK,GAAuBC,SAAS;IACrC,KAAAC,KAAK,GAAW,CAAC;IAGf,IAAI,CAACJ,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACC,SAAS,GAAGA,SAAS;EAC5B;EAEAI,KAAKA,CAAA;IACH,IAAI,CAACD,KAAK,GAAG,CAAC;IACdE,YAAY,CAAC,IAAI,CAACJ,KAAK,CAAC;EAC1B;EAEA;EACAK,eAAeA,CAAA;IACbD,YAAY,CAAC,IAAI,CAACJ,KAAK,CAAC;IAExB,IAAI,CAACA,KAAK,GAAQM,UAAU,CAAC,MAAK;MAChC,IAAI,CAACJ,KAAK,GAAG,IAAI,CAACA,KAAK,GAAG,CAAC;MAC3B,IAAI,CAACJ,QAAQ,EAAE;IACjB,CAAC,EAAE,IAAI,CAACC,SAAS,CAAC,IAAI,CAACG,KAAK,GAAG,CAAC,CAAC,CAAC;EACpC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}