{"ast": null, "code": "var __awaiter = this && this.__awaiter || function (thisArg, _arguments, P, generator) {\n  function adopt(value) {\n    return value instanceof P ? value : new P(function (resolve) {\n      resolve(value);\n    });\n  }\n  return new (P || (P = Promise))(function (resolve, reject) {\n    function fulfilled(value) {\n      try {\n        step(generator.next(value));\n      } catch (e) {\n        reject(e);\n      }\n    }\n    function rejected(value) {\n      try {\n        step(generator[\"throw\"](value));\n      } catch (e) {\n        reject(e);\n      }\n    }\n    function step(result) {\n      result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected);\n    }\n    step((generator = generator.apply(thisArg, _arguments || [])).next());\n  });\n};\nexport const resolveFetch = customFetch => {\n  let _fetch;\n  if (customFetch) {\n    _fetch = customFetch;\n  } else if (typeof fetch === 'undefined') {\n    _fetch = (...args) => import('@supabase/node-fetch').then(({\n      default: fetch\n    }) => fetch(...args));\n  } else {\n    _fetch = fetch;\n  }\n  return (...args) => _fetch(...args);\n};\nexport const resolveResponse = () => __awaiter(void 0, void 0, void 0, function* () {\n  if (typeof Response === 'undefined') {\n    // @ts-ignore\n    return (yield import('@supabase/node-fetch')).Response;\n  }\n  return Response;\n});\nexport const recursiveToCamel = item => {\n  if (Array.isArray(item)) {\n    return item.map(el => recursiveToCamel(el));\n  } else if (typeof item === 'function' || item !== Object(item)) {\n    return item;\n  }\n  const result = {};\n  Object.entries(item).forEach(([key, value]) => {\n    const newKey = key.replace(/([-_][a-z])/gi, c => c.toUpperCase().replace(/[-_]/g, ''));\n    result[newKey] = recursiveToCamel(value);\n  });\n  return result;\n};", "map": {"version": 3, "names": ["resolveFetch", "customFetch", "_fetch", "fetch", "args", "then", "default", "resolveResponse", "__awaiter", "Response", "recursiveToCamel", "item", "Array", "isArray", "map", "el", "Object", "result", "entries", "for<PERSON>ach", "key", "value", "new<PERSON>ey", "replace", "c", "toUpperCase"], "sources": ["C:\\Users\\<USER>\\node_modules\\@supabase\\storage-js\\src\\lib\\helpers.ts"], "sourcesContent": ["type Fetch = typeof fetch\n\nexport const resolveFetch = (customFetch?: Fetch): Fetch => {\n  let _fetch: Fetch\n  if (customFetch) {\n    _fetch = customFetch\n  } else if (typeof fetch === 'undefined') {\n    _fetch = (...args) =>\n      import('@supabase/node-fetch' as any).then(({ default: fetch }) => fetch(...args))\n  } else {\n    _fetch = fetch\n  }\n  return (...args) => _fetch(...args)\n}\n\nexport const resolveResponse = async (): Promise<typeof Response> => {\n  if (typeof Response === 'undefined') {\n    // @ts-ignore\n    return (await import('@supabase/node-fetch' as any)).Response\n  }\n\n  return Response\n}\n\nexport const recursiveToCamel = (item: Record<string, any>): unknown => {\n  if (Array.isArray(item)) {\n    return item.map((el) => recursiveToCamel(el))\n  } else if (typeof item === 'function' || item !== Object(item)) {\n    return item\n  }\n\n  const result: Record<string, any> = {}\n  Object.entries(item).forEach(([key, value]) => {\n    const newKey = key.replace(/([-_][a-z])/gi, (c) => c.toUpperCase().replace(/[-_]/g, ''))\n    result[newKey] = recursiveToCamel(value)\n  })\n\n  return result\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;AAEA,OAAO,MAAMA,YAAY,GAAIC,WAAmB,IAAW;EACzD,IAAIC,MAAa;EACjB,IAAID,WAAW,EAAE;IACfC,MAAM,GAAGD,WAAW;GACrB,MAAM,IAAI,OAAOE,KAAK,KAAK,WAAW,EAAE;IACvCD,MAAM,GAAGA,CAAC,GAAGE,IAAI,KACf,MAAM,CAAC,sBAA6B,CAAC,CAACC,IAAI,CAAC,CAAC;MAAEC,OAAO,EAAEH;IAAK,CAAE,KAAKA,KAAK,CAAC,GAAGC,IAAI,CAAC,CAAC;GACrF,MAAM;IACLF,MAAM,GAAGC,KAAK;;EAEhB,OAAO,CAAC,GAAGC,IAAI,KAAKF,MAAM,CAAC,GAAGE,IAAI,CAAC;AACrC,CAAC;AAED,OAAO,MAAMG,eAAe,GAAGA,CAAA,KAAqCC,SAAA;EAClE,IAAI,OAAOC,QAAQ,KAAK,WAAW,EAAE;IACnC;IACA,OAAO,CAAC,MAAM,MAAM,CAAC,sBAA6B,CAAC,EAAEA,QAAQ;;EAG/D,OAAOA,QAAQ;AACjB,CAAC;AAED,OAAO,MAAMC,gBAAgB,GAAIC,IAAyB,IAAa;EACrE,IAAIC,KAAK,CAACC,OAAO,CAACF,IAAI,CAAC,EAAE;IACvB,OAAOA,IAAI,CAACG,GAAG,CAAEC,EAAE,IAAKL,gBAAgB,CAACK,EAAE,CAAC,CAAC;GAC9C,MAAM,IAAI,OAAOJ,IAAI,KAAK,UAAU,IAAIA,IAAI,KAAKK,MAAM,CAACL,IAAI,CAAC,EAAE;IAC9D,OAAOA,IAAI;;EAGb,MAAMM,MAAM,GAAwB,EAAE;EACtCD,MAAM,CAACE,OAAO,CAACP,IAAI,CAAC,CAACQ,OAAO,CAAC,CAAC,CAACC,GAAG,EAAEC,KAAK,CAAC,KAAI;IAC5C,MAAMC,MAAM,GAAGF,GAAG,CAACG,OAAO,CAAC,eAAe,EAAGC,CAAC,IAAKA,CAAC,CAACC,WAAW,EAAE,CAACF,OAAO,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC;IACxFN,MAAM,CAACK,MAAM,CAAC,GAAGZ,gBAAgB,CAACW,KAAK,CAAC;EAC1C,CAAC,CAAC;EAEF,OAAOJ,MAAM;AACf,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}