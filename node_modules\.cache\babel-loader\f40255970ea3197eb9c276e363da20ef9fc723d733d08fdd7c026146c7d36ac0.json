{"ast": null, "code": "import { supabase } from './supabase';\n\n// Interface for the Glide app admin data structure\n\n// Interface for our profile import data\n\n/**\n * Maps a Glide admin record to our profile import format\n */\nfunction mapGlideAdminToProfile(admin) {\n  // Map role - blank roles become 'guest'\n  let role = 'guest';\n  if (admin.role) {\n    const lowerRole = admin.role.toLowerCase();\n    if (lowerRole === 'admin') {\n      role = 'admin';\n    } else if (lowerRole === 'rakerdiver') {\n      role = 'rakerdiver';\n    } else if (lowerRole === 'user') {\n      role = 'user';\n    }\n    // If role doesn't match known values, defaults to 'guest'\n  }\n  return {\n    email: admin.email,\n    full_name: admin.name || '',\n    role,\n    legacy_row_id: admin.defaultSourceRowId,\n    pdga_number: admin.pdga,\n    facebook_profile: admin.facebookProfile,\n    sms_number: admin.phoneNumberForTextMessages,\n    phone_number: admin.phoneNumberForTextMessages,\n    // Using same field for both\n    avatar_url: admin.photo // We'll handle photo migration separately\n  };\n}\n\n/**\n * Imports a single profile using the database function\n */\nasync function importSingleProfile(profileData) {\n  try {\n    const {\n      data,\n      error\n    } = await supabase.rpc('import_legacy_profile', {\n      p_email: profileData.email,\n      p_full_name: profileData.full_name,\n      p_role: profileData.role,\n      p_legacy_row_id: profileData.legacy_row_id,\n      p_pdga_number: profileData.pdga_number,\n      p_facebook_profile: profileData.facebook_profile,\n      p_instagram_handle: profileData.instagram_handle,\n      p_sms_number: profileData.sms_number,\n      p_phone_number: profileData.phone_number,\n      p_avatar_url: profileData.avatar_url\n    });\n    if (error) {\n      console.error('Error importing profile:', error);\n      return {\n        success: false,\n        error: error.message\n      };\n    }\n    return {\n      success: true,\n      id: data\n    };\n  } catch (err) {\n    console.error('Exception importing profile:', err);\n    return {\n      success: false,\n      error: err instanceof Error ? err.message : 'Unknown error'\n    };\n  }\n}\n\n/**\n * Imports multiple profiles from Glide admin data\n */\nexport async function importProfilesFromGlide(admins) {\n  const results = {\n    success: true,\n    imported: 0,\n    failed: 0,\n    errors: []\n  };\n  console.log(`Starting import of ${admins.length} profiles...`);\n  for (const admin of admins) {\n    try {\n      // Skip records without email\n      if (!admin.email) {\n        results.failed++;\n        results.errors.push('Skipped record without email');\n        continue;\n      }\n      const profileData = mapGlideAdminToProfile(admin);\n      const result = await importSingleProfile(profileData);\n      if (result.success) {\n        results.imported++;\n        console.log(`✓ Imported profile for ${admin.email}`);\n      } else {\n        results.failed++;\n        results.errors.push(`Failed to import ${admin.email}: ${result.error}`);\n        console.error(`✗ Failed to import ${admin.email}:`, result.error);\n      }\n    } catch (err) {\n      results.failed++;\n      const errorMsg = err instanceof Error ? err.message : 'Unknown error';\n      results.errors.push(`Exception importing ${admin.email}: ${errorMsg}`);\n      console.error(`✗ Exception importing ${admin.email}:`, err);\n    }\n  }\n  if (results.failed > 0) {\n    results.success = false;\n  }\n  console.log(`Import complete: ${results.imported} imported, ${results.failed} failed`);\n  return results;\n}\n\n/**\n * Fetches profiles from Glide using the provided API code\n * Note: This requires the Glide API token and configuration\n */\nexport async function fetchProfilesFromGlide() {\n  // This is a placeholder - you'll need to implement the actual Glide API call\n  // based on your provided code structure\n\n  // Example implementation (you'll need to adapt this):\n  /*\n  import * as glide from \"@glideapps/tables\";\n   const adminsTable = glide.table({\n    token: \"57a0c23e-1215-451b-aa43-c3ef6a4bfeed\",\n    app: \"ruvULEC0fwdPVN39ErX6\",\n    table: \"native-table-qQtBfW3I3zbQYJd4b3oF\",\n    columns: {\n      name: { type: \"string\", name: \"Name\" },\n      email: { type: \"email-address\", name: \"Email\" },\n      photo: { type: \"image-uri\", name: \"Photo\" },\n      role: { type: \"string\", name: \"Role\" },\n      defaultSourceRowId: { type: \"string\", name: \"tGL3F\" },\n      created: { type: \"date-time\", name: \"k655R\" },\n      phoneNumberForTextMessages: { type: \"phone-number\", name: \"OhhU3\" },\n      pdga: { type: \"number\", name: \"Dscqd\" },\n      facebookProfile: { type: \"string\", name: \"n1NEI\" }\n    }\n  });\n   const admins = await adminsTable.get();\n  return admins;\n  */\n\n  throw new Error('fetchProfilesFromGlide not implemented - please implement based on your Glide API setup');\n}\n\n/**\n * Gets imported profiles that haven't signed up yet\n */\nexport async function getImportedProfilesNeedingSignup() {\n  const {\n    data,\n    error\n  } = await supabase.from('imported_profiles').select('*').eq('needs_signup', true);\n  if (error) {\n    console.error('Error fetching imported profiles:', error);\n    return {\n      success: false,\n      error: error.message,\n      profiles: []\n    };\n  }\n  return {\n    success: true,\n    profiles: data || []\n  };\n}\n\n/**\n * Manual import function for testing with sample data\n */\nexport async function importSampleProfiles() {\n  const sampleAdmins = [{\n    name: \"John Doe\",\n    email: \"<EMAIL>\",\n    role: \"user\",\n    pdga: 12345,\n    facebookProfile: \"john.doe.profile\",\n    phoneNumberForTextMessages: \"+1234567890\"\n  }, {\n    name: \"Jane Admin\",\n    email: \"<EMAIL>\",\n    role: \"admin\",\n    pdga: 67890,\n    phoneNumberForTextMessages: \"+0987654321\"\n  }, {\n    name: \"Bob Diver\",\n    email: \"<EMAIL>\",\n    role: \"rakerdiver\",\n    phoneNumberForTextMessages: \"+1122334455\"\n  }];\n  return await importProfilesFromGlide(sampleAdmins);\n}", "map": {"version": 3, "names": ["supabase", "mapGlideAdminToProfile", "admin", "role", "lowerRole", "toLowerCase", "email", "full_name", "name", "legacy_row_id", "defaultSourceRowId", "pdga_number", "pdga", "facebook_profile", "facebookProfile", "sms_number", "phoneNumberForTextMessages", "phone_number", "avatar_url", "photo", "importSingleProfile", "profileData", "data", "error", "rpc", "p_email", "p_full_name", "p_role", "p_legacy_row_id", "p_pdga_number", "p_facebook_profile", "p_instagram_handle", "instagram_handle", "p_sms_number", "p_phone_number", "p_avatar_url", "console", "success", "message", "id", "err", "Error", "importProfilesFromGlide", "admins", "results", "imported", "failed", "errors", "log", "length", "push", "result", "errorMsg", "fetchProfilesFromGlide", "getImportedProfilesNeedingSignup", "from", "select", "eq", "profiles", "importSampleProfiles", "sampleAdmins"], "sources": ["C:/Users/<USER>/lostandfound/discfinder-app/src/lib/profileImport.ts"], "sourcesContent": ["import { supabase } from './supabase';\n\n// Interface for the Glide app admin data structure\ninterface GlideAdmin {\n  name: string;\n  email: string;\n  photo?: string;\n  role?: string;\n  defaultSourceRowId?: string;\n  created?: string;\n  phoneNumberForTextMessages?: string;\n  pdga?: number;\n  facebookProfile?: string;\n}\n\n// Interface for our profile import data\ninterface ImportProfileData {\n  email: string;\n  full_name: string;\n  role: 'guest' | 'user' | 'admin' | 'rakerdiver';\n  legacy_row_id?: string;\n  pdga_number?: number;\n  facebook_profile?: string;\n  instagram_handle?: string;\n  sms_number?: string;\n  phone_number?: string;\n  avatar_url?: string;\n}\n\n/**\n * Maps a Glide admin record to our profile import format\n */\nfunction mapGlideAdminToProfile(admin: GlideAdmin): ImportProfileData {\n  // Map role - blank roles become 'guest'\n  let role: 'guest' | 'user' | 'admin' | 'rakerdiver' = 'guest';\n  if (admin.role) {\n    const lowerRole = admin.role.toLowerCase();\n    if (lowerRole === 'admin') {\n      role = 'admin';\n    } else if (lowerRole === 'rakerdiver') {\n      role = 'rakerdiver';\n    } else if (lowerRole === 'user') {\n      role = 'user';\n    }\n    // If role doesn't match known values, defaults to 'guest'\n  }\n\n  return {\n    email: admin.email,\n    full_name: admin.name || '',\n    role,\n    legacy_row_id: admin.defaultSourceRowId,\n    pdga_number: admin.pdga,\n    facebook_profile: admin.facebookProfile,\n    sms_number: admin.phoneNumberForTextMessages,\n    phone_number: admin.phoneNumberForTextMessages, // Using same field for both\n    avatar_url: admin.photo, // We'll handle photo migration separately\n  };\n}\n\n/**\n * Imports a single profile using the database function\n */\nasync function importSingleProfile(profileData: ImportProfileData): Promise<{ success: boolean; id?: string; error?: string }> {\n  try {\n    const { data, error } = await supabase.rpc('import_legacy_profile', {\n      p_email: profileData.email,\n      p_full_name: profileData.full_name,\n      p_role: profileData.role,\n      p_legacy_row_id: profileData.legacy_row_id,\n      p_pdga_number: profileData.pdga_number,\n      p_facebook_profile: profileData.facebook_profile,\n      p_instagram_handle: profileData.instagram_handle,\n      p_sms_number: profileData.sms_number,\n      p_phone_number: profileData.phone_number,\n      p_avatar_url: profileData.avatar_url,\n    });\n\n    if (error) {\n      console.error('Error importing profile:', error);\n      return { success: false, error: error.message };\n    }\n\n    return { success: true, id: data };\n  } catch (err) {\n    console.error('Exception importing profile:', err);\n    return { success: false, error: err instanceof Error ? err.message : 'Unknown error' };\n  }\n}\n\n/**\n * Imports multiple profiles from Glide admin data\n */\nexport async function importProfilesFromGlide(admins: GlideAdmin[]): Promise<{\n  success: boolean;\n  imported: number;\n  failed: number;\n  errors: string[];\n}> {\n  const results = {\n    success: true,\n    imported: 0,\n    failed: 0,\n    errors: [] as string[],\n  };\n\n  console.log(`Starting import of ${admins.length} profiles...`);\n\n  for (const admin of admins) {\n    try {\n      // Skip records without email\n      if (!admin.email) {\n        results.failed++;\n        results.errors.push('Skipped record without email');\n        continue;\n      }\n\n      const profileData = mapGlideAdminToProfile(admin);\n      const result = await importSingleProfile(profileData);\n\n      if (result.success) {\n        results.imported++;\n        console.log(`✓ Imported profile for ${admin.email}`);\n      } else {\n        results.failed++;\n        results.errors.push(`Failed to import ${admin.email}: ${result.error}`);\n        console.error(`✗ Failed to import ${admin.email}:`, result.error);\n      }\n    } catch (err) {\n      results.failed++;\n      const errorMsg = err instanceof Error ? err.message : 'Unknown error';\n      results.errors.push(`Exception importing ${admin.email}: ${errorMsg}`);\n      console.error(`✗ Exception importing ${admin.email}:`, err);\n    }\n  }\n\n  if (results.failed > 0) {\n    results.success = false;\n  }\n\n  console.log(`Import complete: ${results.imported} imported, ${results.failed} failed`);\n  return results;\n}\n\n/**\n * Fetches profiles from Glide using the provided API code\n * Note: This requires the Glide API token and configuration\n */\nexport async function fetchProfilesFromGlide(): Promise<GlideAdmin[]> {\n  // This is a placeholder - you'll need to implement the actual Glide API call\n  // based on your provided code structure\n  \n  // Example implementation (you'll need to adapt this):\n  /*\n  import * as glide from \"@glideapps/tables\";\n\n  const adminsTable = glide.table({\n    token: \"57a0c23e-1215-451b-aa43-c3ef6a4bfeed\",\n    app: \"ruvULEC0fwdPVN39ErX6\",\n    table: \"native-table-qQtBfW3I3zbQYJd4b3oF\",\n    columns: {\n      name: { type: \"string\", name: \"Name\" },\n      email: { type: \"email-address\", name: \"Email\" },\n      photo: { type: \"image-uri\", name: \"Photo\" },\n      role: { type: \"string\", name: \"Role\" },\n      defaultSourceRowId: { type: \"string\", name: \"tGL3F\" },\n      created: { type: \"date-time\", name: \"k655R\" },\n      phoneNumberForTextMessages: { type: \"phone-number\", name: \"OhhU3\" },\n      pdga: { type: \"number\", name: \"Dscqd\" },\n      facebookProfile: { type: \"string\", name: \"n1NEI\" }\n    }\n  });\n\n  const admins = await adminsTable.get();\n  return admins;\n  */\n  \n  throw new Error('fetchProfilesFromGlide not implemented - please implement based on your Glide API setup');\n}\n\n/**\n * Gets imported profiles that haven't signed up yet\n */\nexport async function getImportedProfilesNeedingSignup() {\n  const { data, error } = await supabase\n    .from('imported_profiles')\n    .select('*')\n    .eq('needs_signup', true);\n\n  if (error) {\n    console.error('Error fetching imported profiles:', error);\n    return { success: false, error: error.message, profiles: [] };\n  }\n\n  return { success: true, profiles: data || [] };\n}\n\n/**\n * Manual import function for testing with sample data\n */\nexport async function importSampleProfiles(): Promise<{\n  success: boolean;\n  imported: number;\n  failed: number;\n  errors: string[];\n}> {\n  const sampleAdmins: GlideAdmin[] = [\n    {\n      name: \"John Doe\",\n      email: \"<EMAIL>\",\n      role: \"user\",\n      pdga: 12345,\n      facebookProfile: \"john.doe.profile\",\n      phoneNumberForTextMessages: \"+1234567890\"\n    },\n    {\n      name: \"Jane Admin\",\n      email: \"<EMAIL>\", \n      role: \"admin\",\n      pdga: 67890,\n      phoneNumberForTextMessages: \"+0987654321\"\n    },\n    {\n      name: \"Bob Diver\",\n      email: \"<EMAIL>\",\n      role: \"rakerdiver\",\n      phoneNumberForTextMessages: \"+1122334455\"\n    }\n  ];\n\n  return await importProfilesFromGlide(sampleAdmins);\n}\n"], "mappings": "AAAA,SAASA,QAAQ,QAAQ,YAAY;;AAErC;;AAaA;;AAcA;AACA;AACA;AACA,SAASC,sBAAsBA,CAACC,KAAiB,EAAqB;EACpE;EACA,IAAIC,IAA+C,GAAG,OAAO;EAC7D,IAAID,KAAK,CAACC,IAAI,EAAE;IACd,MAAMC,SAAS,GAAGF,KAAK,CAACC,IAAI,CAACE,WAAW,CAAC,CAAC;IAC1C,IAAID,SAAS,KAAK,OAAO,EAAE;MACzBD,IAAI,GAAG,OAAO;IAChB,CAAC,MAAM,IAAIC,SAAS,KAAK,YAAY,EAAE;MACrCD,IAAI,GAAG,YAAY;IACrB,CAAC,MAAM,IAAIC,SAAS,KAAK,MAAM,EAAE;MAC/BD,IAAI,GAAG,MAAM;IACf;IACA;EACF;EAEA,OAAO;IACLG,KAAK,EAAEJ,KAAK,CAACI,KAAK;IAClBC,SAAS,EAAEL,KAAK,CAACM,IAAI,IAAI,EAAE;IAC3BL,IAAI;IACJM,aAAa,EAAEP,KAAK,CAACQ,kBAAkB;IACvCC,WAAW,EAAET,KAAK,CAACU,IAAI;IACvBC,gBAAgB,EAAEX,KAAK,CAACY,eAAe;IACvCC,UAAU,EAAEb,KAAK,CAACc,0BAA0B;IAC5CC,YAAY,EAAEf,KAAK,CAACc,0BAA0B;IAAE;IAChDE,UAAU,EAAEhB,KAAK,CAACiB,KAAK,CAAE;EAC3B,CAAC;AACH;;AAEA;AACA;AACA;AACA,eAAeC,mBAAmBA,CAACC,WAA8B,EAA8D;EAC7H,IAAI;IACF,MAAM;MAAEC,IAAI;MAAEC;IAAM,CAAC,GAAG,MAAMvB,QAAQ,CAACwB,GAAG,CAAC,uBAAuB,EAAE;MAClEC,OAAO,EAAEJ,WAAW,CAACf,KAAK;MAC1BoB,WAAW,EAAEL,WAAW,CAACd,SAAS;MAClCoB,MAAM,EAAEN,WAAW,CAAClB,IAAI;MACxByB,eAAe,EAAEP,WAAW,CAACZ,aAAa;MAC1CoB,aAAa,EAAER,WAAW,CAACV,WAAW;MACtCmB,kBAAkB,EAAET,WAAW,CAACR,gBAAgB;MAChDkB,kBAAkB,EAAEV,WAAW,CAACW,gBAAgB;MAChDC,YAAY,EAAEZ,WAAW,CAACN,UAAU;MACpCmB,cAAc,EAAEb,WAAW,CAACJ,YAAY;MACxCkB,YAAY,EAAEd,WAAW,CAACH;IAC5B,CAAC,CAAC;IAEF,IAAIK,KAAK,EAAE;MACTa,OAAO,CAACb,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;MAChD,OAAO;QAAEc,OAAO,EAAE,KAAK;QAAEd,KAAK,EAAEA,KAAK,CAACe;MAAQ,CAAC;IACjD;IAEA,OAAO;MAAED,OAAO,EAAE,IAAI;MAAEE,EAAE,EAAEjB;IAAK,CAAC;EACpC,CAAC,CAAC,OAAOkB,GAAG,EAAE;IACZJ,OAAO,CAACb,KAAK,CAAC,8BAA8B,EAAEiB,GAAG,CAAC;IAClD,OAAO;MAAEH,OAAO,EAAE,KAAK;MAAEd,KAAK,EAAEiB,GAAG,YAAYC,KAAK,GAAGD,GAAG,CAACF,OAAO,GAAG;IAAgB,CAAC;EACxF;AACF;;AAEA;AACA;AACA;AACA,OAAO,eAAeI,uBAAuBA,CAACC,MAAoB,EAK/D;EACD,MAAMC,OAAO,GAAG;IACdP,OAAO,EAAE,IAAI;IACbQ,QAAQ,EAAE,CAAC;IACXC,MAAM,EAAE,CAAC;IACTC,MAAM,EAAE;EACV,CAAC;EAEDX,OAAO,CAACY,GAAG,CAAC,sBAAsBL,MAAM,CAACM,MAAM,cAAc,CAAC;EAE9D,KAAK,MAAM/C,KAAK,IAAIyC,MAAM,EAAE;IAC1B,IAAI;MACF;MACA,IAAI,CAACzC,KAAK,CAACI,KAAK,EAAE;QAChBsC,OAAO,CAACE,MAAM,EAAE;QAChBF,OAAO,CAACG,MAAM,CAACG,IAAI,CAAC,8BAA8B,CAAC;QACnD;MACF;MAEA,MAAM7B,WAAW,GAAGpB,sBAAsB,CAACC,KAAK,CAAC;MACjD,MAAMiD,MAAM,GAAG,MAAM/B,mBAAmB,CAACC,WAAW,CAAC;MAErD,IAAI8B,MAAM,CAACd,OAAO,EAAE;QAClBO,OAAO,CAACC,QAAQ,EAAE;QAClBT,OAAO,CAACY,GAAG,CAAC,0BAA0B9C,KAAK,CAACI,KAAK,EAAE,CAAC;MACtD,CAAC,MAAM;QACLsC,OAAO,CAACE,MAAM,EAAE;QAChBF,OAAO,CAACG,MAAM,CAACG,IAAI,CAAC,oBAAoBhD,KAAK,CAACI,KAAK,KAAK6C,MAAM,CAAC5B,KAAK,EAAE,CAAC;QACvEa,OAAO,CAACb,KAAK,CAAC,sBAAsBrB,KAAK,CAACI,KAAK,GAAG,EAAE6C,MAAM,CAAC5B,KAAK,CAAC;MACnE;IACF,CAAC,CAAC,OAAOiB,GAAG,EAAE;MACZI,OAAO,CAACE,MAAM,EAAE;MAChB,MAAMM,QAAQ,GAAGZ,GAAG,YAAYC,KAAK,GAAGD,GAAG,CAACF,OAAO,GAAG,eAAe;MACrEM,OAAO,CAACG,MAAM,CAACG,IAAI,CAAC,uBAAuBhD,KAAK,CAACI,KAAK,KAAK8C,QAAQ,EAAE,CAAC;MACtEhB,OAAO,CAACb,KAAK,CAAC,yBAAyBrB,KAAK,CAACI,KAAK,GAAG,EAAEkC,GAAG,CAAC;IAC7D;EACF;EAEA,IAAII,OAAO,CAACE,MAAM,GAAG,CAAC,EAAE;IACtBF,OAAO,CAACP,OAAO,GAAG,KAAK;EACzB;EAEAD,OAAO,CAACY,GAAG,CAAC,oBAAoBJ,OAAO,CAACC,QAAQ,cAAcD,OAAO,CAACE,MAAM,SAAS,CAAC;EACtF,OAAOF,OAAO;AAChB;;AAEA;AACA;AACA;AACA;AACA,OAAO,eAAeS,sBAAsBA,CAAA,EAA0B;EACpE;EACA;;EAEA;EACA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;EAIE,MAAM,IAAIZ,KAAK,CAAC,yFAAyF,CAAC;AAC5G;;AAEA;AACA;AACA;AACA,OAAO,eAAea,gCAAgCA,CAAA,EAAG;EACvD,MAAM;IAAEhC,IAAI;IAAEC;EAAM,CAAC,GAAG,MAAMvB,QAAQ,CACnCuD,IAAI,CAAC,mBAAmB,CAAC,CACzBC,MAAM,CAAC,GAAG,CAAC,CACXC,EAAE,CAAC,cAAc,EAAE,IAAI,CAAC;EAE3B,IAAIlC,KAAK,EAAE;IACTa,OAAO,CAACb,KAAK,CAAC,mCAAmC,EAAEA,KAAK,CAAC;IACzD,OAAO;MAAEc,OAAO,EAAE,KAAK;MAAEd,KAAK,EAAEA,KAAK,CAACe,OAAO;MAAEoB,QAAQ,EAAE;IAAG,CAAC;EAC/D;EAEA,OAAO;IAAErB,OAAO,EAAE,IAAI;IAAEqB,QAAQ,EAAEpC,IAAI,IAAI;EAAG,CAAC;AAChD;;AAEA;AACA;AACA;AACA,OAAO,eAAeqC,oBAAoBA,CAAA,EAKvC;EACD,MAAMC,YAA0B,GAAG,CACjC;IACEpD,IAAI,EAAE,UAAU;IAChBF,KAAK,EAAE,sBAAsB;IAC7BH,IAAI,EAAE,MAAM;IACZS,IAAI,EAAE,KAAK;IACXE,eAAe,EAAE,kBAAkB;IACnCE,0BAA0B,EAAE;EAC9B,CAAC,EACD;IACER,IAAI,EAAE,YAAY;IAClBF,KAAK,EAAE,wBAAwB;IAC/BH,IAAI,EAAE,OAAO;IACbS,IAAI,EAAE,KAAK;IACXI,0BAA0B,EAAE;EAC9B,CAAC,EACD;IACER,IAAI,EAAE,WAAW;IACjBF,KAAK,EAAE,uBAAuB;IAC9BH,IAAI,EAAE,YAAY;IAClBa,0BAA0B,EAAE;EAC9B,CAAC,CACF;EAED,OAAO,MAAM0B,uBAAuB,CAACkB,YAAY,CAAC;AACpD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}