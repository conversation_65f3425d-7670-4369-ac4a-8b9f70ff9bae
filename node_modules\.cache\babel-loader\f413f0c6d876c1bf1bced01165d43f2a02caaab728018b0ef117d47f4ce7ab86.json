{"ast": null, "code": "\"use strict\";\n\nvar __importDefault = this && this.__importDefault || function (mod) {\n  return mod && mod.__esModule ? mod : {\n    \"default\": mod\n  };\n};\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\n// @ts-ignore\nconst node_fetch_1 = __importDefault(require(\"@supabase/node-fetch\"));\nconst PostgrestError_1 = __importDefault(require(\"./PostgrestError\"));\nclass PostgrestBuilder {\n  constructor(builder) {\n    this.shouldThrowOnError = false;\n    this.method = builder.method;\n    this.url = builder.url;\n    this.headers = builder.headers;\n    this.schema = builder.schema;\n    this.body = builder.body;\n    this.shouldThrowOnError = builder.shouldThrowOnError;\n    this.signal = builder.signal;\n    this.isMaybeSingle = builder.isMaybeSingle;\n    if (builder.fetch) {\n      this.fetch = builder.fetch;\n    } else if (typeof fetch === 'undefined') {\n      this.fetch = node_fetch_1.default;\n    } else {\n      this.fetch = fetch;\n    }\n  }\n  /**\n   * If there's an error with the query, throwOnError will reject the promise by\n   * throwing the error instead of returning it as part of a successful response.\n   *\n   * {@link https://github.com/supabase/supabase-js/issues/92}\n   */\n  throwOnError() {\n    this.shouldThrowOnError = true;\n    return this;\n  }\n  /**\n   * Set an HTTP header for the request.\n   */\n  setHeader(name, value) {\n    this.headers = Object.assign({}, this.headers);\n    this.headers[name] = value;\n    return this;\n  }\n  then(onfulfilled, onrejected) {\n    // https://postgrest.org/en/stable/api.html#switching-schemas\n    if (this.schema === undefined) {\n      // skip\n    } else if (['GET', 'HEAD'].includes(this.method)) {\n      this.headers['Accept-Profile'] = this.schema;\n    } else {\n      this.headers['Content-Profile'] = this.schema;\n    }\n    if (this.method !== 'GET' && this.method !== 'HEAD') {\n      this.headers['Content-Type'] = 'application/json';\n    }\n    // NOTE: Invoke w/o `this` to avoid illegal invocation error.\n    // https://github.com/supabase/postgrest-js/pull/247\n    const _fetch = this.fetch;\n    let res = _fetch(this.url.toString(), {\n      method: this.method,\n      headers: this.headers,\n      body: JSON.stringify(this.body),\n      signal: this.signal\n    }).then(async res => {\n      var _a, _b, _c;\n      let error = null;\n      let data = null;\n      let count = null;\n      let status = res.status;\n      let statusText = res.statusText;\n      if (res.ok) {\n        if (this.method !== 'HEAD') {\n          const body = await res.text();\n          if (body === '') {\n            // Prefer: return=minimal\n          } else if (this.headers['Accept'] === 'text/csv') {\n            data = body;\n          } else if (this.headers['Accept'] && this.headers['Accept'].includes('application/vnd.pgrst.plan+text')) {\n            data = body;\n          } else {\n            data = JSON.parse(body);\n          }\n        }\n        const countHeader = (_a = this.headers['Prefer']) === null || _a === void 0 ? void 0 : _a.match(/count=(exact|planned|estimated)/);\n        const contentRange = (_b = res.headers.get('content-range')) === null || _b === void 0 ? void 0 : _b.split('/');\n        if (countHeader && contentRange && contentRange.length > 1) {\n          count = parseInt(contentRange[1]);\n        }\n        // Temporary partial fix for https://github.com/supabase/postgrest-js/issues/361\n        // Issue persists e.g. for `.insert([...]).select().maybeSingle()`\n        if (this.isMaybeSingle && this.method === 'GET' && Array.isArray(data)) {\n          if (data.length > 1) {\n            error = {\n              // https://github.com/PostgREST/postgrest/blob/a867d79c42419af16c18c3fb019eba8df992626f/src/PostgREST/Error.hs#L553\n              code: 'PGRST116',\n              details: `Results contain ${data.length} rows, application/vnd.pgrst.object+json requires 1 row`,\n              hint: null,\n              message: 'JSON object requested, multiple (or no) rows returned'\n            };\n            data = null;\n            count = null;\n            status = 406;\n            statusText = 'Not Acceptable';\n          } else if (data.length === 1) {\n            data = data[0];\n          } else {\n            data = null;\n          }\n        }\n      } else {\n        const body = await res.text();\n        try {\n          error = JSON.parse(body);\n          // Workaround for https://github.com/supabase/postgrest-js/issues/295\n          if (Array.isArray(error) && res.status === 404) {\n            data = [];\n            error = null;\n            status = 200;\n            statusText = 'OK';\n          }\n        } catch (_d) {\n          // Workaround for https://github.com/supabase/postgrest-js/issues/295\n          if (res.status === 404 && body === '') {\n            status = 204;\n            statusText = 'No Content';\n          } else {\n            error = {\n              message: body\n            };\n          }\n        }\n        if (error && this.isMaybeSingle && ((_c = error === null || error === void 0 ? void 0 : error.details) === null || _c === void 0 ? void 0 : _c.includes('0 rows'))) {\n          error = null;\n          status = 200;\n          statusText = 'OK';\n        }\n        if (error && this.shouldThrowOnError) {\n          throw new PostgrestError_1.default(error);\n        }\n      }\n      const postgrestResponse = {\n        error,\n        data,\n        count,\n        status,\n        statusText\n      };\n      return postgrestResponse;\n    });\n    if (!this.shouldThrowOnError) {\n      res = res.catch(fetchError => {\n        var _a, _b, _c;\n        return {\n          error: {\n            message: `${(_a = fetchError === null || fetchError === void 0 ? void 0 : fetchError.name) !== null && _a !== void 0 ? _a : 'FetchError'}: ${fetchError === null || fetchError === void 0 ? void 0 : fetchError.message}`,\n            details: `${(_b = fetchError === null || fetchError === void 0 ? void 0 : fetchError.stack) !== null && _b !== void 0 ? _b : ''}`,\n            hint: '',\n            code: `${(_c = fetchError === null || fetchError === void 0 ? void 0 : fetchError.code) !== null && _c !== void 0 ? _c : ''}`\n          },\n          data: null,\n          count: null,\n          status: 0,\n          statusText: ''\n        };\n      });\n    }\n    return res.then(onfulfilled, onrejected);\n  }\n  /**\n   * Override the type of the returned `data`.\n   *\n   * @typeParam NewResult - The new result type to override with\n   * @deprecated Use overrideTypes<yourType, { merge: false }>() method at the end of your call chain instead\n   */\n  returns() {\n    /* istanbul ignore next */\n    return this;\n  }\n  /**\n   * Override the type of the returned `data` field in the response.\n   *\n   * @typeParam NewResult - The new type to cast the response data to\n   * @typeParam Options - Optional type configuration (defaults to { merge: true })\n   * @typeParam Options.merge - When true, merges the new type with existing return type. When false, replaces the existing types entirely (defaults to true)\n   * @example\n   * ```typescript\n   * // Merge with existing types (default behavior)\n   * const query = supabase\n   *   .from('users')\n   *   .select()\n   *   .overrideTypes<{ custom_field: string }>()\n   *\n   * // Replace existing types completely\n   * const replaceQuery = supabase\n   *   .from('users')\n   *   .select()\n   *   .overrideTypes<{ id: number; name: string }, { merge: false }>()\n   * ```\n   * @returns A PostgrestBuilder instance with the new type\n   */\n  overrideTypes() {\n    return this;\n  }\n}\nexports.default = PostgrestBuilder;", "map": {"version": 3, "names": ["node_fetch_1", "__importDefault", "require", "PostgrestError_1", "PostgrestBuilder", "constructor", "builder", "shouldThrowOnError", "method", "url", "headers", "schema", "body", "signal", "isMaybeSingle", "fetch", "default", "throwOnError", "<PERSON><PERSON><PERSON><PERSON>", "name", "value", "Object", "assign", "then", "onfulfilled", "onrejected", "undefined", "includes", "_fetch", "res", "toString", "JSON", "stringify", "error", "data", "count", "status", "statusText", "ok", "text", "parse", "<PERSON><PERSON><PERSON><PERSON>", "_a", "match", "contentRange", "_b", "get", "split", "length", "parseInt", "Array", "isArray", "code", "details", "hint", "message", "_d", "_c", "postgrestResponse", "catch", "fetchError", "stack", "returns", "overrideTypes", "exports"], "sources": ["C:\\Users\\<USER>\\node_modules\\@supabase\\postgrest-js\\src\\PostgrestBuilder.ts"], "sourcesContent": ["// @ts-ignore\nimport nodeFetch from '@supabase/node-fetch'\n\nimport type {\n  Fetch,\n  PostgrestSingleResponse,\n  PostgrestResponseSuccess,\n  CheckMatchingArrayTypes,\n  MergePartialResult,\n  IsValidResultOverride,\n} from './types'\nimport PostgrestError from './PostgrestError'\nimport { ContainsNull } from './select-query-parser/types'\n\nexport default abstract class PostgrestBuilder<Result, ThrowOnError extends boolean = false>\n  implements\n    PromiseLike<\n      ThrowOnError extends true ? PostgrestResponseSuccess<Result> : PostgrestSingleResponse<Result>\n    >\n{\n  protected method: 'GET' | 'HEAD' | 'POST' | 'PATCH' | 'DELETE'\n  protected url: URL\n  protected headers: Record<string, string>\n  protected schema?: string\n  protected body?: unknown\n  protected shouldThrowOnError = false\n  protected signal?: AbortSignal\n  protected fetch: Fetch\n  protected isMaybeSingle: boolean\n\n  constructor(builder: PostgrestBuilder<Result>) {\n    this.method = builder.method\n    this.url = builder.url\n    this.headers = builder.headers\n    this.schema = builder.schema\n    this.body = builder.body\n    this.shouldThrowOnError = builder.shouldThrowOnError\n    this.signal = builder.signal\n    this.isMaybeSingle = builder.isMaybeSingle\n\n    if (builder.fetch) {\n      this.fetch = builder.fetch\n    } else if (typeof fetch === 'undefined') {\n      this.fetch = nodeFetch\n    } else {\n      this.fetch = fetch\n    }\n  }\n\n  /**\n   * If there's an error with the query, throwOnError will reject the promise by\n   * throwing the error instead of returning it as part of a successful response.\n   *\n   * {@link https://github.com/supabase/supabase-js/issues/92}\n   */\n  throwOnError(): this & PostgrestBuilder<Result, true> {\n    this.shouldThrowOnError = true\n    return this as this & PostgrestBuilder<Result, true>\n  }\n\n  /**\n   * Set an HTTP header for the request.\n   */\n  setHeader(name: string, value: string): this {\n    this.headers = { ...this.headers }\n    this.headers[name] = value\n    return this\n  }\n\n  then<\n    TResult1 = ThrowOnError extends true\n      ? PostgrestResponseSuccess<Result>\n      : PostgrestSingleResponse<Result>,\n    TResult2 = never\n  >(\n    onfulfilled?:\n      | ((\n          value: ThrowOnError extends true\n            ? PostgrestResponseSuccess<Result>\n            : PostgrestSingleResponse<Result>\n        ) => TResult1 | PromiseLike<TResult1>)\n      | undefined\n      | null,\n    onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null\n  ): PromiseLike<TResult1 | TResult2> {\n    // https://postgrest.org/en/stable/api.html#switching-schemas\n    if (this.schema === undefined) {\n      // skip\n    } else if (['GET', 'HEAD'].includes(this.method)) {\n      this.headers['Accept-Profile'] = this.schema\n    } else {\n      this.headers['Content-Profile'] = this.schema\n    }\n    if (this.method !== 'GET' && this.method !== 'HEAD') {\n      this.headers['Content-Type'] = 'application/json'\n    }\n\n    // NOTE: Invoke w/o `this` to avoid illegal invocation error.\n    // https://github.com/supabase/postgrest-js/pull/247\n    const _fetch = this.fetch\n    let res = _fetch(this.url.toString(), {\n      method: this.method,\n      headers: this.headers,\n      body: JSON.stringify(this.body),\n      signal: this.signal,\n    }).then(async (res) => {\n      let error = null\n      let data = null\n      let count: number | null = null\n      let status = res.status\n      let statusText = res.statusText\n\n      if (res.ok) {\n        if (this.method !== 'HEAD') {\n          const body = await res.text()\n          if (body === '') {\n            // Prefer: return=minimal\n          } else if (this.headers['Accept'] === 'text/csv') {\n            data = body\n          } else if (\n            this.headers['Accept'] &&\n            this.headers['Accept'].includes('application/vnd.pgrst.plan+text')\n          ) {\n            data = body\n          } else {\n            data = JSON.parse(body)\n          }\n        }\n\n        const countHeader = this.headers['Prefer']?.match(/count=(exact|planned|estimated)/)\n        const contentRange = res.headers.get('content-range')?.split('/')\n        if (countHeader && contentRange && contentRange.length > 1) {\n          count = parseInt(contentRange[1])\n        }\n\n        // Temporary partial fix for https://github.com/supabase/postgrest-js/issues/361\n        // Issue persists e.g. for `.insert([...]).select().maybeSingle()`\n        if (this.isMaybeSingle && this.method === 'GET' && Array.isArray(data)) {\n          if (data.length > 1) {\n            error = {\n              // https://github.com/PostgREST/postgrest/blob/a867d79c42419af16c18c3fb019eba8df992626f/src/PostgREST/Error.hs#L553\n              code: 'PGRST116',\n              details: `Results contain ${data.length} rows, application/vnd.pgrst.object+json requires 1 row`,\n              hint: null,\n              message: 'JSON object requested, multiple (or no) rows returned',\n            }\n            data = null\n            count = null\n            status = 406\n            statusText = 'Not Acceptable'\n          } else if (data.length === 1) {\n            data = data[0]\n          } else {\n            data = null\n          }\n        }\n      } else {\n        const body = await res.text()\n\n        try {\n          error = JSON.parse(body)\n\n          // Workaround for https://github.com/supabase/postgrest-js/issues/295\n          if (Array.isArray(error) && res.status === 404) {\n            data = []\n            error = null\n            status = 200\n            statusText = 'OK'\n          }\n        } catch {\n          // Workaround for https://github.com/supabase/postgrest-js/issues/295\n          if (res.status === 404 && body === '') {\n            status = 204\n            statusText = 'No Content'\n          } else {\n            error = {\n              message: body,\n            }\n          }\n        }\n\n        if (error && this.isMaybeSingle && error?.details?.includes('0 rows')) {\n          error = null\n          status = 200\n          statusText = 'OK'\n        }\n\n        if (error && this.shouldThrowOnError) {\n          throw new PostgrestError(error)\n        }\n      }\n\n      const postgrestResponse = {\n        error,\n        data,\n        count,\n        status,\n        statusText,\n      }\n\n      return postgrestResponse\n    })\n    if (!this.shouldThrowOnError) {\n      res = res.catch((fetchError) => ({\n        error: {\n          message: `${fetchError?.name ?? 'FetchError'}: ${fetchError?.message}`,\n          details: `${fetchError?.stack ?? ''}`,\n          hint: '',\n          code: `${fetchError?.code ?? ''}`,\n        },\n        data: null,\n        count: null,\n        status: 0,\n        statusText: '',\n      }))\n    }\n\n    return res.then(onfulfilled, onrejected)\n  }\n\n  /**\n   * Override the type of the returned `data`.\n   *\n   * @typeParam NewResult - The new result type to override with\n   * @deprecated Use overrideTypes<yourType, { merge: false }>() method at the end of your call chain instead\n   */\n  returns<NewResult>(): PostgrestBuilder<CheckMatchingArrayTypes<Result, NewResult>, ThrowOnError> {\n    /* istanbul ignore next */\n    return this as unknown as PostgrestBuilder<\n      CheckMatchingArrayTypes<Result, NewResult>,\n      ThrowOnError\n    >\n  }\n\n  /**\n   * Override the type of the returned `data` field in the response.\n   *\n   * @typeParam NewResult - The new type to cast the response data to\n   * @typeParam Options - Optional type configuration (defaults to { merge: true })\n   * @typeParam Options.merge - When true, merges the new type with existing return type. When false, replaces the existing types entirely (defaults to true)\n   * @example\n   * ```typescript\n   * // Merge with existing types (default behavior)\n   * const query = supabase\n   *   .from('users')\n   *   .select()\n   *   .overrideTypes<{ custom_field: string }>()\n   *\n   * // Replace existing types completely\n   * const replaceQuery = supabase\n   *   .from('users')\n   *   .select()\n   *   .overrideTypes<{ id: number; name: string }, { merge: false }>()\n   * ```\n   * @returns A PostgrestBuilder instance with the new type\n   */\n  overrideTypes<\n    NewResult,\n    Options extends { merge?: boolean } = { merge: true }\n  >(): PostgrestBuilder<\n    IsValidResultOverride<Result, NewResult, false, false> extends true\n      ? // Preserve the optionality of the result if the overriden type is an object (case of chaining with `maybeSingle`)\n        ContainsNull<Result> extends true\n        ? MergePartialResult<NewResult, NonNullable<Result>, Options> | null\n        : MergePartialResult<NewResult, Result, Options>\n      : CheckMatchingArrayTypes<Result, NewResult>,\n    ThrowOnError\n  > {\n    return this as unknown as PostgrestBuilder<\n      IsValidResultOverride<Result, NewResult, false, false> extends true\n        ? // Preserve the optionality of the result if the overriden type is an object (case of chaining with `maybeSingle`)\n          ContainsNull<Result> extends true\n          ? MergePartialResult<NewResult, NonNullable<Result>, Options> | null\n          : MergePartialResult<NewResult, Result, Options>\n        : CheckMatchingArrayTypes<Result, NewResult>,\n      ThrowOnError\n    >\n  }\n}\n"], "mappings": ";;;;;;;;;;AAAA;AACA,MAAAA,YAAA,GAAAC,eAAA,CAAAC,OAAA;AAUA,MAAAC,gBAAA,GAAAF,eAAA,CAAAC,OAAA;AAGA,MAA8BE,gBAAgB;EAgB5CC,YAAYC,OAAiC;IALnC,KAAAC,kBAAkB,GAAG,KAAK;IAMlC,IAAI,CAACC,MAAM,GAAGF,OAAO,CAACE,MAAM;IAC5B,IAAI,CAACC,GAAG,GAAGH,OAAO,CAACG,GAAG;IACtB,IAAI,CAACC,OAAO,GAAGJ,OAAO,CAACI,OAAO;IAC9B,IAAI,CAACC,MAAM,GAAGL,OAAO,CAACK,MAAM;IAC5B,IAAI,CAACC,IAAI,GAAGN,OAAO,CAACM,IAAI;IACxB,IAAI,CAACL,kBAAkB,GAAGD,OAAO,CAACC,kBAAkB;IACpD,IAAI,CAACM,MAAM,GAAGP,OAAO,CAACO,MAAM;IAC5B,IAAI,CAACC,aAAa,GAAGR,OAAO,CAACQ,aAAa;IAE1C,IAAIR,OAAO,CAACS,KAAK,EAAE;MACjB,IAAI,CAACA,KAAK,GAAGT,OAAO,CAACS,KAAK;KAC3B,MAAM,IAAI,OAAOA,KAAK,KAAK,WAAW,EAAE;MACvC,IAAI,CAACA,KAAK,GAAGf,YAAA,CAAAgB,OAAS;KACvB,MAAM;MACL,IAAI,CAACD,KAAK,GAAGA,KAAK;;EAEtB;EAEA;;;;;;EAMAE,YAAYA,CAAA;IACV,IAAI,CAACV,kBAAkB,GAAG,IAAI;IAC9B,OAAO,IAA6C;EACtD;EAEA;;;EAGAW,SAASA,CAACC,IAAY,EAAEC,KAAa;IACnC,IAAI,CAACV,OAAO,GAAAW,MAAA,CAAAC,MAAA,KAAQ,IAAI,CAACZ,OAAO,CAAE;IAClC,IAAI,CAACA,OAAO,CAACS,IAAI,CAAC,GAAGC,KAAK;IAC1B,OAAO,IAAI;EACb;EAEAG,IAAIA,CAMFC,WAOQ,EACRC,UAAmF;IAEnF;IACA,IAAI,IAAI,CAACd,MAAM,KAAKe,SAAS,EAAE;MAC7B;IAAA,CACD,MAAM,IAAI,CAAC,KAAK,EAAE,MAAM,CAAC,CAACC,QAAQ,CAAC,IAAI,CAACnB,MAAM,CAAC,EAAE;MAChD,IAAI,CAACE,OAAO,CAAC,gBAAgB,CAAC,GAAG,IAAI,CAACC,MAAM;KAC7C,MAAM;MACL,IAAI,CAACD,OAAO,CAAC,iBAAiB,CAAC,GAAG,IAAI,CAACC,MAAM;;IAE/C,IAAI,IAAI,CAACH,MAAM,KAAK,KAAK,IAAI,IAAI,CAACA,MAAM,KAAK,MAAM,EAAE;MACnD,IAAI,CAACE,OAAO,CAAC,cAAc,CAAC,GAAG,kBAAkB;;IAGnD;IACA;IACA,MAAMkB,MAAM,GAAG,IAAI,CAACb,KAAK;IACzB,IAAIc,GAAG,GAAGD,MAAM,CAAC,IAAI,CAACnB,GAAG,CAACqB,QAAQ,EAAE,EAAE;MACpCtB,MAAM,EAAE,IAAI,CAACA,MAAM;MACnBE,OAAO,EAAE,IAAI,CAACA,OAAO;MACrBE,IAAI,EAAEmB,IAAI,CAACC,SAAS,CAAC,IAAI,CAACpB,IAAI,CAAC;MAC/BC,MAAM,EAAE,IAAI,CAACA;KACd,CAAC,CAACU,IAAI,CAAC,MAAOM,GAAG,IAAI;;MACpB,IAAII,KAAK,GAAG,IAAI;MAChB,IAAIC,IAAI,GAAG,IAAI;MACf,IAAIC,KAAK,GAAkB,IAAI;MAC/B,IAAIC,MAAM,GAAGP,GAAG,CAACO,MAAM;MACvB,IAAIC,UAAU,GAAGR,GAAG,CAACQ,UAAU;MAE/B,IAAIR,GAAG,CAACS,EAAE,EAAE;QACV,IAAI,IAAI,CAAC9B,MAAM,KAAK,MAAM,EAAE;UAC1B,MAAMI,IAAI,GAAG,MAAMiB,GAAG,CAACU,IAAI,EAAE;UAC7B,IAAI3B,IAAI,KAAK,EAAE,EAAE;YACf;UAAA,CACD,MAAM,IAAI,IAAI,CAACF,OAAO,CAAC,QAAQ,CAAC,KAAK,UAAU,EAAE;YAChDwB,IAAI,GAAGtB,IAAI;WACZ,MAAM,IACL,IAAI,CAACF,OAAO,CAAC,QAAQ,CAAC,IACtB,IAAI,CAACA,OAAO,CAAC,QAAQ,CAAC,CAACiB,QAAQ,CAAC,iCAAiC,CAAC,EAClE;YACAO,IAAI,GAAGtB,IAAI;WACZ,MAAM;YACLsB,IAAI,GAAGH,IAAI,CAACS,KAAK,CAAC5B,IAAI,CAAC;;;QAI3B,MAAM6B,WAAW,GAAG,CAAAC,EAAA,OAAI,CAAChC,OAAO,CAAC,QAAQ,CAAC,cAAAgC,EAAA,uBAAAA,EAAA,CAAEC,KAAK,CAAC,iCAAiC,CAAC;QACpF,MAAMC,YAAY,GAAG,CAAAC,EAAA,GAAAhB,GAAG,CAACnB,OAAO,CAACoC,GAAG,CAAC,eAAe,CAAC,cAAAD,EAAA,uBAAAA,EAAA,CAAEE,KAAK,CAAC,GAAG,CAAC;QACjE,IAAIN,WAAW,IAAIG,YAAY,IAAIA,YAAY,CAACI,MAAM,GAAG,CAAC,EAAE;UAC1Db,KAAK,GAAGc,QAAQ,CAACL,YAAY,CAAC,CAAC,CAAC,CAAC;;QAGnC;QACA;QACA,IAAI,IAAI,CAAC9B,aAAa,IAAI,IAAI,CAACN,MAAM,KAAK,KAAK,IAAI0C,KAAK,CAACC,OAAO,CAACjB,IAAI,CAAC,EAAE;UACtE,IAAIA,IAAI,CAACc,MAAM,GAAG,CAAC,EAAE;YACnBf,KAAK,GAAG;cACN;cACAmB,IAAI,EAAE,UAAU;cAChBC,OAAO,EAAE,mBAAmBnB,IAAI,CAACc,MAAM,yDAAyD;cAChGM,IAAI,EAAE,IAAI;cACVC,OAAO,EAAE;aACV;YACDrB,IAAI,GAAG,IAAI;YACXC,KAAK,GAAG,IAAI;YACZC,MAAM,GAAG,GAAG;YACZC,UAAU,GAAG,gBAAgB;WAC9B,MAAM,IAAIH,IAAI,CAACc,MAAM,KAAK,CAAC,EAAE;YAC5Bd,IAAI,GAAGA,IAAI,CAAC,CAAC,CAAC;WACf,MAAM;YACLA,IAAI,GAAG,IAAI;;;OAGhB,MAAM;QACL,MAAMtB,IAAI,GAAG,MAAMiB,GAAG,CAACU,IAAI,EAAE;QAE7B,IAAI;UACFN,KAAK,GAAGF,IAAI,CAACS,KAAK,CAAC5B,IAAI,CAAC;UAExB;UACA,IAAIsC,KAAK,CAACC,OAAO,CAAClB,KAAK,CAAC,IAAIJ,GAAG,CAACO,MAAM,KAAK,GAAG,EAAE;YAC9CF,IAAI,GAAG,EAAE;YACTD,KAAK,GAAG,IAAI;YACZG,MAAM,GAAG,GAAG;YACZC,UAAU,GAAG,IAAI;;SAEpB,CAAC,OAAAmB,EAAA,EAAM;UACN;UACA,IAAI3B,GAAG,CAACO,MAAM,KAAK,GAAG,IAAIxB,IAAI,KAAK,EAAE,EAAE;YACrCwB,MAAM,GAAG,GAAG;YACZC,UAAU,GAAG,YAAY;WAC1B,MAAM;YACLJ,KAAK,GAAG;cACNsB,OAAO,EAAE3C;aACV;;;QAIL,IAAIqB,KAAK,IAAI,IAAI,CAACnB,aAAa,KAAI,CAAA2C,EAAA,GAAAxB,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEoB,OAAO,cAAAI,EAAA,uBAAAA,EAAA,CAAE9B,QAAQ,CAAC,QAAQ,CAAC,GAAE;UACrEM,KAAK,GAAG,IAAI;UACZG,MAAM,GAAG,GAAG;UACZC,UAAU,GAAG,IAAI;;QAGnB,IAAIJ,KAAK,IAAI,IAAI,CAAC1B,kBAAkB,EAAE;UACpC,MAAM,IAAIJ,gBAAA,CAAAa,OAAc,CAACiB,KAAK,CAAC;;;MAInC,MAAMyB,iBAAiB,GAAG;QACxBzB,KAAK;QACLC,IAAI;QACJC,KAAK;QACLC,MAAM;QACNC;OACD;MAED,OAAOqB,iBAAiB;IAC1B,CAAC,CAAC;IACF,IAAI,CAAC,IAAI,CAACnD,kBAAkB,EAAE;MAC5BsB,GAAG,GAAGA,GAAG,CAAC8B,KAAK,CAAEC,UAAU,IAAI;;QAAC,OAAC;UAC/B3B,KAAK,EAAE;YACLsB,OAAO,EAAE,GAAG,CAAAb,EAAA,GAAAkB,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEzC,IAAI,cAAAuB,EAAA,cAAAA,EAAA,GAAI,YAAY,KAAKkB,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEL,OAAO,EAAE;YACtEF,OAAO,EAAE,GAAG,CAAAR,EAAA,GAAAe,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEC,KAAK,cAAAhB,EAAA,cAAAA,EAAA,GAAI,EAAE,EAAE;YACrCS,IAAI,EAAE,EAAE;YACRF,IAAI,EAAE,GAAG,CAAAK,EAAA,GAAAG,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAER,IAAI,cAAAK,EAAA,cAAAA,EAAA,GAAI,EAAE;WAChC;UACDvB,IAAI,EAAE,IAAI;UACVC,KAAK,EAAE,IAAI;UACXC,MAAM,EAAE,CAAC;UACTC,UAAU,EAAE;SACb;OAAC,CAAC;;IAGL,OAAOR,GAAG,CAACN,IAAI,CAACC,WAAW,EAAEC,UAAU,CAAC;EAC1C;EAEA;;;;;;EAMAqC,OAAOA,CAAA;IACL;IACA,OAAO,IAGN;EACH;EAEA;;;;;;;;;;;;;;;;;;;;;;EAsBAC,aAAaA,CAAA;IAYX,OAAO,IAQN;EACH;;AAvQFC,OAAA,CAAAhD,OAAA,GAAAZ,gBAAA", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}