{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\lostandfound\\\\discfinder-app\\\\src\\\\App.tsx\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$(),\n  _s3 = $RefreshSig$(),\n  _s4 = $RefreshSig$(),\n  _s5 = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { discService, imageService, supabaseService } from './lib/supabase';\nimport { AuthProvider, useAuth } from './contexts/AuthContext';\nimport { ImageUpload } from './components/ImageUpload';\nimport { ReturnStatusManager } from './components/ReturnStatusManager';\nimport { ContactAttempts } from './components/ContactAttempts';\nimport { RakerDiverDashboard } from './components/RakerDiverDashboard';\nimport { AdminBulkTurnins } from './components/AdminBulkTurnins';\nimport ProfileImportManager from './components/ProfileImportManager';\nimport ProfileManager from './components/ProfileManager';\nimport PhotoMigrationManager from './components/PhotoMigrationManager';\nimport SourceManager from './components/SourceManager';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nfunction AppContent() {\n  _s();\n  const [currentPage, setCurrentPage] = useState('home');\n  const {\n    user,\n    userRole,\n    signOut,\n    loading\n  } = useAuth();\n  const handleNavigate = page => {\n    setCurrentPage(page);\n  };\n  const renderPage = () => {\n    switch (currentPage) {\n      case 'home':\n        return /*#__PURE__*/_jsxDEV(Home, {\n          onNavigate: setCurrentPage\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 27,\n          columnNumber: 16\n        }, this);\n      case 'report-found':\n        return /*#__PURE__*/_jsxDEV(ReportFound, {\n          onNavigate: setCurrentPage\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 29,\n          columnNumber: 16\n        }, this);\n      case 'search-lost':\n        return /*#__PURE__*/_jsxDEV(SearchLost, {\n          onNavigate: setCurrentPage\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 31,\n          columnNumber: 16\n        }, this);\n      case 'login':\n        return /*#__PURE__*/_jsxDEV(Login, {\n          onNavigate: setCurrentPage\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 33,\n          columnNumber: 16\n        }, this);\n      case 'admin':\n        return /*#__PURE__*/_jsxDEV(AdminDashboard, {\n          onNavigate: setCurrentPage\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 35,\n          columnNumber: 16\n        }, this);\n      case 'rakerdiver':\n        return /*#__PURE__*/_jsxDEV(RakerDiverDashboard, {\n          onNavigate: handleNavigate\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 37,\n          columnNumber: 16\n        }, this);\n      case 'admin-bulk-turnins':\n        return /*#__PURE__*/_jsxDEV(AdminBulkTurnins, {\n          onNavigate: handleNavigate\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 39,\n          columnNumber: 16\n        }, this);\n      case 'profile-import':\n        return /*#__PURE__*/_jsxDEV(ProfileImportManager, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 41,\n          columnNumber: 16\n        }, this);\n      case 'photo-migration':\n        return /*#__PURE__*/_jsxDEV(PhotoMigrationManager, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 43,\n          columnNumber: 16\n        }, this);\n      case 'profile':\n        return user ? /*#__PURE__*/_jsxDEV(ProfileManager, {\n          userId: user.id\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 45,\n          columnNumber: 23\n        }, this) : /*#__PURE__*/_jsxDEV(Login, {\n          onNavigate: setCurrentPage\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 45,\n          columnNumber: 61\n        }, this);\n      default:\n        return /*#__PURE__*/_jsxDEV(Home, {\n          onNavigate: setCurrentPage\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 47,\n          columnNumber: 16\n        }, this);\n    }\n  };\n  const handleSignOut = async () => {\n    await signOut();\n    setCurrentPage('home');\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"app\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"loading-container\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"loading-spinner\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 60,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Loading...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 61,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          style: {\n            fontSize: '0.8rem',\n            color: '#666',\n            marginTop: '1rem'\n          },\n          children: \"If this takes too long, check the browser console for errors\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 62,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 59,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 58,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"app\",\n    children: [/*#__PURE__*/_jsxDEV(\"nav\", {\n      className: \"navbar\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"nav-container\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"logo\",\n          onClick: () => setCurrentPage('home'),\n          style: {\n            cursor: 'pointer'\n          },\n          children: \"DiscFinder\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 74,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"nav-buttons\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"nav-button\",\n            onClick: () => setCurrentPage('report-found'),\n            children: \"Report Found\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 78,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"nav-button\",\n            onClick: () => setCurrentPage('search-lost'),\n            children: \"Search Lost\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 81,\n            columnNumber: 13\n          }, this), user ? /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"user-menu\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"user-info\",\n              children: [user.email, \" (\", userRole, \")\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 87,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"nav-button\",\n              onClick: () => setCurrentPage('profile'),\n              children: \"Profile\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 90,\n              columnNumber: 17\n            }, this), userRole === 'admin' && /*#__PURE__*/_jsxDEV(_Fragment, {\n              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"nav-button\",\n                onClick: () => setCurrentPage('admin'),\n                children: \"Admin\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 95,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"nav-button\",\n                onClick: () => setCurrentPage('profile-import'),\n                children: \"Import\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 98,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"nav-button\",\n                onClick: () => setCurrentPage('photo-migration'),\n                children: \"Photos\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 101,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true), userRole === 'rakerdiver' && /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"nav-button\",\n              onClick: () => setCurrentPage('rakerdiver'),\n              children: \"RakerDiver\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 107,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"nav-button\",\n              onClick: handleSignOut,\n              children: \"Sign Out\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 111,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 86,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"nav-button primary\",\n            onClick: () => setCurrentPage('login'),\n            children: \"Sign In\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 116,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 77,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 73,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 72,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"main\", {\n      className: \"main-container\",\n      children: renderPage()\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 124,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 71,\n    columnNumber: 5\n  }, this);\n}\n_s(AppContent, \"/kJIu7Z5Ok/vEH6yLgQ1dcDyKKY=\", false, function () {\n  return [useAuth];\n});\n_c = AppContent;\nfunction App() {\n  return /*#__PURE__*/_jsxDEV(AuthProvider, {\n    children: /*#__PURE__*/_jsxDEV(AppContent, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 134,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 133,\n    columnNumber: 5\n  }, this);\n}\n_c2 = App;\nfunction AdminDashboard({\n  onNavigate\n}) {\n  _s2();\n  const {\n    userRole\n  } = useAuth();\n  const [allDiscs, setAllDiscs] = useState([]);\n  const [isLoading, setIsLoading] = useState(false);\n  const [filter, setFilter] = useState('All');\n  const [showSourceManager, setShowSourceManager] = useState(false);\n  const loadAllDiscs = async () => {\n    setIsLoading(true);\n    try {\n      const {\n        data,\n        error\n      } = await discService.getAdminFoundDiscs();\n      if (error) {\n        console.error('Error loading admin discs:', error);\n      } else {\n        console.log('Admin discs loaded:', data);\n        setAllDiscs(data || []);\n      }\n    } catch (error) {\n      console.error('Error loading admin discs:', error);\n    } finally {\n      setIsLoading(false);\n    }\n  };\n  React.useEffect(() => {\n    loadAllDiscs();\n  }, []);\n  const handleReturnStatusUpdate = (discId, newStatus) => {\n    setAllDiscs(prev => prev.map(disc => disc.id === discId ? {\n      ...disc,\n      return_status: newStatus,\n      returned_at: new Date().toISOString()\n    } : disc));\n  };\n  const filteredDiscs = filter === 'All' ? allDiscs : allDiscs.filter(disc => disc.return_status === filter);\n  const statusCounts = allDiscs.reduce((counts, disc) => {\n    const status = disc.return_status || 'Found';\n    counts[status] = (counts[status] || 0) + 1;\n    return counts;\n  }, {});\n\n  // Redirect if not admin\n  if (userRole !== 'admin') {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"form-container\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"form-header\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"back-button\",\n          onClick: () => onNavigate('home'),\n          children: \"\\u2190 Back to Home\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 194,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n          children: \"Access Denied\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 197,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"You need admin privileges to access this page.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 198,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 193,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 192,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"form-container\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"form-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n        className: \"back-button\",\n        onClick: () => onNavigate('home'),\n        children: \"\\u2190 Back to Home\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 207,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n        children: \"Admin Dashboard\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 210,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"Manage all found discs and their return status.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 211,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 206,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"admin-actions\",\n      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n        className: \"button primary\",\n        onClick: () => onNavigate('admin-bulk-turnins'),\n        children: \"Manage Bulk Turn-Ins\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 216,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        className: \"button primary\",\n        onClick: () => setShowSourceManager(true),\n        children: \"Manage Sources\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 222,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 215,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"status-summary\",\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        children: \"Status Summary\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 232,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"status-counts\",\n        children: Object.entries(statusCounts).map(([status, count]) => /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"status-count\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"count\",\n            children: count\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 236,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"status\",\n            children: status\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 237,\n            columnNumber: 15\n          }, this)]\n        }, status, true, {\n          fileName: _jsxFileName,\n          lineNumber: 235,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 233,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 231,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"filter-controls\",\n      children: [/*#__PURE__*/_jsxDEV(\"label\", {\n        htmlFor: \"status-filter\",\n        children: \"Filter by Status:\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 245,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n        id: \"status-filter\",\n        value: filter,\n        onChange: e => setFilter(e.target.value),\n        children: [/*#__PURE__*/_jsxDEV(\"option\", {\n          value: \"All\",\n          children: \"All Statuses\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 251,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n          value: \"Found\",\n          children: \"Found\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 252,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n          value: \"Returned to Owner\",\n          children: \"Returned to Owner\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 253,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n          value: \"Donated\",\n          children: \"Donated\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 254,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n          value: \"Sold\",\n          children: \"Sold\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 255,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n          value: \"Trashed\",\n          children: \"Trashed\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 256,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 246,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 244,\n      columnNumber: 7\n    }, this), isLoading ? /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"loading-container\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"loading-spinner\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 263,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"Loading discs...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 264,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 262,\n      columnNumber: 9\n    }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"admin-disc-grid\",\n      children: filteredDiscs.length === 0 ? /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"No discs found for the selected filter.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 269,\n        columnNumber: 13\n      }, this) : filteredDiscs.map(disc => /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"admin-disc-card\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"disc-header\",\n          children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n            children: [disc.brand, \" \", disc.mold || 'Unknown Mold']\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 274,\n            columnNumber: 19\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"disc-meta\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"disc-type\",\n              children: disc.disc_type || 'Unknown Type'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 276,\n              columnNumber: 21\n            }, this), disc.rack_id && /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"rack-id\",\n              children: [\"Rack #\", disc.rack_id]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 277,\n              columnNumber: 38\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 275,\n            columnNumber: 19\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 273,\n          columnNumber: 17\n        }, this), /*#__PURE__*/_jsxDEV(ReturnStatusManager, {\n          discId: disc.id,\n          currentStatus: disc.return_status || 'Found',\n          onStatusUpdated: newStatus => handleReturnStatusUpdate(disc.id, newStatus),\n          disabled: false\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 281,\n          columnNumber: 17\n        }, this), disc.image_urls && disc.image_urls.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"disc-images\",\n          children: disc.image_urls.slice(0, 2).map((imageUrl, index) => /*#__PURE__*/_jsxDEV(\"img\", {\n            src: imageUrl,\n            alt: `${disc.brand} ${disc.mold || 'disc'} ${index + 1}`,\n            className: \"disc-image\",\n            onError: e => {\n              e.target.style.display = 'none';\n            }\n          }, index, false, {\n            fileName: _jsxFileName,\n            lineNumber: 291,\n            columnNumber: 23\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 289,\n          columnNumber: 19\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"disc-details\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"detail-row\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"label\",\n              children: \"Color:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 306,\n              columnNumber: 21\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"value\",\n              children: disc.color\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 307,\n              columnNumber: 21\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 305,\n            columnNumber: 19\n          }, this), disc.source_name && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"detail-row\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"label\",\n              children: \"Source:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 311,\n              columnNumber: 23\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"value\",\n              children: disc.source_name\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 312,\n              columnNumber: 23\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 310,\n            columnNumber: 21\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"detail-row\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"label\",\n              children: \"Specific Location:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 316,\n              columnNumber: 21\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"value\",\n              children: disc.location_found\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 317,\n              columnNumber: 21\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 315,\n            columnNumber: 19\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"detail-row\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"label\",\n              children: \"Found Date:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 320,\n              columnNumber: 21\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"value\",\n              children: new Date(disc.found_date).toLocaleDateString()\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 321,\n              columnNumber: 21\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 319,\n            columnNumber: 19\n          }, this), disc.returned_at && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"detail-row\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"label\",\n              children: \"Returned Date:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 325,\n              columnNumber: 23\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"value\",\n              children: new Date(disc.returned_at).toLocaleDateString()\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 326,\n              columnNumber: 23\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 324,\n            columnNumber: 21\n          }, this), disc.returned_notes && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"detail-row\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"label\",\n              children: \"Notes:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 331,\n              columnNumber: 23\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"value\",\n              children: disc.returned_notes\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 332,\n              columnNumber: 23\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 330,\n            columnNumber: 21\n          }, this), disc.contact_attempts_count !== undefined && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"detail-row\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"label\",\n              children: \"Contact Attempts:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 337,\n              columnNumber: 23\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"value\",\n              children: [disc.contact_attempts_count, disc.last_contact_date && /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-sm text-gray-500 ml-2\",\n                children: [\"(Last: \", new Date(disc.last_contact_date).toLocaleDateString(), \")\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 341,\n                columnNumber: 27\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 338,\n              columnNumber: 23\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 336,\n            columnNumber: 21\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 304,\n          columnNumber: 17\n        }, this), /*#__PURE__*/_jsxDEV(ContactAttempts, {\n          discId: disc.id,\n          onContactAdded: () => loadAllDiscs()\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 351,\n          columnNumber: 17\n        }, this)]\n      }, disc.id, true, {\n        fileName: _jsxFileName,\n        lineNumber: 272,\n        columnNumber: 15\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 267,\n      columnNumber: 9\n    }, this), showSourceManager && /*#__PURE__*/_jsxDEV(SourceManager, {\n      onClose: () => setShowSourceManager(false)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 363,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 205,\n    columnNumber: 5\n  }, this);\n}\n_s2(AdminDashboard, \"uB+PH3puc50TZS8kj7F3Mk5SdKA=\", false, function () {\n  return [useAuth];\n});\n_c3 = AdminDashboard;\nfunction Home({\n  onNavigate\n}) {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"hero\",\n      children: [/*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"The DZDiscFinder helps disc golf players reunite with their lost discs, report found discs, orsearch for your lost discs in our database.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 373,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"hero-buttons\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"hero-button secondary\",\n          onClick: () => onNavigate('search-lost'),\n          children: \"Search\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 378,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"hero-button primary\",\n          onClick: () => onNavigate('report-found'),\n          children: \"Found\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 381,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 377,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 372,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"features\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"feature-card\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"feature-icon\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            children: \"\\uD83D\\uDD0D\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 391,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 390,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n          children: \"Smart Matching\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 393,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Our intelligent system matches found and lost discs based on brand, model, color, and location.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 394,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 389,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"feature-card\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"feature-icon\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            children: \"\\uD83D\\uDCCD\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 401,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 400,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n          children: \"Location Based\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 403,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Find discs near where you lost them with our location-based search and matching.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 404,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 399,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"feature-card\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"feature-icon\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            children: \"\\uD83D\\uDCAC\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 411,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 410,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n          children: \"Easy Communication\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 413,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Connect directly with finders and owners through our secure messaging system.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 414,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 409,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 388,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"stats\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"stats-grid\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-item\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"stat-number\",\n            children: \"500+\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 423,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"stat-label\",\n            children: \"Discs Reunited\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 424,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 422,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-item\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"stat-number\",\n            children: \"1,200+\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 427,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"stat-label\",\n            children: \"Active Users\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 428,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 426,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-item\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"stat-number\",\n            children: \"95%\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 431,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"stat-label\",\n            children: \"Success Rate\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 432,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 430,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 421,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 420,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"cta\",\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        children: \"Join the Community\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 438,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"Create an account to report found discs, search for lost ones, and help fellow disc golfers.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 439,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        className: \"cta-button\",\n        onClick: () => onNavigate('login'),\n        children: \"Sign Up Now\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 442,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 437,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 371,\n    columnNumber: 5\n  }, this);\n}\n_c4 = Home;\nfunction ReportFound({\n  onNavigate\n}) {\n  _s3();\n  const {\n    user,\n    isGuest\n  } = useAuth();\n\n  // Get today's date in YYYY-MM-DD format\n  const getTodayDate = () => {\n    const today = new Date();\n    return today.toISOString().split('T')[0];\n  };\n  const [formData, setFormData] = useState({\n    brand: '',\n    mold: '',\n    discType: '',\n    color: '',\n    weight: '',\n    condition: '',\n    plasticType: '',\n    stampText: '',\n    phoneNumber: '',\n    nameOnDisc: '',\n    sourceId: '',\n    locationFound: 'Exact location unknown.',\n    foundDate: getTodayDate(),\n    description: ''\n  });\n  const [selectedImages, setSelectedImages] = useState([]);\n  const [isSubmitting, setIsSubmitting] = useState(false);\n  const [submitMessage, setSubmitMessage] = useState('');\n  const [sources, setSources] = useState([]);\n\n  // Load active sources for the dropdown and user's default source\n  useEffect(() => {\n    const loadSourcesAndDefaults = async () => {\n      try {\n        const activeSources = await supabaseService.getActiveSources();\n        setSources(activeSources);\n\n        // If user is authenticated, load their default source\n        if (user !== null && user !== void 0 && user.id) {\n          const defaultSource = await supabaseService.getUserDefaultSource(user.id);\n          if (defaultSource) {\n            setFormData(prev => ({\n              ...prev,\n              sourceId: defaultSource.id\n            }));\n          }\n        }\n      } catch (error) {\n        console.error('Error loading sources:', error);\n      }\n    };\n    loadSourcesAndDefaults();\n  }, [user === null || user === void 0 ? void 0 : user.id]);\n\n  // Require authentication to report found discs\n  if (isGuest) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"page-container\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"page-header\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"back-button\",\n          onClick: () => onNavigate('home'),\n          children: \"\\u2190 Back to Home\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 510,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n          children: \"Report a Found Disc\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 513,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"You must be signed in to report found discs.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 514,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 509,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"auth-required\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          children: \"Authentication Required\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 517,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Please sign in or create an account to report found discs. This helps us maintain data quality and allows disc owners to contact you.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 518,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"button primary\",\n          onClick: () => onNavigate('login'),\n          children: \"Sign In / Sign Up\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 519,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 516,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 508,\n      columnNumber: 7\n    }, this);\n  }\n  const handleInputChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: value\n    }));\n  };\n  const handleSubmit = async e => {\n    e.preventDefault();\n    setIsSubmitting(true);\n    setSubmitMessage('');\n    try {\n      console.log('Form submission started');\n      console.log('User:', user);\n      console.log('Selected images:', selectedImages.length);\n\n      // Check if user is authenticated for image upload\n      if (selectedImages.length > 0 && !user) {\n        setSubmitMessage('Error: You must be signed in to upload images');\n        return;\n      }\n\n      // Test connection first\n      const {\n        connected\n      } = await discService.testConnection();\n      console.log('Supabase connection test:', connected);\n      if (!connected) {\n        setSubmitMessage('Demo Mode: Form data logged to console (Supabase not configured)');\n        console.log('Form data:', formData, 'Images:', selectedImages.length);\n        setTimeout(() => {\n          onNavigate('home');\n        }, 2000);\n        return;\n      }\n      let imageUrls = [];\n\n      // Upload images if any are selected\n      if (selectedImages.length > 0) {\n        console.log('Starting image upload for user:', user.id);\n        setSubmitMessage('Uploading images...');\n        const {\n          urls,\n          error: imageError\n        } = await imageService.uploadImages(selectedImages, user.id);\n        console.log('Image upload result:', {\n          urls,\n          error: imageError\n        });\n        if (imageError) {\n          console.error('Image upload error:', imageError);\n          setSubmitMessage(`Error uploading images: ${imageError.message || JSON.stringify(imageError)}`);\n          return;\n        }\n        imageUrls = urls;\n        console.log('Images uploaded successfully:', imageUrls);\n      }\n\n      // Prepare data for Supabase\n      const discData = {\n        finder_id: user.id,\n        // Use authenticated user's ID\n        brand: formData.brand,\n        mold: formData.mold || undefined,\n        disc_type: formData.discType || undefined,\n        color: formData.color,\n        weight: formData.weight ? parseInt(formData.weight) : undefined,\n        condition: formData.condition || undefined,\n        plastic_type: formData.plasticType || undefined,\n        stamp_text: formData.stampText || undefined,\n        phone_number: formData.phoneNumber || undefined,\n        name_on_disc: formData.nameOnDisc || undefined,\n        source_id: formData.sourceId || undefined,\n        location_found: formData.locationFound,\n        found_date: formData.foundDate,\n        description: formData.description || undefined,\n        image_urls: imageUrls.length > 0 ? imageUrls : undefined\n      };\n      setSubmitMessage('Saving disc information...');\n      const {\n        data,\n        error\n      } = await discService.createFoundDisc(discData);\n      if (error) {\n        // If disc creation failed but images were uploaded, clean up the images\n        if (imageUrls.length > 0) {\n          await imageService.deleteImages(imageUrls);\n        }\n        setSubmitMessage(`Error: ${(error === null || error === void 0 ? void 0 : error.message) || 'Unknown error occurred'}`);\n      } else {\n        setSubmitMessage('Found disc reported successfully!');\n        console.log('Saved disc:', data);\n        setTimeout(() => {\n          onNavigate('home');\n        }, 2000);\n      }\n    } catch (error) {\n      setSubmitMessage('Demo Mode: Form data logged to console (Supabase not configured)');\n      console.log('Form data:', formData, 'Images:', selectedImages.length);\n      setTimeout(() => {\n        onNavigate('home');\n      }, 2000);\n    } finally {\n      setIsSubmitting(false);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"form-container\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"form-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n        className: \"back-button\",\n        onClick: () => onNavigate('home'),\n        children: \"\\u2190 Back to Home\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 634,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n        children: \"Report a Found Disc\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 637,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"Help reunite a disc with its owner by providing details about the disc you found.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 638,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 633,\n      columnNumber: 7\n    }, this), !user && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"auth-notice\",\n      children: [/*#__PURE__*/_jsxDEV(\"p\", {\n        children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n          children: \"Sign in required:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 644,\n          columnNumber: 13\n        }, this), \" You need to be signed in to report found discs and upload images.\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 643,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        className: \"button primary\",\n        onClick: () => onNavigate('login'),\n        children: \"Sign In\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 646,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 642,\n      columnNumber: 9\n    }, this), submitMessage && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: `status-message ${submitMessage.includes('Error') ? 'error' : 'success'}`,\n      children: submitMessage\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 656,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n      className: \"disc-form\",\n      onSubmit: handleSubmit,\n      style: {\n        opacity: !user ? 0.6 : 1\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"form-section\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          children: \"Disc Information\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 663,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-row\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"brand\",\n              children: \"Brand *\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 666,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              id: \"brand\",\n              name: \"brand\",\n              value: formData.brand,\n              onChange: handleInputChange,\n              required: true,\n              placeholder: \"e.g., Innova, Discraft, Dynamic Discs\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 667,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 665,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"mold\",\n              children: \"Mold *\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 678,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              id: \"mold\",\n              name: \"mold\",\n              value: formData.mold,\n              onChange: handleInputChange,\n              required: true,\n              placeholder: \"e.g., Destroyer, Buzzz, Judge\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 679,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 677,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 664,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-row\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"discType\",\n              children: \"Disc Type\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 693,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              id: \"discType\",\n              name: \"discType\",\n              value: formData.discType,\n              onChange: handleInputChange,\n              placeholder: \"e.g., Putter, Midrange, Fairway Driver, Distance Driver\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 694,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 692,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"color\",\n              children: \"Color *\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 704,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              id: \"color\",\n              name: \"color\",\n              value: formData.color,\n              onChange: handleInputChange,\n              required: true,\n              placeholder: \"e.g., Blue, Red, Orange\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 705,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 703,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 691,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-row\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"weight\",\n              children: \"Weight (grams)\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 719,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"number\",\n              id: \"weight\",\n              name: \"weight\",\n              value: formData.weight,\n              onChange: handleInputChange,\n              placeholder: \"e.g., 175\",\n              min: \"100\",\n              max: \"200\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 720,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 718,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"condition\",\n              children: \"Condition\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 732,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              id: \"condition\",\n              name: \"condition\",\n              value: formData.condition,\n              onChange: handleInputChange,\n              placeholder: \"e.g., New, Excellent, Good, Fair, Poor\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 733,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 731,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 717,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 662,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"form-section\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          children: \"Additional Details\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 746,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-row\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"plasticType\",\n              children: \"Plastic Type\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 749,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              id: \"plasticType\",\n              name: \"plasticType\",\n              value: formData.plasticType,\n              onChange: handleInputChange,\n              placeholder: \"e.g., Champion, ESP, Lucid\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 750,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 748,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"stampText\",\n              children: \"Stamp/Text\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 760,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              id: \"stampText\",\n              name: \"stampText\",\n              value: formData.stampText,\n              onChange: handleInputChange,\n              placeholder: \"Any text or stamps on the disc\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 761,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 759,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 747,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-row\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"phoneNumber\",\n              children: \"Phone Number on Disc\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 774,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"tel\",\n              id: \"phoneNumber\",\n              name: \"phoneNumber\",\n              value: formData.phoneNumber,\n              onChange: handleInputChange,\n              placeholder: \"Phone number written on disc\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 775,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 773,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"nameOnDisc\",\n              children: \"Name on Disc\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 785,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              id: \"nameOnDisc\",\n              name: \"nameOnDisc\",\n              value: formData.nameOnDisc,\n              onChange: handleInputChange,\n              placeholder: \"Name written on disc\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 786,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 784,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 772,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 745,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"form-section\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          children: \"Location & Date\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 799,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-row\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"sourceId\",\n              children: \"Source *\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 802,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n              id: \"sourceId\",\n              name: \"sourceId\",\n              value: formData.sourceId,\n              onChange: handleInputChange,\n              required: true,\n              className: \"form-select\",\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"\",\n                children: \"Select where disc was found...\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 811,\n                columnNumber: 17\n              }, this), sources.map(source => /*#__PURE__*/_jsxDEV(\"option\", {\n                value: source.id,\n                children: source.name\n              }, source.id, false, {\n                fileName: _jsxFileName,\n                lineNumber: 813,\n                columnNumber: 19\n              }, this))]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 803,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n              className: \"form-help\",\n              children: \"Choose the general location or event where the disc was found\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 818,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 801,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"foundDate\",\n              children: \"Date Found *\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 821,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"date\",\n              id: \"foundDate\",\n              name: \"foundDate\",\n              value: formData.foundDate,\n              onChange: handleInputChange,\n              required: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 822,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 820,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 800,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-row\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"locationFound\",\n              children: \"Specific Location *\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 834,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              id: \"locationFound\",\n              name: \"locationFound\",\n              value: formData.locationFound,\n              onChange: handleInputChange,\n              required: true,\n              placeholder: \"e.g., East Pond, Hole 7, Near the basket (or leave default)\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 835,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n              className: \"form-help\",\n              children: \"Provide specific details about where within the source location. Defaults to \\\"Exact location unknown.\\\" if not changed.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 844,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 833,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 832,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            htmlFor: \"description\",\n            children: \"Additional Description\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 849,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n            id: \"description\",\n            name: \"description\",\n            value: formData.description,\n            onChange: handleInputChange,\n            rows: 4,\n            placeholder: \"Any additional details about where or how you found the disc...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 850,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 848,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 798,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"form-section\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          children: \"Disc Images\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 862,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"form-section-description\",\n          children: \"Adding photos helps disc owners identify their disc more easily. You can upload up to 2 images.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 863,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(ImageUpload, {\n          onImagesChange: setSelectedImages,\n          maxImages: 2,\n          maxSizePerImage: 10,\n          disabled: isSubmitting\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 866,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 861,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"form-actions\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          type: \"button\",\n          className: \"button secondary\",\n          onClick: () => onNavigate('home'),\n          disabled: isSubmitting,\n          children: \"Cancel\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 875,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          type: \"submit\",\n          className: \"button primary\",\n          disabled: isSubmitting || !user,\n          children: isSubmitting ? 'Submitting...' : !user ? 'Sign In Required' : 'Report Found Disc'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 883,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 874,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 661,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 632,\n    columnNumber: 5\n  }, this);\n}\n_s3(ReportFound, \"ER27boObYpTa16+9wI3Kq97Id7I=\", false, function () {\n  return [useAuth];\n});\n_c5 = ReportFound;\nfunction SearchLost({\n  onNavigate\n}) {\n  _s4();\n  const {\n    userRole\n  } = useAuth();\n  const [searchCriteria, setSearchCriteria] = useState({\n    brand: '',\n    mold: '',\n    color: '',\n    discType: '',\n    locationFound: '',\n    rackId: ''\n  });\n  const [foundDiscs, setFoundDiscs] = useState([]);\n  const [isSearching, setIsSearching] = useState(false);\n  const [hasSearched, setHasSearched] = useState(false);\n  const handleReturnStatusUpdate = (discId, newStatus) => {\n    // Update the disc in the local state\n    setFoundDiscs(prev => prev.map(disc => disc.id === discId ? {\n      ...disc,\n      return_status: newStatus,\n      returned_at: new Date().toISOString()\n    } : disc));\n  };\n  const handleInputChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n    setSearchCriteria(prev => ({\n      ...prev,\n      [name]: value\n    }));\n  };\n  const handleSearch = async e => {\n    e.preventDefault();\n    setIsSearching(true);\n    setHasSearched(true);\n    try {\n      const {\n        data,\n        error\n      } = await discService.searchFoundDiscs(searchCriteria);\n      if (error) {\n        console.error('Search error:', error);\n        setFoundDiscs([]);\n      } else {\n        console.log('Search results:', data);\n        setFoundDiscs(data || []);\n      }\n    } catch (error) {\n      console.error('Search failed:', error);\n      setFoundDiscs([]);\n    } finally {\n      setIsSearching(false);\n    }\n  };\n  const loadAllDiscs = async () => {\n    setIsSearching(true);\n    setHasSearched(true);\n    try {\n      const {\n        data,\n        error\n      } = await discService.getFoundDiscs();\n      if (error) {\n        console.error('Load error:', error);\n        setFoundDiscs([]);\n      } else {\n        console.log('Load all results:', data);\n        setFoundDiscs(data || []);\n      }\n    } catch (error) {\n      console.error('Load failed:', error);\n      setFoundDiscs([]);\n    } finally {\n      setIsSearching(false);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"page-container\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"page-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n        className: \"back-button\",\n        onClick: () => onNavigate('home'),\n        children: \"\\u2190 Back to Home\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 975,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n        children: \"Search Lost Discs\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 978,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"Search through reported found discs to see if someone has found your lost disc.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 979,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 974,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"search-container\",\n      children: [/*#__PURE__*/_jsxDEV(\"form\", {\n        className: \"search-form\",\n        onSubmit: handleSearch,\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"search-section\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"Search Criteria\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 985,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-row\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                htmlFor: \"search-rack-id\",\n                children: \"Rack ID\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 988,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"number\",\n                id: \"search-rack-id\",\n                name: \"rackId\",\n                value: searchCriteria.rackId,\n                onChange: handleInputChange,\n                placeholder: \"e.g., 417, 555, 7338\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 989,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 987,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                htmlFor: \"search-brand\",\n                children: \"Brand\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 999,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                id: \"search-brand\",\n                name: \"brand\",\n                value: searchCriteria.brand,\n                onChange: handleInputChange,\n                placeholder: \"e.g., Innova, Discraft\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1000,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 998,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 986,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-row\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                htmlFor: \"search-mold\",\n                children: \"Mold\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1013,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                id: \"search-mold\",\n                name: \"mold\",\n                value: searchCriteria.mold,\n                onChange: handleInputChange,\n                placeholder: \"e.g., Destroyer, Buzzz\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1014,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1012,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1011,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-row\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                htmlFor: \"search-color\",\n                children: \"Color\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1027,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                id: \"search-color\",\n                name: \"color\",\n                value: searchCriteria.color,\n                onChange: handleInputChange,\n                placeholder: \"e.g., Blue, Red\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1028,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1026,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                htmlFor: \"search-discType\",\n                children: \"Disc Type\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1038,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                id: \"search-discType\",\n                name: \"discType\",\n                value: searchCriteria.discType,\n                onChange: handleInputChange,\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"\",\n                  children: \"Any type\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1045,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"putter\",\n                  children: \"Putter\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1046,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"midrange\",\n                  children: \"Midrange\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1047,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"fairway_driver\",\n                  children: \"Fairway Driver\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1048,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"distance_driver\",\n                  children: \"Distance Driver\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1049,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"approach\",\n                  children: \"Approach\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1050,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1039,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1037,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1025,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"search-location\",\n              children: \"Location\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1056,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              id: \"search-location\",\n              name: \"locationFound\",\n              value: searchCriteria.locationFound,\n              onChange: handleInputChange,\n              placeholder: \"e.g., Maple Hill, DeLaveaga\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1057,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1055,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 984,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"search-actions\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"button\",\n            className: \"button secondary\",\n            onClick: loadAllDiscs,\n            disabled: isSearching,\n            children: isSearching ? 'Loading...' : 'Show All Found Discs'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1069,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"submit\",\n            className: \"button primary\",\n            disabled: isSearching,\n            children: isSearching ? 'Searching...' : 'Search'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1077,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1068,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 983,\n        columnNumber: 9\n      }, this), hasSearched && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"search-results\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          children: foundDiscs.length > 0 ? `Found ${foundDiscs.length} disc${foundDiscs.length === 1 ? '' : 's'}` : 'No discs found matching your criteria'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1089,\n          columnNumber: 13\n        }, this), foundDiscs.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"disc-grid\",\n          children: foundDiscs.map(disc => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"disc-card\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"disc-header\",\n              children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                children: [disc.brand, \" \", disc.mold || 'Unknown Mold']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1101,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"disc-meta\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"disc-type\",\n                  children: disc.disc_type || 'Unknown Type'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1103,\n                  columnNumber: 25\n                }, this), disc.rack_id && /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"rack-id\",\n                  children: [\"Rack #\", disc.rack_id]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1104,\n                  columnNumber: 42\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1102,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1100,\n              columnNumber: 21\n            }, this), (userRole === 'admin' || disc.return_status && disc.return_status !== 'Found') && /*#__PURE__*/_jsxDEV(ReturnStatusManager, {\n              discId: disc.id,\n              currentStatus: disc.return_status || 'Found',\n              onStatusUpdated: newStatus => handleReturnStatusUpdate(disc.id, newStatus),\n              disabled: userRole !== 'admin'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1110,\n              columnNumber: 23\n            }, this), disc.image_urls && disc.image_urls.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"disc-images\",\n              children: disc.image_urls.slice(0, 2).map((imageUrl, index) => /*#__PURE__*/_jsxDEV(\"img\", {\n                src: imageUrl,\n                alt: `${disc.brand} ${disc.mold || 'disc'} ${index + 1}`,\n                className: \"disc-image\",\n                onError: e => {\n                  // Hide broken images\n                  e.target.style.display = 'none';\n                }\n              }, index, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1121,\n                columnNumber: 27\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1119,\n              columnNumber: 23\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"disc-details\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"detail-row\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"label\",\n                  children: \"Color:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1137,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"value\",\n                  children: disc.color\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1138,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1136,\n                columnNumber: 23\n              }, this), disc.weight && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"detail-row\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"label\",\n                  children: \"Weight:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1143,\n                  columnNumber: 27\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"value\",\n                  children: [disc.weight, \"g\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1144,\n                  columnNumber: 27\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1142,\n                columnNumber: 25\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"detail-row\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"label\",\n                  children: \"Condition:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1149,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"value\",\n                  children: disc.condition || 'Unknown'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1150,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1148,\n                columnNumber: 23\n              }, this), disc.source_name && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"detail-row\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"label\",\n                  children: \"Source:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1155,\n                  columnNumber: 27\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"value\",\n                  children: disc.source_name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1156,\n                  columnNumber: 27\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1154,\n                columnNumber: 25\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"detail-row\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"label\",\n                  children: \"Specific Location:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1161,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"value\",\n                  children: disc.location_found\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1162,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1160,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"detail-row\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"label\",\n                  children: \"Found on:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1166,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"value\",\n                  children: new Date(disc.found_date).toLocaleDateString()\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1167,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1165,\n                columnNumber: 23\n              }, this), disc.phone_number && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"detail-row\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"label\",\n                  children: \"Phone on disc:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1172,\n                  columnNumber: 27\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"value\",\n                  children: disc.phone_number\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1173,\n                  columnNumber: 27\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1171,\n                columnNumber: 25\n              }, this), disc.name_on_disc && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"detail-row\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"label\",\n                  children: \"Name on disc:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1179,\n                  columnNumber: 27\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"value\",\n                  children: disc.name_on_disc\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1180,\n                  columnNumber: 27\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1178,\n                columnNumber: 25\n              }, this), disc.description && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"detail-row\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"label\",\n                  children: \"Description:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1186,\n                  columnNumber: 27\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"value\",\n                  children: disc.description\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1187,\n                  columnNumber: 27\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1185,\n                columnNumber: 25\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1135,\n              columnNumber: 21\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"disc-actions\",\n              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"button primary small\",\n                children: \"Contact Finder\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1193,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"button secondary small\",\n                children: \"Report as Mine\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1196,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1192,\n              columnNumber: 21\n            }, this)]\n          }, disc.id, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1099,\n            columnNumber: 19\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1097,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1088,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 982,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 973,\n    columnNumber: 5\n  }, this);\n}\n_s4(SearchLost, \"0H+xQRtyCdOFKoDOxnhsbdd/+9U=\", false, function () {\n  return [useAuth];\n});\n_c6 = SearchLost;\nfunction Login({\n  onNavigate\n}) {\n  _s5();\n  const {\n    signIn,\n    signUp\n  } = useAuth();\n  const [isLogin, setIsLogin] = useState(true);\n  const [formData, setFormData] = useState({\n    email: '',\n    password: '',\n    fullName: '',\n    confirmPassword: ''\n  });\n  const [loading, setLoading] = useState(false);\n  const [message, setMessage] = useState('');\n  const handleInputChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: value\n    }));\n  };\n  const handleSubmit = async e => {\n    e.preventDefault();\n    setLoading(true);\n    setMessage('');\n    try {\n      if (isLogin) {\n        const {\n          error\n        } = await signIn(formData.email, formData.password);\n        if (error) {\n          setMessage(error.message);\n        } else {\n          setMessage('Signed in successfully!');\n          setTimeout(() => onNavigate('home'), 1000);\n        }\n      } else {\n        if (formData.password !== formData.confirmPassword) {\n          setMessage('Passwords do not match');\n          setLoading(false);\n          return;\n        }\n        if (formData.password.length < 6) {\n          setMessage('Password must be at least 6 characters');\n          setLoading(false);\n          return;\n        }\n        const {\n          error\n        } = await signUp(formData.email, formData.password, formData.fullName);\n        if (error) {\n          setMessage(error.message);\n        } else {\n          setMessage('Account created! Please check your email to verify your account.');\n        }\n      }\n    } catch (error) {\n      setMessage('An unexpected error occurred');\n    } finally {\n      setLoading(false);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"page-container\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"page-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n        className: \"back-button\",\n        onClick: () => onNavigate('home'),\n        children: \"\\u2190 Back to Home\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1274,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n        children: isLogin ? 'Sign In' : 'Create Account'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1277,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: isLogin ? 'Sign in to your account' : 'Create an account to report and search for discs'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1278,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1273,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"auth-container\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"auth-tabs\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          className: `auth-tab ${isLogin ? 'active' : ''}`,\n          onClick: () => setIsLogin(true),\n          children: \"Sign In\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1283,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: `auth-tab ${!isLogin ? 'active' : ''}`,\n          onClick: () => setIsLogin(false),\n          children: \"Sign Up\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1289,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1282,\n        columnNumber: 9\n      }, this), message && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: `status-message ${message.includes('error') || message.includes('Error') ? 'error' : 'success'}`,\n        children: message\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1298,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n        className: \"auth-form\",\n        onSubmit: handleSubmit,\n        children: [!isLogin && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            htmlFor: \"fullName\",\n            children: \"Full Name\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1306,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            id: \"fullName\",\n            name: \"fullName\",\n            value: formData.fullName,\n            onChange: handleInputChange,\n            required: !isLogin,\n            placeholder: \"Your full name\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1307,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1305,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            htmlFor: \"email\",\n            children: \"Email\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1320,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"email\",\n            id: \"email\",\n            name: \"email\",\n            value: formData.email,\n            onChange: handleInputChange,\n            required: true,\n            placeholder: \"<EMAIL>\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1321,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1319,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            htmlFor: \"password\",\n            children: \"Password\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1333,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"password\",\n            id: \"password\",\n            name: \"password\",\n            value: formData.password,\n            onChange: handleInputChange,\n            required: true,\n            placeholder: isLogin ? \"Your password\" : \"At least 6 characters\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1334,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1332,\n          columnNumber: 11\n        }, this), !isLogin && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            htmlFor: \"confirmPassword\",\n            children: \"Confirm Password\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1347,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"password\",\n            id: \"confirmPassword\",\n            name: \"confirmPassword\",\n            value: formData.confirmPassword,\n            onChange: handleInputChange,\n            required: !isLogin,\n            placeholder: \"Confirm your password\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1348,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1346,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          type: \"submit\",\n          className: \"button primary full-width\",\n          disabled: loading,\n          children: loading ? 'Please wait...' : isLogin ? 'Sign In' : 'Create Account'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1360,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1303,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1281,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 1272,\n    columnNumber: 5\n  }, this);\n}\n_s5(Login, \"DtWoNHOVg6rzGvvHVdAJmIhKXiE=\", false, function () {\n  return [useAuth];\n});\n_c7 = Login;\nexport default App;\nvar _c, _c2, _c3, _c4, _c5, _c6, _c7;\n$RefreshReg$(_c, \"AppContent\");\n$RefreshReg$(_c2, \"App\");\n$RefreshReg$(_c3, \"AdminDashboard\");\n$RefreshReg$(_c4, \"Home\");\n$RefreshReg$(_c5, \"ReportFound\");\n$RefreshReg$(_c6, \"SearchLost\");\n$RefreshReg$(_c7, \"Login\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "discService", "imageService", "supabaseService", "<PERSON>th<PERSON><PERSON><PERSON>", "useAuth", "ImageUpload", "ReturnStatusManager", "ContactAttempts", "RakerDiverDashboard", "AdminBulkTurnins", "ProfileImportManager", "ProfileManager", "PhotoMigrationManager", "SourceManager", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "A<PERSON><PERSON><PERSON>nt", "_s", "currentPage", "setCurrentPage", "user", "userRole", "signOut", "loading", "handleNavigate", "page", "renderPage", "Home", "onNavigate", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "ReportFound", "SearchLost", "<PERSON><PERSON>", "AdminDashboard", "userId", "id", "handleSignOut", "className", "children", "style", "fontSize", "color", "marginTop", "onClick", "cursor", "email", "_c", "App", "_c2", "_s2", "allDiscs", "setAllDiscs", "isLoading", "setIsLoading", "filter", "setFilter", "showSourceManager", "setShowSourceManager", "loadAllDiscs", "data", "error", "getAdminFoundDiscs", "console", "log", "handleReturnStatusUpdate", "discId", "newStatus", "prev", "map", "disc", "return_status", "returned_at", "Date", "toISOString", "filteredDiscs", "statusCounts", "reduce", "counts", "status", "Object", "entries", "count", "htmlFor", "value", "onChange", "e", "target", "length", "brand", "mold", "disc_type", "rack_id", "currentStatus", "onStatusUpdated", "disabled", "image_urls", "slice", "imageUrl", "index", "src", "alt", "onError", "display", "source_name", "location_found", "found_date", "toLocaleDateString", "returned_notes", "contact_attempts_count", "undefined", "last_contact_date", "onContactAdded", "onClose", "_c3", "_c4", "_s3", "isGuest", "getTodayDate", "today", "split", "formData", "setFormData", "discType", "weight", "condition", "plasticType", "stampText", "phoneNumber", "nameOnDisc", "sourceId", "locationFound", "foundDate", "description", "selectedImages", "setSelectedImages", "isSubmitting", "setIsSubmitting", "submitMessage", "setSubmitMessage", "sources", "setSources", "loadSourcesAndDefaults", "activeSources", "getActiveSources", "defaultSource", "getUserDefaultSource", "handleInputChange", "name", "handleSubmit", "preventDefault", "connected", "testConnection", "setTimeout", "imageUrls", "urls", "imageError", "uploadImages", "message", "JSON", "stringify", "discData", "finder_id", "parseInt", "plastic_type", "stamp_text", "phone_number", "name_on_disc", "source_id", "createFoundDisc", "deleteImages", "includes", "onSubmit", "opacity", "type", "required", "placeholder", "min", "max", "source", "rows", "onImagesChange", "maxImages", "maxSizePerImage", "_c5", "_s4", "searchCriteria", "setSearchCriteria", "rackId", "foundDiscs", "setFoundDiscs", "isSearching", "setIsSearching", "hasSearched", "setHasSearched", "handleSearch", "searchFoundDiscs", "getFoundDiscs", "_c6", "_s5", "signIn", "signUp", "is<PERSON>ogin", "setIsLogin", "password", "fullName", "confirmPassword", "setLoading", "setMessage", "_c7", "$RefreshReg$"], "sources": ["C:/Users/<USER>/lostandfound/discfinder-app/src/App.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { discService, imageService, ReturnStatus, Source, supabaseService } from './lib/supabase';\nimport { AuthProvider, useAuth } from './contexts/AuthContext';\nimport { ImageUpload } from './components/ImageUpload';\nimport { ReturnStatusManager } from './components/ReturnStatusManager';\nimport { ContactAttempts } from './components/ContactAttempts';\nimport { RakerDiverDashboard } from './components/RakerDiverDashboard';\nimport { AdminBulkTurnins } from './components/AdminBulkTurnins';\nimport ProfileImportManager from './components/ProfileImportManager';\nimport ProfileManager from './components/ProfileManager';\nimport PhotoMigrationManager from './components/PhotoMigrationManager';\nimport SourceManager from './components/SourceManager';\n\ntype Page = 'home' | 'report-found' | 'search-lost' | 'login' | 'admin' | 'rakerdiver' | 'admin-bulk-turnins' | 'profile-import' | 'profile' | 'photo-migration';\n\nfunction AppContent() {\n  const [currentPage, setCurrentPage] = useState<Page>('home');\n  const { user, userRole, signOut, loading } = useAuth();\n\n  const handleNavigate = (page: string) => {\n    setCurrentPage(page as Page);\n  };\n\n  const renderPage = () => {\n    switch (currentPage) {\n      case 'home':\n        return <Home onNavigate={setCurrentPage} />;\n      case 'report-found':\n        return <ReportFound onNavigate={setCurrentPage} />;\n      case 'search-lost':\n        return <SearchLost onNavigate={setCurrentPage} />;\n      case 'login':\n        return <Login onNavigate={setCurrentPage} />;\n      case 'admin':\n        return <AdminDashboard onNavigate={setCurrentPage} />;\n      case 'rakerdiver':\n        return <RakerDiverDashboard onNavigate={handleNavigate} />;\n      case 'admin-bulk-turnins':\n        return <AdminBulkTurnins onNavigate={handleNavigate} />;\n      case 'profile-import':\n        return <ProfileImportManager />;\n      case 'photo-migration':\n        return <PhotoMigrationManager />;\n      case 'profile':\n        return user ? <ProfileManager userId={user.id} /> : <Login onNavigate={setCurrentPage} />;\n      default:\n        return <Home onNavigate={setCurrentPage} />;\n    }\n  };\n\n  const handleSignOut = async () => {\n    await signOut();\n    setCurrentPage('home');\n  };\n\n  if (loading) {\n    return (\n      <div className=\"app\">\n        <div className=\"loading-container\">\n          <div className=\"loading-spinner\"></div>\n          <p>Loading...</p>\n          <p style={{ fontSize: '0.8rem', color: '#666', marginTop: '1rem' }}>\n            If this takes too long, check the browser console for errors\n          </p>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"app\">\n      <nav className=\"navbar\">\n        <div className=\"nav-container\">\n          <div className=\"logo\" onClick={() => setCurrentPage('home')} style={{ cursor: 'pointer' }}>\n            DiscFinder\n          </div>\n          <div className=\"nav-buttons\">\n            <button className=\"nav-button\" onClick={() => setCurrentPage('report-found')}>\n              Report Found\n            </button>\n            <button className=\"nav-button\" onClick={() => setCurrentPage('search-lost')}>\n              Search Lost\n            </button>\n\n            {user ? (\n              <div className=\"user-menu\">\n                <span className=\"user-info\">\n                  {user.email} ({userRole})\n                </span>\n                <button className=\"nav-button\" onClick={() => setCurrentPage('profile')}>\n                  Profile\n                </button>\n                {userRole === 'admin' && (\n                  <>\n                    <button className=\"nav-button\" onClick={() => setCurrentPage('admin')}>\n                      Admin\n                    </button>\n                    <button className=\"nav-button\" onClick={() => setCurrentPage('profile-import')}>\n                      Import\n                    </button>\n                    <button className=\"nav-button\" onClick={() => setCurrentPage('photo-migration')}>\n                      Photos\n                    </button>\n                  </>\n                )}\n                {userRole === 'rakerdiver' && (\n                  <button className=\"nav-button\" onClick={() => setCurrentPage('rakerdiver')}>\n                    RakerDiver\n                  </button>\n                )}\n                <button className=\"nav-button\" onClick={handleSignOut}>\n                  Sign Out\n                </button>\n              </div>\n            ) : (\n              <button className=\"nav-button primary\" onClick={() => setCurrentPage('login')}>\n                Sign In\n              </button>\n            )}\n          </div>\n        </div>\n      </nav>\n\n      <main className=\"main-container\">\n        {renderPage()}\n      </main>\n    </div>\n  );\n}\n\nfunction App() {\n  return (\n    <AuthProvider>\n      <AppContent />\n    </AuthProvider>\n  );\n}\n\ninterface PageProps {\n  onNavigate: (page: Page) => void;\n}\n\nfunction AdminDashboard({ onNavigate }: PageProps) {\n  const { userRole } = useAuth();\n  const [allDiscs, setAllDiscs] = useState<any[]>([]);\n  const [isLoading, setIsLoading] = useState(false);\n  const [filter, setFilter] = useState<ReturnStatus | 'All'>('All');\n  const [showSourceManager, setShowSourceManager] = useState(false);\n\n  const loadAllDiscs = async () => {\n    setIsLoading(true);\n    try {\n      const { data, error } = await discService.getAdminFoundDiscs();\n      if (error) {\n        console.error('Error loading admin discs:', error);\n      } else {\n        console.log('Admin discs loaded:', data);\n        setAllDiscs(data || []);\n      }\n    } catch (error) {\n      console.error('Error loading admin discs:', error);\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  React.useEffect(() => {\n    loadAllDiscs();\n  }, []);\n\n  const handleReturnStatusUpdate = (discId: string, newStatus: ReturnStatus) => {\n    setAllDiscs(prev => prev.map(disc =>\n      disc.id === discId\n        ? { ...disc, return_status: newStatus, returned_at: new Date().toISOString() }\n        : disc\n    ));\n  };\n\n  const filteredDiscs = filter === 'All'\n    ? allDiscs\n    : allDiscs.filter(disc => disc.return_status === filter);\n\n  const statusCounts = allDiscs.reduce((counts, disc) => {\n    const status = disc.return_status || 'Found';\n    counts[status] = (counts[status] || 0) + 1;\n    return counts;\n  }, {} as Record<string, number>);\n\n  // Redirect if not admin\n  if (userRole !== 'admin') {\n    return (\n      <div className=\"form-container\">\n        <div className=\"form-header\">\n          <button className=\"back-button\" onClick={() => onNavigate('home')}>\n            ← Back to Home\n          </button>\n          <h1>Access Denied</h1>\n          <p>You need admin privileges to access this page.</p>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"form-container\">\n      <div className=\"form-header\">\n        <button className=\"back-button\" onClick={() => onNavigate('home')}>\n          ← Back to Home\n        </button>\n        <h1>Admin Dashboard</h1>\n        <p>Manage all found discs and their return status.</p>\n      </div>\n\n      {/* Admin Actions */}\n      <div className=\"admin-actions\">\n        <button\n          className=\"button primary\"\n          onClick={() => onNavigate('admin-bulk-turnins')}\n        >\n          Manage Bulk Turn-Ins\n        </button>\n        <button\n          className=\"button primary\"\n          onClick={() => setShowSourceManager(true)}\n        >\n          Manage Sources\n        </button>\n      </div>\n\n      {/* Status Summary */}\n      <div className=\"status-summary\">\n        <h3>Status Summary</h3>\n        <div className=\"status-counts\">\n          {Object.entries(statusCounts).map(([status, count]) => (\n            <div key={status} className=\"status-count\">\n              <span className=\"count\">{count as number}</span>\n              <span className=\"status\">{status}</span>\n            </div>\n          ))}\n        </div>\n      </div>\n\n      {/* Filter Controls */}\n      <div className=\"filter-controls\">\n        <label htmlFor=\"status-filter\">Filter by Status:</label>\n        <select\n          id=\"status-filter\"\n          value={filter}\n          onChange={(e) => setFilter(e.target.value as ReturnStatus | 'All')}\n        >\n          <option value=\"All\">All Statuses</option>\n          <option value=\"Found\">Found</option>\n          <option value=\"Returned to Owner\">Returned to Owner</option>\n          <option value=\"Donated\">Donated</option>\n          <option value=\"Sold\">Sold</option>\n          <option value=\"Trashed\">Trashed</option>\n        </select>\n      </div>\n\n      {/* Disc List */}\n      {isLoading ? (\n        <div className=\"loading-container\">\n          <div className=\"loading-spinner\"></div>\n          <p>Loading discs...</p>\n        </div>\n      ) : (\n        <div className=\"admin-disc-grid\">\n          {filteredDiscs.length === 0 ? (\n            <p>No discs found for the selected filter.</p>\n          ) : (\n            filteredDiscs.map((disc) => (\n              <div key={disc.id} className=\"admin-disc-card\">\n                <div className=\"disc-header\">\n                  <h4>{disc.brand} {disc.mold || 'Unknown Mold'}</h4>\n                  <div className=\"disc-meta\">\n                    <span className=\"disc-type\">{disc.disc_type || 'Unknown Type'}</span>\n                    {disc.rack_id && <span className=\"rack-id\">Rack #{disc.rack_id}</span>}\n                  </div>\n                </div>\n\n                <ReturnStatusManager\n                  discId={disc.id}\n                  currentStatus={disc.return_status || 'Found'}\n                  onStatusUpdated={(newStatus) => handleReturnStatusUpdate(disc.id, newStatus)}\n                  disabled={false}\n                />\n\n                {disc.image_urls && disc.image_urls.length > 0 && (\n                  <div className=\"disc-images\">\n                    {disc.image_urls.slice(0, 2).map((imageUrl: string, index: number) => (\n                      <img\n                        key={index}\n                        src={imageUrl}\n                        alt={`${disc.brand} ${disc.mold || 'disc'} ${index + 1}`}\n                        className=\"disc-image\"\n                        onError={(e) => {\n                          (e.target as HTMLImageElement).style.display = 'none';\n                        }}\n                      />\n                    ))}\n                  </div>\n                )}\n\n                <div className=\"disc-details\">\n                  <div className=\"detail-row\">\n                    <span className=\"label\">Color:</span>\n                    <span className=\"value\">{disc.color}</span>\n                  </div>\n                  {disc.source_name && (\n                    <div className=\"detail-row\">\n                      <span className=\"label\">Source:</span>\n                      <span className=\"value\">{disc.source_name}</span>\n                    </div>\n                  )}\n                  <div className=\"detail-row\">\n                    <span className=\"label\">Specific Location:</span>\n                    <span className=\"value\">{disc.location_found}</span>\n                  </div>\n                  <div className=\"detail-row\">\n                    <span className=\"label\">Found Date:</span>\n                    <span className=\"value\">{new Date(disc.found_date).toLocaleDateString()}</span>\n                  </div>\n                  {disc.returned_at && (\n                    <div className=\"detail-row\">\n                      <span className=\"label\">Returned Date:</span>\n                      <span className=\"value\">{new Date(disc.returned_at).toLocaleDateString()}</span>\n                    </div>\n                  )}\n                  {disc.returned_notes && (\n                    <div className=\"detail-row\">\n                      <span className=\"label\">Notes:</span>\n                      <span className=\"value\">{disc.returned_notes}</span>\n                    </div>\n                  )}\n                  {disc.contact_attempts_count !== undefined && (\n                    <div className=\"detail-row\">\n                      <span className=\"label\">Contact Attempts:</span>\n                      <span className=\"value\">\n                        {disc.contact_attempts_count}\n                        {disc.last_contact_date && (\n                          <span className=\"text-sm text-gray-500 ml-2\">\n                            (Last: {new Date(disc.last_contact_date).toLocaleDateString()})\n                          </span>\n                        )}\n                      </span>\n                    </div>\n                  )}\n                </div>\n\n                {/* Contact Attempts Section */}\n                <ContactAttempts\n                  discId={disc.id}\n                  onContactAdded={() => loadAllDiscs()}\n                />\n              </div>\n            ))\n          )}\n        </div>\n      )}\n\n      {/* Source Manager Modal */}\n      {showSourceManager && (\n        <SourceManager onClose={() => setShowSourceManager(false)} />\n      )}\n    </div>\n  );\n}\n\nfunction Home({ onNavigate }: PageProps) {\n  return (\n    <div>\n      <div className=\"hero\">\n        <p>\n          The DZDiscFinder helps disc golf players reunite with their lost discs, report found discs, orsearch for your lost discs in our database.\n        </p>\n\n        <div className=\"hero-buttons\">\n          <button className=\"hero-button secondary\" onClick={() => onNavigate('search-lost')}>\n            Search\n          </button>\n          <button className=\"hero-button primary\" onClick={() => onNavigate('report-found')}>\n            Found\n          </button>\n\n        </div>\n      </div>\n\n      <div className=\"features\">\n        <div className=\"feature-card\">\n          <div className=\"feature-icon\">\n            <div>🔍</div>\n          </div>\n          <h3>Smart Matching</h3>\n          <p>\n            Our intelligent system matches found and lost discs based on brand, model, color, and location.\n          </p>\n        </div>\n\n        <div className=\"feature-card\">\n          <div className=\"feature-icon\">\n            <div>📍</div>\n          </div>\n          <h3>Location Based</h3>\n          <p>\n            Find discs near where you lost them with our location-based search and matching.\n          </p>\n        </div>\n\n        <div className=\"feature-card\">\n          <div className=\"feature-icon\">\n            <div>💬</div>\n          </div>\n          <h3>Easy Communication</h3>\n          <p>\n            Connect directly with finders and owners through our secure messaging system.\n          </p>\n        </div>\n      </div>\n\n      <div className=\"stats\">\n        <div className=\"stats-grid\">\n          <div className=\"stat-item\">\n            <div className=\"stat-number\">500+</div>\n            <div className=\"stat-label\">Discs Reunited</div>\n          </div>\n          <div className=\"stat-item\">\n            <div className=\"stat-number\">1,200+</div>\n            <div className=\"stat-label\">Active Users</div>\n          </div>\n          <div className=\"stat-item\">\n            <div className=\"stat-number\">95%</div>\n            <div className=\"stat-label\">Success Rate</div>\n          </div>\n        </div>\n      </div>\n\n      <div className=\"cta\">\n        <h2>Join the Community</h2>\n        <p>\n          Create an account to report found discs, search for lost ones, and help fellow disc golfers.\n        </p>\n        <button className=\"cta-button\" onClick={() => onNavigate('login')}>\n          Sign Up Now\n        </button>\n      </div>\n    </div>\n  );\n}\n\nfunction ReportFound({ onNavigate }: PageProps) {\n  const { user, isGuest } = useAuth();\n\n  // Get today's date in YYYY-MM-DD format\n  const getTodayDate = () => {\n    const today = new Date();\n    return today.toISOString().split('T')[0];\n  };\n\n  const [formData, setFormData] = useState({\n    brand: '',\n    mold: '',\n    discType: '',\n    color: '',\n    weight: '',\n    condition: '',\n    plasticType: '',\n    stampText: '',\n    phoneNumber: '',\n    nameOnDisc: '',\n    sourceId: '',\n    locationFound: 'Exact location unknown.',\n    foundDate: getTodayDate(),\n    description: '',\n  });\n  const [selectedImages, setSelectedImages] = useState<File[]>([]);\n  const [isSubmitting, setIsSubmitting] = useState(false);\n  const [submitMessage, setSubmitMessage] = useState('');\n  const [sources, setSources] = useState<Source[]>([]);\n\n  // Load active sources for the dropdown and user's default source\n  useEffect(() => {\n    const loadSourcesAndDefaults = async () => {\n      try {\n        const activeSources = await supabaseService.getActiveSources();\n        setSources(activeSources);\n\n        // If user is authenticated, load their default source\n        if (user?.id) {\n          const defaultSource = await supabaseService.getUserDefaultSource(user.id);\n          if (defaultSource) {\n            setFormData(prev => ({\n              ...prev,\n              sourceId: defaultSource.id\n            }));\n          }\n        }\n      } catch (error) {\n        console.error('Error loading sources:', error);\n      }\n    };\n\n    loadSourcesAndDefaults();\n  }, [user?.id]);\n\n  // Require authentication to report found discs\n  if (isGuest) {\n    return (\n      <div className=\"page-container\">\n        <div className=\"page-header\">\n          <button className=\"back-button\" onClick={() => onNavigate('home')}>\n            ← Back to Home\n          </button>\n          <h1>Report a Found Disc</h1>\n          <p>You must be signed in to report found discs.</p>\n        </div>\n        <div className=\"auth-required\">\n          <h2>Authentication Required</h2>\n          <p>Please sign in or create an account to report found discs. This helps us maintain data quality and allows disc owners to contact you.</p>\n          <button className=\"button primary\" onClick={() => onNavigate('login')}>\n            Sign In / Sign Up\n          </button>\n        </div>\n      </div>\n    );\n  }\n\n  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {\n    const { name, value } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: value\n    }));\n  };\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n    setIsSubmitting(true);\n    setSubmitMessage('');\n\n    try {\n      console.log('Form submission started');\n      console.log('User:', user);\n      console.log('Selected images:', selectedImages.length);\n\n      // Check if user is authenticated for image upload\n      if (selectedImages.length > 0 && !user) {\n        setSubmitMessage('Error: You must be signed in to upload images');\n        return;\n      }\n\n      // Test connection first\n      const { connected } = await discService.testConnection();\n      console.log('Supabase connection test:', connected);\n\n      if (!connected) {\n        setSubmitMessage('Demo Mode: Form data logged to console (Supabase not configured)');\n        console.log('Form data:', formData, 'Images:', selectedImages.length);\n        setTimeout(() => {\n          onNavigate('home');\n        }, 2000);\n        return;\n      }\n\n      let imageUrls: string[] = [];\n\n      // Upload images if any are selected\n      if (selectedImages.length > 0) {\n        console.log('Starting image upload for user:', user!.id);\n        setSubmitMessage('Uploading images...');\n        const { urls, error: imageError } = await imageService.uploadImages(selectedImages, user!.id);\n\n        console.log('Image upload result:', { urls, error: imageError });\n\n        if (imageError) {\n          console.error('Image upload error:', imageError);\n          setSubmitMessage(`Error uploading images: ${imageError.message || JSON.stringify(imageError)}`);\n          return;\n        }\n\n        imageUrls = urls;\n        console.log('Images uploaded successfully:', imageUrls);\n      }\n\n      // Prepare data for Supabase\n      const discData = {\n        finder_id: user!.id, // Use authenticated user's ID\n        brand: formData.brand,\n        mold: formData.mold || undefined,\n        disc_type: formData.discType || undefined,\n        color: formData.color,\n        weight: formData.weight ? parseInt(formData.weight) : undefined,\n        condition: formData.condition || undefined,\n        plastic_type: formData.plasticType || undefined,\n        stamp_text: formData.stampText || undefined,\n        phone_number: formData.phoneNumber || undefined,\n        name_on_disc: formData.nameOnDisc || undefined,\n        source_id: formData.sourceId || undefined,\n        location_found: formData.locationFound,\n        found_date: formData.foundDate,\n        description: formData.description || undefined,\n        image_urls: imageUrls.length > 0 ? imageUrls : undefined,\n      };\n\n      setSubmitMessage('Saving disc information...');\n      const { data, error } = await discService.createFoundDisc(discData);\n\n      if (error) {\n        // If disc creation failed but images were uploaded, clean up the images\n        if (imageUrls.length > 0) {\n          await imageService.deleteImages(imageUrls);\n        }\n        setSubmitMessage(`Error: ${(error as any)?.message || 'Unknown error occurred'}`);\n      } else {\n        setSubmitMessage('Found disc reported successfully!');\n        console.log('Saved disc:', data);\n        setTimeout(() => {\n          onNavigate('home');\n        }, 2000);\n      }\n    } catch (error) {\n      setSubmitMessage('Demo Mode: Form data logged to console (Supabase not configured)');\n      console.log('Form data:', formData, 'Images:', selectedImages.length);\n      setTimeout(() => {\n        onNavigate('home');\n      }, 2000);\n    } finally {\n      setIsSubmitting(false);\n    }\n  };\n\n  return (\n    <div className=\"form-container\">\n      <div className=\"form-header\">\n        <button className=\"back-button\" onClick={() => onNavigate('home')}>\n          ← Back to Home\n        </button>\n        <h1>Report a Found Disc</h1>\n        <p>Help reunite a disc with its owner by providing details about the disc you found.</p>\n      </div>\n\n      {!user && (\n        <div className=\"auth-notice\">\n          <p>\n            <strong>Sign in required:</strong> You need to be signed in to report found discs and upload images.\n          </p>\n          <button\n            className=\"button primary\"\n            onClick={() => onNavigate('login')}\n          >\n            Sign In\n          </button>\n        </div>\n      )}\n\n      {submitMessage && (\n        <div className={`status-message ${submitMessage.includes('Error') ? 'error' : 'success'}`}>\n          {submitMessage}\n        </div>\n      )}\n\n      <form className=\"disc-form\" onSubmit={handleSubmit} style={{ opacity: !user ? 0.6 : 1 }}>\n        <div className=\"form-section\">\n          <h3>Disc Information</h3>\n          <div className=\"form-row\">\n            <div className=\"form-group\">\n              <label htmlFor=\"brand\">Brand *</label>\n              <input\n                type=\"text\"\n                id=\"brand\"\n                name=\"brand\"\n                value={formData.brand}\n                onChange={handleInputChange}\n                required\n                placeholder=\"e.g., Innova, Discraft, Dynamic Discs\"\n              />\n            </div>\n            <div className=\"form-group\">\n              <label htmlFor=\"mold\">Mold *</label>\n              <input\n                type=\"text\"\n                id=\"mold\"\n                name=\"mold\"\n                value={formData.mold}\n                onChange={handleInputChange}\n                required\n                placeholder=\"e.g., Destroyer, Buzzz, Judge\"\n              />\n            </div>\n          </div>\n\n          <div className=\"form-row\">\n            <div className=\"form-group\">\n              <label htmlFor=\"discType\">Disc Type</label>\n              <input\n                type=\"text\"\n                id=\"discType\"\n                name=\"discType\"\n                value={formData.discType}\n                onChange={handleInputChange}\n                placeholder=\"e.g., Putter, Midrange, Fairway Driver, Distance Driver\"\n              />\n            </div>\n            <div className=\"form-group\">\n              <label htmlFor=\"color\">Color *</label>\n              <input\n                type=\"text\"\n                id=\"color\"\n                name=\"color\"\n                value={formData.color}\n                onChange={handleInputChange}\n                required\n                placeholder=\"e.g., Blue, Red, Orange\"\n              />\n            </div>\n          </div>\n\n          <div className=\"form-row\">\n            <div className=\"form-group\">\n              <label htmlFor=\"weight\">Weight (grams)</label>\n              <input\n                type=\"number\"\n                id=\"weight\"\n                name=\"weight\"\n                value={formData.weight}\n                onChange={handleInputChange}\n                placeholder=\"e.g., 175\"\n                min=\"100\"\n                max=\"200\"\n              />\n            </div>\n            <div className=\"form-group\">\n              <label htmlFor=\"condition\">Condition</label>\n              <input\n                type=\"text\"\n                id=\"condition\"\n                name=\"condition\"\n                value={formData.condition}\n                onChange={handleInputChange}\n                placeholder=\"e.g., New, Excellent, Good, Fair, Poor\"\n              />\n            </div>\n          </div>\n        </div>\n\n        <div className=\"form-section\">\n          <h3>Additional Details</h3>\n          <div className=\"form-row\">\n            <div className=\"form-group\">\n              <label htmlFor=\"plasticType\">Plastic Type</label>\n              <input\n                type=\"text\"\n                id=\"plasticType\"\n                name=\"plasticType\"\n                value={formData.plasticType}\n                onChange={handleInputChange}\n                placeholder=\"e.g., Champion, ESP, Lucid\"\n              />\n            </div>\n            <div className=\"form-group\">\n              <label htmlFor=\"stampText\">Stamp/Text</label>\n              <input\n                type=\"text\"\n                id=\"stampText\"\n                name=\"stampText\"\n                value={formData.stampText}\n                onChange={handleInputChange}\n                placeholder=\"Any text or stamps on the disc\"\n              />\n            </div>\n          </div>\n\n          <div className=\"form-row\">\n            <div className=\"form-group\">\n              <label htmlFor=\"phoneNumber\">Phone Number on Disc</label>\n              <input\n                type=\"tel\"\n                id=\"phoneNumber\"\n                name=\"phoneNumber\"\n                value={formData.phoneNumber}\n                onChange={handleInputChange}\n                placeholder=\"Phone number written on disc\"\n              />\n            </div>\n            <div className=\"form-group\">\n              <label htmlFor=\"nameOnDisc\">Name on Disc</label>\n              <input\n                type=\"text\"\n                id=\"nameOnDisc\"\n                name=\"nameOnDisc\"\n                value={formData.nameOnDisc}\n                onChange={handleInputChange}\n                placeholder=\"Name written on disc\"\n              />\n            </div>\n          </div>\n        </div>\n\n        <div className=\"form-section\">\n          <h3>Location & Date</h3>\n          <div className=\"form-row\">\n            <div className=\"form-group\">\n              <label htmlFor=\"sourceId\">Source *</label>\n              <select\n                id=\"sourceId\"\n                name=\"sourceId\"\n                value={formData.sourceId}\n                onChange={handleInputChange}\n                required\n                className=\"form-select\"\n              >\n                <option value=\"\">Select where disc was found...</option>\n                {sources.map((source) => (\n                  <option key={source.id} value={source.id}>\n                    {source.name}\n                  </option>\n                ))}\n              </select>\n              <small className=\"form-help\">Choose the general location or event where the disc was found</small>\n            </div>\n            <div className=\"form-group\">\n              <label htmlFor=\"foundDate\">Date Found *</label>\n              <input\n                type=\"date\"\n                id=\"foundDate\"\n                name=\"foundDate\"\n                value={formData.foundDate}\n                onChange={handleInputChange}\n                required\n              />\n            </div>\n          </div>\n          <div className=\"form-row\">\n            <div className=\"form-group\">\n              <label htmlFor=\"locationFound\">Specific Location *</label>\n              <input\n                type=\"text\"\n                id=\"locationFound\"\n                name=\"locationFound\"\n                value={formData.locationFound}\n                onChange={handleInputChange}\n                required\n                placeholder=\"e.g., East Pond, Hole 7, Near the basket (or leave default)\"\n              />\n              <small className=\"form-help\">Provide specific details about where within the source location. Defaults to \"Exact location unknown.\" if not changed.</small>\n            </div>\n          </div>\n\n          <div className=\"form-group\">\n            <label htmlFor=\"description\">Additional Description</label>\n            <textarea\n              id=\"description\"\n              name=\"description\"\n              value={formData.description}\n              onChange={handleInputChange}\n              rows={4}\n              placeholder=\"Any additional details about where or how you found the disc...\"\n            />\n          </div>\n        </div>\n\n        <div className=\"form-section\">\n          <h3>Disc Images</h3>\n          <p className=\"form-section-description\">\n            Adding photos helps disc owners identify their disc more easily. You can upload up to 2 images.\n          </p>\n          <ImageUpload\n            onImagesChange={setSelectedImages}\n            maxImages={2}\n            maxSizePerImage={10}\n            disabled={isSubmitting}\n          />\n        </div>\n\n        <div className=\"form-actions\">\n          <button\n            type=\"button\"\n            className=\"button secondary\"\n            onClick={() => onNavigate('home')}\n            disabled={isSubmitting}\n          >\n            Cancel\n          </button>\n          <button\n            type=\"submit\"\n            className=\"button primary\"\n            disabled={isSubmitting || !user}\n          >\n            {isSubmitting ? 'Submitting...' : !user ? 'Sign In Required' : 'Report Found Disc'}\n          </button>\n        </div>\n      </form>\n    </div>\n  );\n}\n\nfunction SearchLost({ onNavigate }: PageProps) {\n  const { userRole } = useAuth();\n  const [searchCriteria, setSearchCriteria] = useState({\n    brand: '',\n    mold: '',\n    color: '',\n    discType: '',\n    locationFound: '',\n    rackId: '',\n  });\n  const [foundDiscs, setFoundDiscs] = useState<any[]>([]);\n  const [isSearching, setIsSearching] = useState(false);\n  const [hasSearched, setHasSearched] = useState(false);\n\n  const handleReturnStatusUpdate = (discId: string, newStatus: ReturnStatus) => {\n    // Update the disc in the local state\n    setFoundDiscs(prev => prev.map(disc =>\n      disc.id === discId\n        ? { ...disc, return_status: newStatus, returned_at: new Date().toISOString() }\n        : disc\n    ));\n  };\n\n  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {\n    const { name, value } = e.target;\n    setSearchCriteria(prev => ({\n      ...prev,\n      [name]: value\n    }));\n  };\n\n  const handleSearch = async (e: React.FormEvent) => {\n    e.preventDefault();\n    setIsSearching(true);\n    setHasSearched(true);\n\n    try {\n      const { data, error } = await discService.searchFoundDiscs(searchCriteria);\n\n      if (error) {\n        console.error('Search error:', error);\n        setFoundDiscs([]);\n      } else {\n        console.log('Search results:', data);\n        setFoundDiscs(data || []);\n      }\n    } catch (error) {\n      console.error('Search failed:', error);\n      setFoundDiscs([]);\n    } finally {\n      setIsSearching(false);\n    }\n  };\n\n  const loadAllDiscs = async () => {\n    setIsSearching(true);\n    setHasSearched(true);\n\n    try {\n      const { data, error } = await discService.getFoundDiscs();\n\n      if (error) {\n        console.error('Load error:', error);\n        setFoundDiscs([]);\n      } else {\n        console.log('Load all results:', data);\n        setFoundDiscs(data || []);\n      }\n    } catch (error) {\n      console.error('Load failed:', error);\n      setFoundDiscs([]);\n    } finally {\n      setIsSearching(false);\n    }\n  };\n\n  return (\n    <div className=\"page-container\">\n      <div className=\"page-header\">\n        <button className=\"back-button\" onClick={() => onNavigate('home')}>\n          ← Back to Home\n        </button>\n        <h1>Search Lost Discs</h1>\n        <p>Search through reported found discs to see if someone has found your lost disc.</p>\n      </div>\n\n      <div className=\"search-container\">\n        <form className=\"search-form\" onSubmit={handleSearch}>\n          <div className=\"search-section\">\n            <h3>Search Criteria</h3>\n            <div className=\"form-row\">\n              <div className=\"form-group\">\n                <label htmlFor=\"search-rack-id\">Rack ID</label>\n                <input\n                  type=\"number\"\n                  id=\"search-rack-id\"\n                  name=\"rackId\"\n                  value={searchCriteria.rackId}\n                  onChange={handleInputChange}\n                  placeholder=\"e.g., 417, 555, 7338\"\n                />\n              </div>\n              <div className=\"form-group\">\n                <label htmlFor=\"search-brand\">Brand</label>\n                <input\n                  type=\"text\"\n                  id=\"search-brand\"\n                  name=\"brand\"\n                  value={searchCriteria.brand}\n                  onChange={handleInputChange}\n                  placeholder=\"e.g., Innova, Discraft\"\n                />\n              </div>\n            </div>\n\n            <div className=\"form-row\">\n              <div className=\"form-group\">\n                <label htmlFor=\"search-mold\">Mold</label>\n                <input\n                  type=\"text\"\n                  id=\"search-mold\"\n                  name=\"mold\"\n                  value={searchCriteria.mold}\n                  onChange={handleInputChange}\n                  placeholder=\"e.g., Destroyer, Buzzz\"\n                />\n              </div>\n            </div>\n\n            <div className=\"form-row\">\n              <div className=\"form-group\">\n                <label htmlFor=\"search-color\">Color</label>\n                <input\n                  type=\"text\"\n                  id=\"search-color\"\n                  name=\"color\"\n                  value={searchCriteria.color}\n                  onChange={handleInputChange}\n                  placeholder=\"e.g., Blue, Red\"\n                />\n              </div>\n              <div className=\"form-group\">\n                <label htmlFor=\"search-discType\">Disc Type</label>\n                <select\n                  id=\"search-discType\"\n                  name=\"discType\"\n                  value={searchCriteria.discType}\n                  onChange={handleInputChange}\n                >\n                  <option value=\"\">Any type</option>\n                  <option value=\"putter\">Putter</option>\n                  <option value=\"midrange\">Midrange</option>\n                  <option value=\"fairway_driver\">Fairway Driver</option>\n                  <option value=\"distance_driver\">Distance Driver</option>\n                  <option value=\"approach\">Approach</option>\n                </select>\n              </div>\n            </div>\n\n            <div className=\"form-group\">\n              <label htmlFor=\"search-location\">Location</label>\n              <input\n                type=\"text\"\n                id=\"search-location\"\n                name=\"locationFound\"\n                value={searchCriteria.locationFound}\n                onChange={handleInputChange}\n                placeholder=\"e.g., Maple Hill, DeLaveaga\"\n              />\n            </div>\n          </div>\n\n          <div className=\"search-actions\">\n            <button\n              type=\"button\"\n              className=\"button secondary\"\n              onClick={loadAllDiscs}\n              disabled={isSearching}\n            >\n              {isSearching ? 'Loading...' : 'Show All Found Discs'}\n            </button>\n            <button\n              type=\"submit\"\n              className=\"button primary\"\n              disabled={isSearching}\n            >\n              {isSearching ? 'Searching...' : 'Search'}\n            </button>\n          </div>\n        </form>\n\n        {hasSearched && (\n          <div className=\"search-results\">\n            <h3>\n              {foundDiscs.length > 0\n                ? `Found ${foundDiscs.length} disc${foundDiscs.length === 1 ? '' : 's'}`\n                : 'No discs found matching your criteria'\n              }\n            </h3>\n\n            {foundDiscs.length > 0 && (\n              <div className=\"disc-grid\">\n                {foundDiscs.map((disc) => (\n                  <div key={disc.id} className=\"disc-card\">\n                    <div className=\"disc-header\">\n                      <h4>{disc.brand} {disc.mold || 'Unknown Mold'}</h4>\n                      <div className=\"disc-meta\">\n                        <span className=\"disc-type\">{disc.disc_type || 'Unknown Type'}</span>\n                        {disc.rack_id && <span className=\"rack-id\">Rack #{disc.rack_id}</span>}\n                      </div>\n                    </div>\n\n                    {/* Return Status - only show for admin or if not 'Found' */}\n                    {(userRole === 'admin' || (disc.return_status && disc.return_status !== 'Found')) && (\n                      <ReturnStatusManager\n                        discId={disc.id}\n                        currentStatus={disc.return_status || 'Found'}\n                        onStatusUpdated={(newStatus) => handleReturnStatusUpdate(disc.id, newStatus)}\n                        disabled={userRole !== 'admin'}\n                      />\n                    )}\n\n                    {disc.image_urls && disc.image_urls.length > 0 && (\n                      <div className=\"disc-images\">\n                        {disc.image_urls.slice(0, 2).map((imageUrl: string, index: number) => (\n                          <img\n                            key={index}\n                            src={imageUrl}\n                            alt={`${disc.brand} ${disc.mold || 'disc'} ${index + 1}`}\n                            className=\"disc-image\"\n                            onError={(e) => {\n                              // Hide broken images\n                              (e.target as HTMLImageElement).style.display = 'none';\n                            }}\n                          />\n                        ))}\n                      </div>\n                    )}\n\n                    <div className=\"disc-details\">\n                      <div className=\"detail-row\">\n                        <span className=\"label\">Color:</span>\n                        <span className=\"value\">{disc.color}</span>\n                      </div>\n\n                      {disc.weight && (\n                        <div className=\"detail-row\">\n                          <span className=\"label\">Weight:</span>\n                          <span className=\"value\">{disc.weight}g</span>\n                        </div>\n                      )}\n\n                      <div className=\"detail-row\">\n                        <span className=\"label\">Condition:</span>\n                        <span className=\"value\">{disc.condition || 'Unknown'}</span>\n                      </div>\n\n                      {disc.source_name && (\n                        <div className=\"detail-row\">\n                          <span className=\"label\">Source:</span>\n                          <span className=\"value\">{disc.source_name}</span>\n                        </div>\n                      )}\n\n                      <div className=\"detail-row\">\n                        <span className=\"label\">Specific Location:</span>\n                        <span className=\"value\">{disc.location_found}</span>\n                      </div>\n\n                      <div className=\"detail-row\">\n                        <span className=\"label\">Found on:</span>\n                        <span className=\"value\">{new Date(disc.found_date).toLocaleDateString()}</span>\n                      </div>\n\n                      {disc.phone_number && (\n                        <div className=\"detail-row\">\n                          <span className=\"label\">Phone on disc:</span>\n                          <span className=\"value\">{disc.phone_number}</span>\n                        </div>\n                      )}\n\n                      {disc.name_on_disc && (\n                        <div className=\"detail-row\">\n                          <span className=\"label\">Name on disc:</span>\n                          <span className=\"value\">{disc.name_on_disc}</span>\n                        </div>\n                      )}\n\n                      {disc.description && (\n                        <div className=\"detail-row\">\n                          <span className=\"label\">Description:</span>\n                          <span className=\"value\">{disc.description}</span>\n                        </div>\n                      )}\n                    </div>\n\n                    <div className=\"disc-actions\">\n                      <button className=\"button primary small\">\n                        Contact Finder\n                      </button>\n                      <button className=\"button secondary small\">\n                        Report as Mine\n                      </button>\n                    </div>\n                  </div>\n                ))}\n              </div>\n            )}\n          </div>\n        )}\n      </div>\n    </div>\n  );\n}\n\nfunction Login({ onNavigate }: PageProps) {\n  const { signIn, signUp } = useAuth();\n  const [isLogin, setIsLogin] = useState(true);\n  const [formData, setFormData] = useState({\n    email: '',\n    password: '',\n    fullName: '',\n    confirmPassword: ''\n  });\n  const [loading, setLoading] = useState(false);\n  const [message, setMessage] = useState('');\n\n  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {\n    const { name, value } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: value\n    }));\n  };\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n    setLoading(true);\n    setMessage('');\n\n    try {\n      if (isLogin) {\n        const { error } = await signIn(formData.email, formData.password);\n        if (error) {\n          setMessage(error.message);\n        } else {\n          setMessage('Signed in successfully!');\n          setTimeout(() => onNavigate('home'), 1000);\n        }\n      } else {\n        if (formData.password !== formData.confirmPassword) {\n          setMessage('Passwords do not match');\n          setLoading(false);\n          return;\n        }\n        if (formData.password.length < 6) {\n          setMessage('Password must be at least 6 characters');\n          setLoading(false);\n          return;\n        }\n\n        const { error } = await signUp(formData.email, formData.password, formData.fullName);\n        if (error) {\n          setMessage(error.message);\n        } else {\n          setMessage('Account created! Please check your email to verify your account.');\n        }\n      }\n    } catch (error) {\n      setMessage('An unexpected error occurred');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  return (\n    <div className=\"page-container\">\n      <div className=\"page-header\">\n        <button className=\"back-button\" onClick={() => onNavigate('home')}>\n          ← Back to Home\n        </button>\n        <h1>{isLogin ? 'Sign In' : 'Create Account'}</h1>\n        <p>{isLogin ? 'Sign in to your account' : 'Create an account to report and search for discs'}</p>\n      </div>\n\n      <div className=\"auth-container\">\n        <div className=\"auth-tabs\">\n          <button\n            className={`auth-tab ${isLogin ? 'active' : ''}`}\n            onClick={() => setIsLogin(true)}\n          >\n            Sign In\n          </button>\n          <button\n            className={`auth-tab ${!isLogin ? 'active' : ''}`}\n            onClick={() => setIsLogin(false)}\n          >\n            Sign Up\n          </button>\n        </div>\n\n        {message && (\n          <div className={`status-message ${message.includes('error') || message.includes('Error') ? 'error' : 'success'}`}>\n            {message}\n          </div>\n        )}\n\n        <form className=\"auth-form\" onSubmit={handleSubmit}>\n          {!isLogin && (\n            <div className=\"form-group\">\n              <label htmlFor=\"fullName\">Full Name</label>\n              <input\n                type=\"text\"\n                id=\"fullName\"\n                name=\"fullName\"\n                value={formData.fullName}\n                onChange={handleInputChange}\n                required={!isLogin}\n                placeholder=\"Your full name\"\n              />\n            </div>\n          )}\n\n          <div className=\"form-group\">\n            <label htmlFor=\"email\">Email</label>\n            <input\n              type=\"email\"\n              id=\"email\"\n              name=\"email\"\n              value={formData.email}\n              onChange={handleInputChange}\n              required\n              placeholder=\"<EMAIL>\"\n            />\n          </div>\n\n          <div className=\"form-group\">\n            <label htmlFor=\"password\">Password</label>\n            <input\n              type=\"password\"\n              id=\"password\"\n              name=\"password\"\n              value={formData.password}\n              onChange={handleInputChange}\n              required\n              placeholder={isLogin ? \"Your password\" : \"At least 6 characters\"}\n            />\n          </div>\n\n          {!isLogin && (\n            <div className=\"form-group\">\n              <label htmlFor=\"confirmPassword\">Confirm Password</label>\n              <input\n                type=\"password\"\n                id=\"confirmPassword\"\n                name=\"confirmPassword\"\n                value={formData.confirmPassword}\n                onChange={handleInputChange}\n                required={!isLogin}\n                placeholder=\"Confirm your password\"\n              />\n            </div>\n          )}\n\n          <button\n            type=\"submit\"\n            className=\"button primary full-width\"\n            disabled={loading}\n          >\n            {loading ? 'Please wait...' : (isLogin ? 'Sign In' : 'Create Account')}\n          </button>\n        </form>\n      </div>\n    </div>\n  );\n}\n\nexport default App;\n"], "mappings": ";;;;;;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,WAAW,EAAEC,YAAY,EAAwBC,eAAe,QAAQ,gBAAgB;AACjG,SAASC,YAAY,EAAEC,OAAO,QAAQ,wBAAwB;AAC9D,SAASC,WAAW,QAAQ,0BAA0B;AACtD,SAASC,mBAAmB,QAAQ,kCAAkC;AACtE,SAASC,eAAe,QAAQ,8BAA8B;AAC9D,SAASC,mBAAmB,QAAQ,kCAAkC;AACtE,SAASC,gBAAgB,QAAQ,+BAA+B;AAChE,OAAOC,oBAAoB,MAAM,mCAAmC;AACpE,OAAOC,cAAc,MAAM,6BAA6B;AACxD,OAAOC,qBAAqB,MAAM,oCAAoC;AACtE,OAAOC,aAAa,MAAM,4BAA4B;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAIvD,SAASC,UAAUA,CAAA,EAAG;EAAAC,EAAA;EACpB,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGvB,QAAQ,CAAO,MAAM,CAAC;EAC5D,MAAM;IAAEwB,IAAI;IAAEC,QAAQ;IAAEC,OAAO;IAAEC;EAAQ,CAAC,GAAGrB,OAAO,CAAC,CAAC;EAEtD,MAAMsB,cAAc,GAAIC,IAAY,IAAK;IACvCN,cAAc,CAACM,IAAY,CAAC;EAC9B,CAAC;EAED,MAAMC,UAAU,GAAGA,CAAA,KAAM;IACvB,QAAQR,WAAW;MACjB,KAAK,MAAM;QACT,oBAAOL,OAAA,CAACc,IAAI;UAACC,UAAU,EAAET;QAAe;UAAAU,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAC7C,KAAK,cAAc;QACjB,oBAAOnB,OAAA,CAACoB,WAAW;UAACL,UAAU,EAAET;QAAe;UAAAU,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACpD,KAAK,aAAa;QAChB,oBAAOnB,OAAA,CAACqB,UAAU;UAACN,UAAU,EAAET;QAAe;UAAAU,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACnD,KAAK,OAAO;QACV,oBAAOnB,OAAA,CAACsB,KAAK;UAACP,UAAU,EAAET;QAAe;UAAAU,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAC9C,KAAK,OAAO;QACV,oBAAOnB,OAAA,CAACuB,cAAc;UAACR,UAAU,EAAET;QAAe;UAAAU,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACvD,KAAK,YAAY;QACf,oBAAOnB,OAAA,CAACP,mBAAmB;UAACsB,UAAU,EAAEJ;QAAe;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAC5D,KAAK,oBAAoB;QACvB,oBAAOnB,OAAA,CAACN,gBAAgB;UAACqB,UAAU,EAAEJ;QAAe;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACzD,KAAK,gBAAgB;QACnB,oBAAOnB,OAAA,CAACL,oBAAoB;UAAAqB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACjC,KAAK,iBAAiB;QACpB,oBAAOnB,OAAA,CAACH,qBAAqB;UAAAmB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAClC,KAAK,SAAS;QACZ,OAAOZ,IAAI,gBAAGP,OAAA,CAACJ,cAAc;UAAC4B,MAAM,EAAEjB,IAAI,CAACkB;QAAG;UAAAT,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,gBAAGnB,OAAA,CAACsB,KAAK;UAACP,UAAU,EAAET;QAAe;UAAAU,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAC3F;QACE,oBAAOnB,OAAA,CAACc,IAAI;UAACC,UAAU,EAAET;QAAe;UAAAU,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;IAC/C;EACF,CAAC;EAED,MAAMO,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChC,MAAMjB,OAAO,CAAC,CAAC;IACfH,cAAc,CAAC,MAAM,CAAC;EACxB,CAAC;EAED,IAAII,OAAO,EAAE;IACX,oBACEV,OAAA;MAAK2B,SAAS,EAAC,KAAK;MAAAC,QAAA,eAClB5B,OAAA;QAAK2B,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAChC5B,OAAA;UAAK2B,SAAS,EAAC;QAAiB;UAAAX,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACvCnB,OAAA;UAAA4B,QAAA,EAAG;QAAU;UAAAZ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACjBnB,OAAA;UAAG6B,KAAK,EAAE;YAAEC,QAAQ,EAAE,QAAQ;YAAEC,KAAK,EAAE,MAAM;YAAEC,SAAS,EAAE;UAAO,CAAE;UAAAJ,QAAA,EAAC;QAEpE;UAAAZ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,oBACEnB,OAAA;IAAK2B,SAAS,EAAC,KAAK;IAAAC,QAAA,gBAClB5B,OAAA;MAAK2B,SAAS,EAAC,QAAQ;MAAAC,QAAA,eACrB5B,OAAA;QAAK2B,SAAS,EAAC,eAAe;QAAAC,QAAA,gBAC5B5B,OAAA;UAAK2B,SAAS,EAAC,MAAM;UAACM,OAAO,EAAEA,CAAA,KAAM3B,cAAc,CAAC,MAAM,CAAE;UAACuB,KAAK,EAAE;YAAEK,MAAM,EAAE;UAAU,CAAE;UAAAN,QAAA,EAAC;QAE3F;UAAAZ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACNnB,OAAA;UAAK2B,SAAS,EAAC,aAAa;UAAAC,QAAA,gBAC1B5B,OAAA;YAAQ2B,SAAS,EAAC,YAAY;YAACM,OAAO,EAAEA,CAAA,KAAM3B,cAAc,CAAC,cAAc,CAAE;YAAAsB,QAAA,EAAC;UAE9E;YAAAZ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTnB,OAAA;YAAQ2B,SAAS,EAAC,YAAY;YAACM,OAAO,EAAEA,CAAA,KAAM3B,cAAc,CAAC,aAAa,CAAE;YAAAsB,QAAA,EAAC;UAE7E;YAAAZ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,EAERZ,IAAI,gBACHP,OAAA;YAAK2B,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACxB5B,OAAA;cAAM2B,SAAS,EAAC,WAAW;cAAAC,QAAA,GACxBrB,IAAI,CAAC4B,KAAK,EAAC,IAAE,EAAC3B,QAAQ,EAAC,GAC1B;YAAA;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACPnB,OAAA;cAAQ2B,SAAS,EAAC,YAAY;cAACM,OAAO,EAAEA,CAAA,KAAM3B,cAAc,CAAC,SAAS,CAAE;cAAAsB,QAAA,EAAC;YAEzE;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,EACRX,QAAQ,KAAK,OAAO,iBACnBR,OAAA,CAAAE,SAAA;cAAA0B,QAAA,gBACE5B,OAAA;gBAAQ2B,SAAS,EAAC,YAAY;gBAACM,OAAO,EAAEA,CAAA,KAAM3B,cAAc,CAAC,OAAO,CAAE;gBAAAsB,QAAA,EAAC;cAEvE;gBAAAZ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACTnB,OAAA;gBAAQ2B,SAAS,EAAC,YAAY;gBAACM,OAAO,EAAEA,CAAA,KAAM3B,cAAc,CAAC,gBAAgB,CAAE;gBAAAsB,QAAA,EAAC;cAEhF;gBAAAZ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACTnB,OAAA;gBAAQ2B,SAAS,EAAC,YAAY;gBAACM,OAAO,EAAEA,CAAA,KAAM3B,cAAc,CAAC,iBAAiB,CAAE;gBAAAsB,QAAA,EAAC;cAEjF;gBAAAZ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA,eACT,CACH,EACAX,QAAQ,KAAK,YAAY,iBACxBR,OAAA;cAAQ2B,SAAS,EAAC,YAAY;cAACM,OAAO,EAAEA,CAAA,KAAM3B,cAAc,CAAC,YAAY,CAAE;cAAAsB,QAAA,EAAC;YAE5E;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CACT,eACDnB,OAAA;cAAQ2B,SAAS,EAAC,YAAY;cAACM,OAAO,EAAEP,aAAc;cAAAE,QAAA,EAAC;YAEvD;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,gBAENnB,OAAA;YAAQ2B,SAAS,EAAC,oBAAoB;YAACM,OAAO,EAAEA,CAAA,KAAM3B,cAAc,CAAC,OAAO,CAAE;YAAAsB,QAAA,EAAC;UAE/E;YAAAZ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CACT;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAENnB,OAAA;MAAM2B,SAAS,EAAC,gBAAgB;MAAAC,QAAA,EAC7Bf,UAAU,CAAC;IAAC;MAAAG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACT,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEV;AAACf,EAAA,CAjHQD,UAAU;EAAA,QAE4Bd,OAAO;AAAA;AAAA+C,EAAA,GAF7CjC,UAAU;AAmHnB,SAASkC,GAAGA,CAAA,EAAG;EACb,oBACErC,OAAA,CAACZ,YAAY;IAAAwC,QAAA,eACX5B,OAAA,CAACG,UAAU;MAAAa,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACF,CAAC;AAEnB;AAACmB,GAAA,GANQD,GAAG;AAYZ,SAASd,cAAcA,CAAC;EAAER;AAAsB,CAAC,EAAE;EAAAwB,GAAA;EACjD,MAAM;IAAE/B;EAAS,CAAC,GAAGnB,OAAO,CAAC,CAAC;EAC9B,MAAM,CAACmD,QAAQ,EAAEC,WAAW,CAAC,GAAG1D,QAAQ,CAAQ,EAAE,CAAC;EACnD,MAAM,CAAC2D,SAAS,EAAEC,YAAY,CAAC,GAAG5D,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAAC6D,MAAM,EAAEC,SAAS,CAAC,GAAG9D,QAAQ,CAAuB,KAAK,CAAC;EACjE,MAAM,CAAC+D,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGhE,QAAQ,CAAC,KAAK,CAAC;EAEjE,MAAMiE,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/BL,YAAY,CAAC,IAAI,CAAC;IAClB,IAAI;MACF,MAAM;QAAEM,IAAI;QAAEC;MAAM,CAAC,GAAG,MAAMjE,WAAW,CAACkE,kBAAkB,CAAC,CAAC;MAC9D,IAAID,KAAK,EAAE;QACTE,OAAO,CAACF,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;MACpD,CAAC,MAAM;QACLE,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAEJ,IAAI,CAAC;QACxCR,WAAW,CAACQ,IAAI,IAAI,EAAE,CAAC;MACzB;IACF,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdE,OAAO,CAACF,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;IACpD,CAAC,SAAS;MACRP,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC;EAED7D,KAAK,CAACE,SAAS,CAAC,MAAM;IACpBgE,YAAY,CAAC,CAAC;EAChB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMM,wBAAwB,GAAGA,CAACC,MAAc,EAAEC,SAAuB,KAAK;IAC5Ef,WAAW,CAACgB,IAAI,IAAIA,IAAI,CAACC,GAAG,CAACC,IAAI,IAC/BA,IAAI,CAAClC,EAAE,KAAK8B,MAAM,GACd;MAAE,GAAGI,IAAI;MAAEC,aAAa,EAAEJ,SAAS;MAAEK,WAAW,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;IAAE,CAAC,GAC5EJ,IACN,CAAC,CAAC;EACJ,CAAC;EAED,MAAMK,aAAa,GAAGpB,MAAM,KAAK,KAAK,GAClCJ,QAAQ,GACRA,QAAQ,CAACI,MAAM,CAACe,IAAI,IAAIA,IAAI,CAACC,aAAa,KAAKhB,MAAM,CAAC;EAE1D,MAAMqB,YAAY,GAAGzB,QAAQ,CAAC0B,MAAM,CAAC,CAACC,MAAM,EAAER,IAAI,KAAK;IACrD,MAAMS,MAAM,GAAGT,IAAI,CAACC,aAAa,IAAI,OAAO;IAC5CO,MAAM,CAACC,MAAM,CAAC,GAAG,CAACD,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC;IAC1C,OAAOD,MAAM;EACf,CAAC,EAAE,CAAC,CAA2B,CAAC;;EAEhC;EACA,IAAI3D,QAAQ,KAAK,OAAO,EAAE;IACxB,oBACER,OAAA;MAAK2B,SAAS,EAAC,gBAAgB;MAAAC,QAAA,eAC7B5B,OAAA;QAAK2B,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1B5B,OAAA;UAAQ2B,SAAS,EAAC,aAAa;UAACM,OAAO,EAAEA,CAAA,KAAMlB,UAAU,CAAC,MAAM,CAAE;UAAAa,QAAA,EAAC;QAEnE;UAAAZ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTnB,OAAA;UAAA4B,QAAA,EAAI;QAAa;UAAAZ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACtBnB,OAAA;UAAA4B,QAAA,EAAG;QAA8C;UAAAZ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,oBACEnB,OAAA;IAAK2B,SAAS,EAAC,gBAAgB;IAAAC,QAAA,gBAC7B5B,OAAA;MAAK2B,SAAS,EAAC,aAAa;MAAAC,QAAA,gBAC1B5B,OAAA;QAAQ2B,SAAS,EAAC,aAAa;QAACM,OAAO,EAAEA,CAAA,KAAMlB,UAAU,CAAC,MAAM,CAAE;QAAAa,QAAA,EAAC;MAEnE;QAAAZ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACTnB,OAAA;QAAA4B,QAAA,EAAI;MAAe;QAAAZ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACxBnB,OAAA;QAAA4B,QAAA,EAAG;MAA+C;QAAAZ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACnD,CAAC,eAGNnB,OAAA;MAAK2B,SAAS,EAAC,eAAe;MAAAC,QAAA,gBAC5B5B,OAAA;QACE2B,SAAS,EAAC,gBAAgB;QAC1BM,OAAO,EAAEA,CAAA,KAAMlB,UAAU,CAAC,oBAAoB,CAAE;QAAAa,QAAA,EACjD;MAED;QAAAZ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACTnB,OAAA;QACE2B,SAAS,EAAC,gBAAgB;QAC1BM,OAAO,EAAEA,CAAA,KAAMc,oBAAoB,CAAC,IAAI,CAAE;QAAAnB,QAAA,EAC3C;MAED;QAAAZ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,eAGNnB,OAAA;MAAK2B,SAAS,EAAC,gBAAgB;MAAAC,QAAA,gBAC7B5B,OAAA;QAAA4B,QAAA,EAAI;MAAc;QAAAZ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACvBnB,OAAA;QAAK2B,SAAS,EAAC,eAAe;QAAAC,QAAA,EAC3ByC,MAAM,CAACC,OAAO,CAACL,YAAY,CAAC,CAACP,GAAG,CAAC,CAAC,CAACU,MAAM,EAAEG,KAAK,CAAC,kBAChDvE,OAAA;UAAkB2B,SAAS,EAAC,cAAc;UAAAC,QAAA,gBACxC5B,OAAA;YAAM2B,SAAS,EAAC,OAAO;YAAAC,QAAA,EAAE2C;UAAK;YAAAvD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAiB,CAAC,eAChDnB,OAAA;YAAM2B,SAAS,EAAC,QAAQ;YAAAC,QAAA,EAAEwC;UAAM;YAAApD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA,GAFhCiD,MAAM;UAAApD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAGX,CACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNnB,OAAA;MAAK2B,SAAS,EAAC,iBAAiB;MAAAC,QAAA,gBAC9B5B,OAAA;QAAOwE,OAAO,EAAC,eAAe;QAAA5C,QAAA,EAAC;MAAiB;QAAAZ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eACxDnB,OAAA;QACEyB,EAAE,EAAC,eAAe;QAClBgD,KAAK,EAAE7B,MAAO;QACd8B,QAAQ,EAAGC,CAAC,IAAK9B,SAAS,CAAC8B,CAAC,CAACC,MAAM,CAACH,KAA6B,CAAE;QAAA7C,QAAA,gBAEnE5B,OAAA;UAAQyE,KAAK,EAAC,KAAK;UAAA7C,QAAA,EAAC;QAAY;UAAAZ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACzCnB,OAAA;UAAQyE,KAAK,EAAC,OAAO;UAAA7C,QAAA,EAAC;QAAK;UAAAZ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACpCnB,OAAA;UAAQyE,KAAK,EAAC,mBAAmB;UAAA7C,QAAA,EAAC;QAAiB;UAAAZ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAC5DnB,OAAA;UAAQyE,KAAK,EAAC,SAAS;UAAA7C,QAAA,EAAC;QAAO;UAAAZ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACxCnB,OAAA;UAAQyE,KAAK,EAAC,MAAM;UAAA7C,QAAA,EAAC;QAAI;UAAAZ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAClCnB,OAAA;UAAQyE,KAAK,EAAC,SAAS;UAAA7C,QAAA,EAAC;QAAO;UAAAZ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,EAGLuB,SAAS,gBACR1C,OAAA;MAAK2B,SAAS,EAAC,mBAAmB;MAAAC,QAAA,gBAChC5B,OAAA;QAAK2B,SAAS,EAAC;MAAiB;QAAAX,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eACvCnB,OAAA;QAAA4B,QAAA,EAAG;MAAgB;QAAAZ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACpB,CAAC,gBAENnB,OAAA;MAAK2B,SAAS,EAAC,iBAAiB;MAAAC,QAAA,EAC7BoC,aAAa,CAACa,MAAM,KAAK,CAAC,gBACzB7E,OAAA;QAAA4B,QAAA,EAAG;MAAuC;QAAAZ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,GAE9C6C,aAAa,CAACN,GAAG,CAAEC,IAAI,iBACrB3D,OAAA;QAAmB2B,SAAS,EAAC,iBAAiB;QAAAC,QAAA,gBAC5C5B,OAAA;UAAK2B,SAAS,EAAC,aAAa;UAAAC,QAAA,gBAC1B5B,OAAA;YAAA4B,QAAA,GAAK+B,IAAI,CAACmB,KAAK,EAAC,GAAC,EAACnB,IAAI,CAACoB,IAAI,IAAI,cAAc;UAAA;YAAA/D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACnDnB,OAAA;YAAK2B,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACxB5B,OAAA;cAAM2B,SAAS,EAAC,WAAW;cAAAC,QAAA,EAAE+B,IAAI,CAACqB,SAAS,IAAI;YAAc;cAAAhE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,EACpEwC,IAAI,CAACsB,OAAO,iBAAIjF,OAAA;cAAM2B,SAAS,EAAC,SAAS;cAAAC,QAAA,GAAC,QAAM,EAAC+B,IAAI,CAACsB,OAAO;YAAA;cAAAjE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENnB,OAAA,CAACT,mBAAmB;UAClBgE,MAAM,EAAEI,IAAI,CAAClC,EAAG;UAChByD,aAAa,EAAEvB,IAAI,CAACC,aAAa,IAAI,OAAQ;UAC7CuB,eAAe,EAAG3B,SAAS,IAAKF,wBAAwB,CAACK,IAAI,CAAClC,EAAE,EAAE+B,SAAS,CAAE;UAC7E4B,QAAQ,EAAE;QAAM;UAAApE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjB,CAAC,EAEDwC,IAAI,CAAC0B,UAAU,IAAI1B,IAAI,CAAC0B,UAAU,CAACR,MAAM,GAAG,CAAC,iBAC5C7E,OAAA;UAAK2B,SAAS,EAAC,aAAa;UAAAC,QAAA,EACzB+B,IAAI,CAAC0B,UAAU,CAACC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC5B,GAAG,CAAC,CAAC6B,QAAgB,EAAEC,KAAa,kBAC/DxF,OAAA;YAEEyF,GAAG,EAAEF,QAAS;YACdG,GAAG,EAAE,GAAG/B,IAAI,CAACmB,KAAK,IAAInB,IAAI,CAACoB,IAAI,IAAI,MAAM,IAAIS,KAAK,GAAG,CAAC,EAAG;YACzD7D,SAAS,EAAC,YAAY;YACtBgE,OAAO,EAAGhB,CAAC,IAAK;cACbA,CAAC,CAACC,MAAM,CAAsB/C,KAAK,CAAC+D,OAAO,GAAG,MAAM;YACvD;UAAE,GANGJ,KAAK;YAAAxE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAOX,CACF;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CACN,eAEDnB,OAAA;UAAK2B,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC3B5B,OAAA;YAAK2B,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzB5B,OAAA;cAAM2B,SAAS,EAAC,OAAO;cAAAC,QAAA,EAAC;YAAM;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACrCnB,OAAA;cAAM2B,SAAS,EAAC,OAAO;cAAAC,QAAA,EAAE+B,IAAI,CAAC5B;YAAK;cAAAf,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxC,CAAC,EACLwC,IAAI,CAACkC,WAAW,iBACf7F,OAAA;YAAK2B,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzB5B,OAAA;cAAM2B,SAAS,EAAC,OAAO;cAAAC,QAAA,EAAC;YAAO;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACtCnB,OAAA;cAAM2B,SAAS,EAAC,OAAO;cAAAC,QAAA,EAAE+B,IAAI,CAACkC;YAAW;cAAA7E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9C,CACN,eACDnB,OAAA;YAAK2B,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzB5B,OAAA;cAAM2B,SAAS,EAAC,OAAO;cAAAC,QAAA,EAAC;YAAkB;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACjDnB,OAAA;cAAM2B,SAAS,EAAC,OAAO;cAAAC,QAAA,EAAE+B,IAAI,CAACmC;YAAc;cAAA9E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjD,CAAC,eACNnB,OAAA;YAAK2B,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzB5B,OAAA;cAAM2B,SAAS,EAAC,OAAO;cAAAC,QAAA,EAAC;YAAW;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC1CnB,OAAA;cAAM2B,SAAS,EAAC,OAAO;cAAAC,QAAA,EAAE,IAAIkC,IAAI,CAACH,IAAI,CAACoC,UAAU,CAAC,CAACC,kBAAkB,CAAC;YAAC;cAAAhF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5E,CAAC,EACLwC,IAAI,CAACE,WAAW,iBACf7D,OAAA;YAAK2B,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzB5B,OAAA;cAAM2B,SAAS,EAAC,OAAO;cAAAC,QAAA,EAAC;YAAc;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC7CnB,OAAA;cAAM2B,SAAS,EAAC,OAAO;cAAAC,QAAA,EAAE,IAAIkC,IAAI,CAACH,IAAI,CAACE,WAAW,CAAC,CAACmC,kBAAkB,CAAC;YAAC;cAAAhF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7E,CACN,EACAwC,IAAI,CAACsC,cAAc,iBAClBjG,OAAA;YAAK2B,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzB5B,OAAA;cAAM2B,SAAS,EAAC,OAAO;cAAAC,QAAA,EAAC;YAAM;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACrCnB,OAAA;cAAM2B,SAAS,EAAC,OAAO;cAAAC,QAAA,EAAE+B,IAAI,CAACsC;YAAc;cAAAjF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjD,CACN,EACAwC,IAAI,CAACuC,sBAAsB,KAAKC,SAAS,iBACxCnG,OAAA;YAAK2B,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzB5B,OAAA;cAAM2B,SAAS,EAAC,OAAO;cAAAC,QAAA,EAAC;YAAiB;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAChDnB,OAAA;cAAM2B,SAAS,EAAC,OAAO;cAAAC,QAAA,GACpB+B,IAAI,CAACuC,sBAAsB,EAC3BvC,IAAI,CAACyC,iBAAiB,iBACrBpG,OAAA;gBAAM2B,SAAS,EAAC,4BAA4B;gBAAAC,QAAA,GAAC,SACpC,EAAC,IAAIkC,IAAI,CAACH,IAAI,CAACyC,iBAAiB,CAAC,CAACJ,kBAAkB,CAAC,CAAC,EAAC,GAChE;cAAA;gBAAAhF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CACP;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eAGNnB,OAAA,CAACR,eAAe;UACd+D,MAAM,EAAEI,IAAI,CAAClC,EAAG;UAChB4E,cAAc,EAAEA,CAAA,KAAMrD,YAAY,CAAC;QAAE;UAAAhC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtC,CAAC;MAAA,GAlFMwC,IAAI,CAAClC,EAAE;QAAAT,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAmFZ,CACN;IACF;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CACN,EAGA2B,iBAAiB,iBAChB9C,OAAA,CAACF,aAAa;MAACwG,OAAO,EAAEA,CAAA,KAAMvD,oBAAoB,CAAC,KAAK;IAAE;MAAA/B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAC7D;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV;AAACoB,GAAA,CAhOQhB,cAAc;EAAA,QACAlC,OAAO;AAAA;AAAAkH,GAAA,GADrBhF,cAAc;AAkOvB,SAAST,IAAIA,CAAC;EAAEC;AAAsB,CAAC,EAAE;EACvC,oBACEf,OAAA;IAAA4B,QAAA,gBACE5B,OAAA;MAAK2B,SAAS,EAAC,MAAM;MAAAC,QAAA,gBACnB5B,OAAA;QAAA4B,QAAA,EAAG;MAEH;QAAAZ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,eAEJnB,OAAA;QAAK2B,SAAS,EAAC,cAAc;QAAAC,QAAA,gBAC3B5B,OAAA;UAAQ2B,SAAS,EAAC,uBAAuB;UAACM,OAAO,EAAEA,CAAA,KAAMlB,UAAU,CAAC,aAAa,CAAE;UAAAa,QAAA,EAAC;QAEpF;UAAAZ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTnB,OAAA;UAAQ2B,SAAS,EAAC,qBAAqB;UAACM,OAAO,EAAEA,CAAA,KAAMlB,UAAU,CAAC,cAAc,CAAE;UAAAa,QAAA,EAAC;QAEnF;UAAAZ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAEN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAENnB,OAAA;MAAK2B,SAAS,EAAC,UAAU;MAAAC,QAAA,gBACvB5B,OAAA;QAAK2B,SAAS,EAAC,cAAc;QAAAC,QAAA,gBAC3B5B,OAAA;UAAK2B,SAAS,EAAC,cAAc;UAAAC,QAAA,eAC3B5B,OAAA;YAAA4B,QAAA,EAAK;UAAE;YAAAZ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,eACNnB,OAAA;UAAA4B,QAAA,EAAI;QAAc;UAAAZ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACvBnB,OAAA;UAAA4B,QAAA,EAAG;QAEH;UAAAZ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eAENnB,OAAA;QAAK2B,SAAS,EAAC,cAAc;QAAAC,QAAA,gBAC3B5B,OAAA;UAAK2B,SAAS,EAAC,cAAc;UAAAC,QAAA,eAC3B5B,OAAA;YAAA4B,QAAA,EAAK;UAAE;YAAAZ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,eACNnB,OAAA;UAAA4B,QAAA,EAAI;QAAc;UAAAZ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACvBnB,OAAA;UAAA4B,QAAA,EAAG;QAEH;UAAAZ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eAENnB,OAAA;QAAK2B,SAAS,EAAC,cAAc;QAAAC,QAAA,gBAC3B5B,OAAA;UAAK2B,SAAS,EAAC,cAAc;UAAAC,QAAA,eAC3B5B,OAAA;YAAA4B,QAAA,EAAK;UAAE;YAAAZ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,eACNnB,OAAA;UAAA4B,QAAA,EAAI;QAAkB;UAAAZ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC3BnB,OAAA;UAAA4B,QAAA,EAAG;QAEH;UAAAZ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAENnB,OAAA;MAAK2B,SAAS,EAAC,OAAO;MAAAC,QAAA,eACpB5B,OAAA;QAAK2B,SAAS,EAAC,YAAY;QAAAC,QAAA,gBACzB5B,OAAA;UAAK2B,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxB5B,OAAA;YAAK2B,SAAS,EAAC,aAAa;YAAAC,QAAA,EAAC;UAAI;YAAAZ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACvCnB,OAAA;YAAK2B,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAC;UAAc;YAAAZ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7C,CAAC,eACNnB,OAAA;UAAK2B,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxB5B,OAAA;YAAK2B,SAAS,EAAC,aAAa;YAAAC,QAAA,EAAC;UAAM;YAAAZ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACzCnB,OAAA;YAAK2B,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAC;UAAY;YAAAZ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3C,CAAC,eACNnB,OAAA;UAAK2B,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxB5B,OAAA;YAAK2B,SAAS,EAAC,aAAa;YAAAC,QAAA,EAAC;UAAG;YAAAZ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACtCnB,OAAA;YAAK2B,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAC;UAAY;YAAAZ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3C,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAENnB,OAAA;MAAK2B,SAAS,EAAC,KAAK;MAAAC,QAAA,gBAClB5B,OAAA;QAAA4B,QAAA,EAAI;MAAkB;QAAAZ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC3BnB,OAAA;QAAA4B,QAAA,EAAG;MAEH;QAAAZ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,eACJnB,OAAA;QAAQ2B,SAAS,EAAC,YAAY;QAACM,OAAO,EAAEA,CAAA,KAAMlB,UAAU,CAAC,OAAO,CAAE;QAAAa,QAAA,EAAC;MAEnE;QAAAZ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV;AAACqF,GAAA,GA/EQ1F,IAAI;AAiFb,SAASM,WAAWA,CAAC;EAAEL;AAAsB,CAAC,EAAE;EAAA0F,GAAA;EAC9C,MAAM;IAAElG,IAAI;IAAEmG;EAAQ,CAAC,GAAGrH,OAAO,CAAC,CAAC;;EAEnC;EACA,MAAMsH,YAAY,GAAGA,CAAA,KAAM;IACzB,MAAMC,KAAK,GAAG,IAAI9C,IAAI,CAAC,CAAC;IACxB,OAAO8C,KAAK,CAAC7C,WAAW,CAAC,CAAC,CAAC8C,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;EAC1C,CAAC;EAED,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGhI,QAAQ,CAAC;IACvC+F,KAAK,EAAE,EAAE;IACTC,IAAI,EAAE,EAAE;IACRiC,QAAQ,EAAE,EAAE;IACZjF,KAAK,EAAE,EAAE;IACTkF,MAAM,EAAE,EAAE;IACVC,SAAS,EAAE,EAAE;IACbC,WAAW,EAAE,EAAE;IACfC,SAAS,EAAE,EAAE;IACbC,WAAW,EAAE,EAAE;IACfC,UAAU,EAAE,EAAE;IACdC,QAAQ,EAAE,EAAE;IACZC,aAAa,EAAE,yBAAyB;IACxCC,SAAS,EAAEd,YAAY,CAAC,CAAC;IACzBe,WAAW,EAAE;EACf,CAAC,CAAC;EACF,MAAM,CAACC,cAAc,EAAEC,iBAAiB,CAAC,GAAG7I,QAAQ,CAAS,EAAE,CAAC;EAChE,MAAM,CAAC8I,YAAY,EAAEC,eAAe,CAAC,GAAG/I,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACgJ,aAAa,EAAEC,gBAAgB,CAAC,GAAGjJ,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAACkJ,OAAO,EAAEC,UAAU,CAAC,GAAGnJ,QAAQ,CAAW,EAAE,CAAC;;EAEpD;EACAC,SAAS,CAAC,MAAM;IACd,MAAMmJ,sBAAsB,GAAG,MAAAA,CAAA,KAAY;MACzC,IAAI;QACF,MAAMC,aAAa,GAAG,MAAMjJ,eAAe,CAACkJ,gBAAgB,CAAC,CAAC;QAC9DH,UAAU,CAACE,aAAa,CAAC;;QAEzB;QACA,IAAI7H,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEkB,EAAE,EAAE;UACZ,MAAM6G,aAAa,GAAG,MAAMnJ,eAAe,CAACoJ,oBAAoB,CAAChI,IAAI,CAACkB,EAAE,CAAC;UACzE,IAAI6G,aAAa,EAAE;YACjBvB,WAAW,CAACtD,IAAI,KAAK;cACnB,GAAGA,IAAI;cACP8D,QAAQ,EAAEe,aAAa,CAAC7G;YAC1B,CAAC,CAAC,CAAC;UACL;QACF;MACF,CAAC,CAAC,OAAOyB,KAAK,EAAE;QACdE,OAAO,CAACF,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;MAChD;IACF,CAAC;IAEDiF,sBAAsB,CAAC,CAAC;EAC1B,CAAC,EAAE,CAAC5H,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEkB,EAAE,CAAC,CAAC;;EAEd;EACA,IAAIiF,OAAO,EAAE;IACX,oBACE1G,OAAA;MAAK2B,SAAS,EAAC,gBAAgB;MAAAC,QAAA,gBAC7B5B,OAAA;QAAK2B,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1B5B,OAAA;UAAQ2B,SAAS,EAAC,aAAa;UAACM,OAAO,EAAEA,CAAA,KAAMlB,UAAU,CAAC,MAAM,CAAE;UAAAa,QAAA,EAAC;QAEnE;UAAAZ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTnB,OAAA;UAAA4B,QAAA,EAAI;QAAmB;UAAAZ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC5BnB,OAAA;UAAA4B,QAAA,EAAG;QAA4C;UAAAZ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChD,CAAC,eACNnB,OAAA;QAAK2B,SAAS,EAAC,eAAe;QAAAC,QAAA,gBAC5B5B,OAAA;UAAA4B,QAAA,EAAI;QAAuB;UAAAZ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAChCnB,OAAA;UAAA4B,QAAA,EAAG;QAAqI;UAAAZ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eAC5InB,OAAA;UAAQ2B,SAAS,EAAC,gBAAgB;UAACM,OAAO,EAAEA,CAAA,KAAMlB,UAAU,CAAC,OAAO,CAAE;UAAAa,QAAA,EAAC;QAEvE;UAAAZ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,MAAMqH,iBAAiB,GAAI7D,CAAgF,IAAK;IAC9G,MAAM;MAAE8D,IAAI;MAAEhE;IAAM,CAAC,GAAGE,CAAC,CAACC,MAAM;IAChCmC,WAAW,CAACtD,IAAI,KAAK;MACnB,GAAGA,IAAI;MACP,CAACgF,IAAI,GAAGhE;IACV,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAMiE,YAAY,GAAG,MAAO/D,CAAkB,IAAK;IACjDA,CAAC,CAACgE,cAAc,CAAC,CAAC;IAClBb,eAAe,CAAC,IAAI,CAAC;IACrBE,gBAAgB,CAAC,EAAE,CAAC;IAEpB,IAAI;MACF5E,OAAO,CAACC,GAAG,CAAC,yBAAyB,CAAC;MACtCD,OAAO,CAACC,GAAG,CAAC,OAAO,EAAE9C,IAAI,CAAC;MAC1B6C,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAEsE,cAAc,CAAC9C,MAAM,CAAC;;MAEtD;MACA,IAAI8C,cAAc,CAAC9C,MAAM,GAAG,CAAC,IAAI,CAACtE,IAAI,EAAE;QACtCyH,gBAAgB,CAAC,+CAA+C,CAAC;QACjE;MACF;;MAEA;MACA,MAAM;QAAEY;MAAU,CAAC,GAAG,MAAM3J,WAAW,CAAC4J,cAAc,CAAC,CAAC;MACxDzF,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAEuF,SAAS,CAAC;MAEnD,IAAI,CAACA,SAAS,EAAE;QACdZ,gBAAgB,CAAC,kEAAkE,CAAC;QACpF5E,OAAO,CAACC,GAAG,CAAC,YAAY,EAAEyD,QAAQ,EAAE,SAAS,EAAEa,cAAc,CAAC9C,MAAM,CAAC;QACrEiE,UAAU,CAAC,MAAM;UACf/H,UAAU,CAAC,MAAM,CAAC;QACpB,CAAC,EAAE,IAAI,CAAC;QACR;MACF;MAEA,IAAIgI,SAAmB,GAAG,EAAE;;MAE5B;MACA,IAAIpB,cAAc,CAAC9C,MAAM,GAAG,CAAC,EAAE;QAC7BzB,OAAO,CAACC,GAAG,CAAC,iCAAiC,EAAE9C,IAAI,CAAEkB,EAAE,CAAC;QACxDuG,gBAAgB,CAAC,qBAAqB,CAAC;QACvC,MAAM;UAAEgB,IAAI;UAAE9F,KAAK,EAAE+F;QAAW,CAAC,GAAG,MAAM/J,YAAY,CAACgK,YAAY,CAACvB,cAAc,EAAEpH,IAAI,CAAEkB,EAAE,CAAC;QAE7F2B,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAE;UAAE2F,IAAI;UAAE9F,KAAK,EAAE+F;QAAW,CAAC,CAAC;QAEhE,IAAIA,UAAU,EAAE;UACd7F,OAAO,CAACF,KAAK,CAAC,qBAAqB,EAAE+F,UAAU,CAAC;UAChDjB,gBAAgB,CAAC,2BAA2BiB,UAAU,CAACE,OAAO,IAAIC,IAAI,CAACC,SAAS,CAACJ,UAAU,CAAC,EAAE,CAAC;UAC/F;QACF;QAEAF,SAAS,GAAGC,IAAI;QAChB5F,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAE0F,SAAS,CAAC;MACzD;;MAEA;MACA,MAAMO,QAAQ,GAAG;QACfC,SAAS,EAAEhJ,IAAI,CAAEkB,EAAE;QAAE;QACrBqD,KAAK,EAAEgC,QAAQ,CAAChC,KAAK;QACrBC,IAAI,EAAE+B,QAAQ,CAAC/B,IAAI,IAAIoB,SAAS;QAChCnB,SAAS,EAAE8B,QAAQ,CAACE,QAAQ,IAAIb,SAAS;QACzCpE,KAAK,EAAE+E,QAAQ,CAAC/E,KAAK;QACrBkF,MAAM,EAAEH,QAAQ,CAACG,MAAM,GAAGuC,QAAQ,CAAC1C,QAAQ,CAACG,MAAM,CAAC,GAAGd,SAAS;QAC/De,SAAS,EAAEJ,QAAQ,CAACI,SAAS,IAAIf,SAAS;QAC1CsD,YAAY,EAAE3C,QAAQ,CAACK,WAAW,IAAIhB,SAAS;QAC/CuD,UAAU,EAAE5C,QAAQ,CAACM,SAAS,IAAIjB,SAAS;QAC3CwD,YAAY,EAAE7C,QAAQ,CAACO,WAAW,IAAIlB,SAAS;QAC/CyD,YAAY,EAAE9C,QAAQ,CAACQ,UAAU,IAAInB,SAAS;QAC9C0D,SAAS,EAAE/C,QAAQ,CAACS,QAAQ,IAAIpB,SAAS;QACzCL,cAAc,EAAEgB,QAAQ,CAACU,aAAa;QACtCzB,UAAU,EAAEe,QAAQ,CAACW,SAAS;QAC9BC,WAAW,EAAEZ,QAAQ,CAACY,WAAW,IAAIvB,SAAS;QAC9Cd,UAAU,EAAE0D,SAAS,CAAClE,MAAM,GAAG,CAAC,GAAGkE,SAAS,GAAG5C;MACjD,CAAC;MAED6B,gBAAgB,CAAC,4BAA4B,CAAC;MAC9C,MAAM;QAAE/E,IAAI;QAAEC;MAAM,CAAC,GAAG,MAAMjE,WAAW,CAAC6K,eAAe,CAACR,QAAQ,CAAC;MAEnE,IAAIpG,KAAK,EAAE;QACT;QACA,IAAI6F,SAAS,CAAClE,MAAM,GAAG,CAAC,EAAE;UACxB,MAAM3F,YAAY,CAAC6K,YAAY,CAAChB,SAAS,CAAC;QAC5C;QACAf,gBAAgB,CAAC,UAAU,CAAC9E,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAUiG,OAAO,KAAI,wBAAwB,EAAE,CAAC;MACnF,CAAC,MAAM;QACLnB,gBAAgB,CAAC,mCAAmC,CAAC;QACrD5E,OAAO,CAACC,GAAG,CAAC,aAAa,EAAEJ,IAAI,CAAC;QAChC6F,UAAU,CAAC,MAAM;UACf/H,UAAU,CAAC,MAAM,CAAC;QACpB,CAAC,EAAE,IAAI,CAAC;MACV;IACF,CAAC,CAAC,OAAOmC,KAAK,EAAE;MACd8E,gBAAgB,CAAC,kEAAkE,CAAC;MACpF5E,OAAO,CAACC,GAAG,CAAC,YAAY,EAAEyD,QAAQ,EAAE,SAAS,EAAEa,cAAc,CAAC9C,MAAM,CAAC;MACrEiE,UAAU,CAAC,MAAM;QACf/H,UAAU,CAAC,MAAM,CAAC;MACpB,CAAC,EAAE,IAAI,CAAC;IACV,CAAC,SAAS;MACR+G,eAAe,CAAC,KAAK,CAAC;IACxB;EACF,CAAC;EAED,oBACE9H,OAAA;IAAK2B,SAAS,EAAC,gBAAgB;IAAAC,QAAA,gBAC7B5B,OAAA;MAAK2B,SAAS,EAAC,aAAa;MAAAC,QAAA,gBAC1B5B,OAAA;QAAQ2B,SAAS,EAAC,aAAa;QAACM,OAAO,EAAEA,CAAA,KAAMlB,UAAU,CAAC,MAAM,CAAE;QAAAa,QAAA,EAAC;MAEnE;QAAAZ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACTnB,OAAA;QAAA4B,QAAA,EAAI;MAAmB;QAAAZ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC5BnB,OAAA;QAAA4B,QAAA,EAAG;MAAiF;QAAAZ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACrF,CAAC,EAEL,CAACZ,IAAI,iBACJP,OAAA;MAAK2B,SAAS,EAAC,aAAa;MAAAC,QAAA,gBAC1B5B,OAAA;QAAA4B,QAAA,gBACE5B,OAAA;UAAA4B,QAAA,EAAQ;QAAiB;UAAAZ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,sEACpC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,eACJnB,OAAA;QACE2B,SAAS,EAAC,gBAAgB;QAC1BM,OAAO,EAAEA,CAAA,KAAMlB,UAAU,CAAC,OAAO,CAAE;QAAAa,QAAA,EACpC;MAED;QAAAZ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CACN,EAEA4G,aAAa,iBACZ/H,OAAA;MAAK2B,SAAS,EAAE,kBAAkBoG,aAAa,CAACiC,QAAQ,CAAC,OAAO,CAAC,GAAG,OAAO,GAAG,SAAS,EAAG;MAAApI,QAAA,EACvFmG;IAAa;MAAA/G,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACX,CACN,eAEDnB,OAAA;MAAM2B,SAAS,EAAC,WAAW;MAACsI,QAAQ,EAAEvB,YAAa;MAAC7G,KAAK,EAAE;QAAEqI,OAAO,EAAE,CAAC3J,IAAI,GAAG,GAAG,GAAG;MAAE,CAAE;MAAAqB,QAAA,gBACtF5B,OAAA;QAAK2B,SAAS,EAAC,cAAc;QAAAC,QAAA,gBAC3B5B,OAAA;UAAA4B,QAAA,EAAI;QAAgB;UAAAZ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACzBnB,OAAA;UAAK2B,SAAS,EAAC,UAAU;UAAAC,QAAA,gBACvB5B,OAAA;YAAK2B,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzB5B,OAAA;cAAOwE,OAAO,EAAC,OAAO;cAAA5C,QAAA,EAAC;YAAO;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACtCnB,OAAA;cACEmK,IAAI,EAAC,MAAM;cACX1I,EAAE,EAAC,OAAO;cACVgH,IAAI,EAAC,OAAO;cACZhE,KAAK,EAAEqC,QAAQ,CAAChC,KAAM;cACtBJ,QAAQ,EAAE8D,iBAAkB;cAC5B4B,QAAQ;cACRC,WAAW,EAAC;YAAuC;cAAArJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eACNnB,OAAA;YAAK2B,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzB5B,OAAA;cAAOwE,OAAO,EAAC,MAAM;cAAA5C,QAAA,EAAC;YAAM;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACpCnB,OAAA;cACEmK,IAAI,EAAC,MAAM;cACX1I,EAAE,EAAC,MAAM;cACTgH,IAAI,EAAC,MAAM;cACXhE,KAAK,EAAEqC,QAAQ,CAAC/B,IAAK;cACrBL,QAAQ,EAAE8D,iBAAkB;cAC5B4B,QAAQ;cACRC,WAAW,EAAC;YAA+B;cAAArJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5C,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENnB,OAAA;UAAK2B,SAAS,EAAC,UAAU;UAAAC,QAAA,gBACvB5B,OAAA;YAAK2B,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzB5B,OAAA;cAAOwE,OAAO,EAAC,UAAU;cAAA5C,QAAA,EAAC;YAAS;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC3CnB,OAAA;cACEmK,IAAI,EAAC,MAAM;cACX1I,EAAE,EAAC,UAAU;cACbgH,IAAI,EAAC,UAAU;cACfhE,KAAK,EAAEqC,QAAQ,CAACE,QAAS;cACzBtC,QAAQ,EAAE8D,iBAAkB;cAC5B6B,WAAW,EAAC;YAAyD;cAAArJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eACNnB,OAAA;YAAK2B,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzB5B,OAAA;cAAOwE,OAAO,EAAC,OAAO;cAAA5C,QAAA,EAAC;YAAO;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACtCnB,OAAA;cACEmK,IAAI,EAAC,MAAM;cACX1I,EAAE,EAAC,OAAO;cACVgH,IAAI,EAAC,OAAO;cACZhE,KAAK,EAAEqC,QAAQ,CAAC/E,KAAM;cACtB2C,QAAQ,EAAE8D,iBAAkB;cAC5B4B,QAAQ;cACRC,WAAW,EAAC;YAAyB;cAAArJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENnB,OAAA;UAAK2B,SAAS,EAAC,UAAU;UAAAC,QAAA,gBACvB5B,OAAA;YAAK2B,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzB5B,OAAA;cAAOwE,OAAO,EAAC,QAAQ;cAAA5C,QAAA,EAAC;YAAc;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC9CnB,OAAA;cACEmK,IAAI,EAAC,QAAQ;cACb1I,EAAE,EAAC,QAAQ;cACXgH,IAAI,EAAC,QAAQ;cACbhE,KAAK,EAAEqC,QAAQ,CAACG,MAAO;cACvBvC,QAAQ,EAAE8D,iBAAkB;cAC5B6B,WAAW,EAAC,WAAW;cACvBC,GAAG,EAAC,KAAK;cACTC,GAAG,EAAC;YAAK;cAAAvJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eACNnB,OAAA;YAAK2B,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzB5B,OAAA;cAAOwE,OAAO,EAAC,WAAW;cAAA5C,QAAA,EAAC;YAAS;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC5CnB,OAAA;cACEmK,IAAI,EAAC,MAAM;cACX1I,EAAE,EAAC,WAAW;cACdgH,IAAI,EAAC,WAAW;cAChBhE,KAAK,EAAEqC,QAAQ,CAACI,SAAU;cAC1BxC,QAAQ,EAAE8D,iBAAkB;cAC5B6B,WAAW,EAAC;YAAwC;cAAArJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENnB,OAAA;QAAK2B,SAAS,EAAC,cAAc;QAAAC,QAAA,gBAC3B5B,OAAA;UAAA4B,QAAA,EAAI;QAAkB;UAAAZ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC3BnB,OAAA;UAAK2B,SAAS,EAAC,UAAU;UAAAC,QAAA,gBACvB5B,OAAA;YAAK2B,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzB5B,OAAA;cAAOwE,OAAO,EAAC,aAAa;cAAA5C,QAAA,EAAC;YAAY;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACjDnB,OAAA;cACEmK,IAAI,EAAC,MAAM;cACX1I,EAAE,EAAC,aAAa;cAChBgH,IAAI,EAAC,aAAa;cAClBhE,KAAK,EAAEqC,QAAQ,CAACK,WAAY;cAC5BzC,QAAQ,EAAE8D,iBAAkB;cAC5B6B,WAAW,EAAC;YAA4B;cAAArJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eACNnB,OAAA;YAAK2B,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzB5B,OAAA;cAAOwE,OAAO,EAAC,WAAW;cAAA5C,QAAA,EAAC;YAAU;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC7CnB,OAAA;cACEmK,IAAI,EAAC,MAAM;cACX1I,EAAE,EAAC,WAAW;cACdgH,IAAI,EAAC,WAAW;cAChBhE,KAAK,EAAEqC,QAAQ,CAACM,SAAU;cAC1B1C,QAAQ,EAAE8D,iBAAkB;cAC5B6B,WAAW,EAAC;YAAgC;cAAArJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7C,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENnB,OAAA;UAAK2B,SAAS,EAAC,UAAU;UAAAC,QAAA,gBACvB5B,OAAA;YAAK2B,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzB5B,OAAA;cAAOwE,OAAO,EAAC,aAAa;cAAA5C,QAAA,EAAC;YAAoB;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACzDnB,OAAA;cACEmK,IAAI,EAAC,KAAK;cACV1I,EAAE,EAAC,aAAa;cAChBgH,IAAI,EAAC,aAAa;cAClBhE,KAAK,EAAEqC,QAAQ,CAACO,WAAY;cAC5B3C,QAAQ,EAAE8D,iBAAkB;cAC5B6B,WAAW,EAAC;YAA8B;cAAArJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3C,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eACNnB,OAAA;YAAK2B,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzB5B,OAAA;cAAOwE,OAAO,EAAC,YAAY;cAAA5C,QAAA,EAAC;YAAY;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAChDnB,OAAA;cACEmK,IAAI,EAAC,MAAM;cACX1I,EAAE,EAAC,YAAY;cACfgH,IAAI,EAAC,YAAY;cACjBhE,KAAK,EAAEqC,QAAQ,CAACQ,UAAW;cAC3B5C,QAAQ,EAAE8D,iBAAkB;cAC5B6B,WAAW,EAAC;YAAsB;cAAArJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENnB,OAAA;QAAK2B,SAAS,EAAC,cAAc;QAAAC,QAAA,gBAC3B5B,OAAA;UAAA4B,QAAA,EAAI;QAAe;UAAAZ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACxBnB,OAAA;UAAK2B,SAAS,EAAC,UAAU;UAAAC,QAAA,gBACvB5B,OAAA;YAAK2B,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzB5B,OAAA;cAAOwE,OAAO,EAAC,UAAU;cAAA5C,QAAA,EAAC;YAAQ;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC1CnB,OAAA;cACEyB,EAAE,EAAC,UAAU;cACbgH,IAAI,EAAC,UAAU;cACfhE,KAAK,EAAEqC,QAAQ,CAACS,QAAS;cACzB7C,QAAQ,EAAE8D,iBAAkB;cAC5B4B,QAAQ;cACRzI,SAAS,EAAC,aAAa;cAAAC,QAAA,gBAEvB5B,OAAA;gBAAQyE,KAAK,EAAC,EAAE;gBAAA7C,QAAA,EAAC;cAA8B;gBAAAZ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,EACvD8G,OAAO,CAACvE,GAAG,CAAE8G,MAAM,iBAClBxK,OAAA;gBAAwByE,KAAK,EAAE+F,MAAM,CAAC/I,EAAG;gBAAAG,QAAA,EACtC4I,MAAM,CAAC/B;cAAI,GADD+B,MAAM,CAAC/I,EAAE;gBAAAT,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAEd,CACT,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC,eACTnB,OAAA;cAAO2B,SAAS,EAAC,WAAW;cAAAC,QAAA,EAAC;YAA6D;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/F,CAAC,eACNnB,OAAA;YAAK2B,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzB5B,OAAA;cAAOwE,OAAO,EAAC,WAAW;cAAA5C,QAAA,EAAC;YAAY;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC/CnB,OAAA;cACEmK,IAAI,EAAC,MAAM;cACX1I,EAAE,EAAC,WAAW;cACdgH,IAAI,EAAC,WAAW;cAChBhE,KAAK,EAAEqC,QAAQ,CAACW,SAAU;cAC1B/C,QAAQ,EAAE8D,iBAAkB;cAC5B4B,QAAQ;YAAA;cAAApJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACNnB,OAAA;UAAK2B,SAAS,EAAC,UAAU;UAAAC,QAAA,eACvB5B,OAAA;YAAK2B,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzB5B,OAAA;cAAOwE,OAAO,EAAC,eAAe;cAAA5C,QAAA,EAAC;YAAmB;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC1DnB,OAAA;cACEmK,IAAI,EAAC,MAAM;cACX1I,EAAE,EAAC,eAAe;cAClBgH,IAAI,EAAC,eAAe;cACpBhE,KAAK,EAAEqC,QAAQ,CAACU,aAAc;cAC9B9C,QAAQ,EAAE8D,iBAAkB;cAC5B4B,QAAQ;cACRC,WAAW,EAAC;YAA6D;cAAArJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1E,CAAC,eACFnB,OAAA;cAAO2B,SAAS,EAAC,WAAW;cAAAC,QAAA,EAAC;YAAsH;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxJ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENnB,OAAA;UAAK2B,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzB5B,OAAA;YAAOwE,OAAO,EAAC,aAAa;YAAA5C,QAAA,EAAC;UAAsB;YAAAZ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAC3DnB,OAAA;YACEyB,EAAE,EAAC,aAAa;YAChBgH,IAAI,EAAC,aAAa;YAClBhE,KAAK,EAAEqC,QAAQ,CAACY,WAAY;YAC5BhD,QAAQ,EAAE8D,iBAAkB;YAC5BiC,IAAI,EAAE,CAAE;YACRJ,WAAW,EAAC;UAAiE;YAAArJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9E,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENnB,OAAA;QAAK2B,SAAS,EAAC,cAAc;QAAAC,QAAA,gBAC3B5B,OAAA;UAAA4B,QAAA,EAAI;QAAW;UAAAZ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACpBnB,OAAA;UAAG2B,SAAS,EAAC,0BAA0B;UAAAC,QAAA,EAAC;QAExC;UAAAZ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACJnB,OAAA,CAACV,WAAW;UACVoL,cAAc,EAAE9C,iBAAkB;UAClC+C,SAAS,EAAE,CAAE;UACbC,eAAe,EAAE,EAAG;UACpBxF,QAAQ,EAAEyC;QAAa;UAAA7G,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAENnB,OAAA;QAAK2B,SAAS,EAAC,cAAc;QAAAC,QAAA,gBAC3B5B,OAAA;UACEmK,IAAI,EAAC,QAAQ;UACbxI,SAAS,EAAC,kBAAkB;UAC5BM,OAAO,EAAEA,CAAA,KAAMlB,UAAU,CAAC,MAAM,CAAE;UAClCqE,QAAQ,EAAEyC,YAAa;UAAAjG,QAAA,EACxB;QAED;UAAAZ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTnB,OAAA;UACEmK,IAAI,EAAC,QAAQ;UACbxI,SAAS,EAAC,gBAAgB;UAC1ByD,QAAQ,EAAEyC,YAAY,IAAI,CAACtH,IAAK;UAAAqB,QAAA,EAE/BiG,YAAY,GAAG,eAAe,GAAG,CAACtH,IAAI,GAAG,kBAAkB,GAAG;QAAmB;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5E,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEV;AAACsF,GAAA,CA5bQrF,WAAW;EAAA,QACQ/B,OAAO;AAAA;AAAAwL,GAAA,GAD1BzJ,WAAW;AA8bpB,SAASC,UAAUA,CAAC;EAAEN;AAAsB,CAAC,EAAE;EAAA+J,GAAA;EAC7C,MAAM;IAAEtK;EAAS,CAAC,GAAGnB,OAAO,CAAC,CAAC;EAC9B,MAAM,CAAC0L,cAAc,EAAEC,iBAAiB,CAAC,GAAGjM,QAAQ,CAAC;IACnD+F,KAAK,EAAE,EAAE;IACTC,IAAI,EAAE,EAAE;IACRhD,KAAK,EAAE,EAAE;IACTiF,QAAQ,EAAE,EAAE;IACZQ,aAAa,EAAE,EAAE;IACjByD,MAAM,EAAE;EACV,CAAC,CAAC;EACF,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGpM,QAAQ,CAAQ,EAAE,CAAC;EACvD,MAAM,CAACqM,WAAW,EAAEC,cAAc,CAAC,GAAGtM,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACuM,WAAW,EAAEC,cAAc,CAAC,GAAGxM,QAAQ,CAAC,KAAK,CAAC;EAErD,MAAMuE,wBAAwB,GAAGA,CAACC,MAAc,EAAEC,SAAuB,KAAK;IAC5E;IACA2H,aAAa,CAAC1H,IAAI,IAAIA,IAAI,CAACC,GAAG,CAACC,IAAI,IACjCA,IAAI,CAAClC,EAAE,KAAK8B,MAAM,GACd;MAAE,GAAGI,IAAI;MAAEC,aAAa,EAAEJ,SAAS;MAAEK,WAAW,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;IAAE,CAAC,GAC5EJ,IACN,CAAC,CAAC;EACJ,CAAC;EAED,MAAM6E,iBAAiB,GAAI7D,CAA0D,IAAK;IACxF,MAAM;MAAE8D,IAAI;MAAEhE;IAAM,CAAC,GAAGE,CAAC,CAACC,MAAM;IAChCoG,iBAAiB,CAACvH,IAAI,KAAK;MACzB,GAAGA,IAAI;MACP,CAACgF,IAAI,GAAGhE;IACV,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAM+G,YAAY,GAAG,MAAO7G,CAAkB,IAAK;IACjDA,CAAC,CAACgE,cAAc,CAAC,CAAC;IAClB0C,cAAc,CAAC,IAAI,CAAC;IACpBE,cAAc,CAAC,IAAI,CAAC;IAEpB,IAAI;MACF,MAAM;QAAEtI,IAAI;QAAEC;MAAM,CAAC,GAAG,MAAMjE,WAAW,CAACwM,gBAAgB,CAACV,cAAc,CAAC;MAE1E,IAAI7H,KAAK,EAAE;QACTE,OAAO,CAACF,KAAK,CAAC,eAAe,EAAEA,KAAK,CAAC;QACrCiI,aAAa,CAAC,EAAE,CAAC;MACnB,CAAC,MAAM;QACL/H,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEJ,IAAI,CAAC;QACpCkI,aAAa,CAAClI,IAAI,IAAI,EAAE,CAAC;MAC3B;IACF,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdE,OAAO,CAACF,KAAK,CAAC,gBAAgB,EAAEA,KAAK,CAAC;MACtCiI,aAAa,CAAC,EAAE,CAAC;IACnB,CAAC,SAAS;MACRE,cAAc,CAAC,KAAK,CAAC;IACvB;EACF,CAAC;EAED,MAAMrI,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/BqI,cAAc,CAAC,IAAI,CAAC;IACpBE,cAAc,CAAC,IAAI,CAAC;IAEpB,IAAI;MACF,MAAM;QAAEtI,IAAI;QAAEC;MAAM,CAAC,GAAG,MAAMjE,WAAW,CAACyM,aAAa,CAAC,CAAC;MAEzD,IAAIxI,KAAK,EAAE;QACTE,OAAO,CAACF,KAAK,CAAC,aAAa,EAAEA,KAAK,CAAC;QACnCiI,aAAa,CAAC,EAAE,CAAC;MACnB,CAAC,MAAM;QACL/H,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEJ,IAAI,CAAC;QACtCkI,aAAa,CAAClI,IAAI,IAAI,EAAE,CAAC;MAC3B;IACF,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdE,OAAO,CAACF,KAAK,CAAC,cAAc,EAAEA,KAAK,CAAC;MACpCiI,aAAa,CAAC,EAAE,CAAC;IACnB,CAAC,SAAS;MACRE,cAAc,CAAC,KAAK,CAAC;IACvB;EACF,CAAC;EAED,oBACErL,OAAA;IAAK2B,SAAS,EAAC,gBAAgB;IAAAC,QAAA,gBAC7B5B,OAAA;MAAK2B,SAAS,EAAC,aAAa;MAAAC,QAAA,gBAC1B5B,OAAA;QAAQ2B,SAAS,EAAC,aAAa;QAACM,OAAO,EAAEA,CAAA,KAAMlB,UAAU,CAAC,MAAM,CAAE;QAAAa,QAAA,EAAC;MAEnE;QAAAZ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACTnB,OAAA;QAAA4B,QAAA,EAAI;MAAiB;QAAAZ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC1BnB,OAAA;QAAA4B,QAAA,EAAG;MAA+E;QAAAZ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACnF,CAAC,eAENnB,OAAA;MAAK2B,SAAS,EAAC,kBAAkB;MAAAC,QAAA,gBAC/B5B,OAAA;QAAM2B,SAAS,EAAC,aAAa;QAACsI,QAAQ,EAAEuB,YAAa;QAAA5J,QAAA,gBACnD5B,OAAA;UAAK2B,SAAS,EAAC,gBAAgB;UAAAC,QAAA,gBAC7B5B,OAAA;YAAA4B,QAAA,EAAI;UAAe;YAAAZ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACxBnB,OAAA;YAAK2B,SAAS,EAAC,UAAU;YAAAC,QAAA,gBACvB5B,OAAA;cAAK2B,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzB5B,OAAA;gBAAOwE,OAAO,EAAC,gBAAgB;gBAAA5C,QAAA,EAAC;cAAO;gBAAAZ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC/CnB,OAAA;gBACEmK,IAAI,EAAC,QAAQ;gBACb1I,EAAE,EAAC,gBAAgB;gBACnBgH,IAAI,EAAC,QAAQ;gBACbhE,KAAK,EAAEsG,cAAc,CAACE,MAAO;gBAC7BvG,QAAQ,EAAE8D,iBAAkB;gBAC5B6B,WAAW,EAAC;cAAsB;gBAAArJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNnB,OAAA;cAAK2B,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzB5B,OAAA;gBAAOwE,OAAO,EAAC,cAAc;gBAAA5C,QAAA,EAAC;cAAK;gBAAAZ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC3CnB,OAAA;gBACEmK,IAAI,EAAC,MAAM;gBACX1I,EAAE,EAAC,cAAc;gBACjBgH,IAAI,EAAC,OAAO;gBACZhE,KAAK,EAAEsG,cAAc,CAACjG,KAAM;gBAC5BJ,QAAQ,EAAE8D,iBAAkB;gBAC5B6B,WAAW,EAAC;cAAwB;gBAAArJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENnB,OAAA;YAAK2B,SAAS,EAAC,UAAU;YAAAC,QAAA,eACvB5B,OAAA;cAAK2B,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzB5B,OAAA;gBAAOwE,OAAO,EAAC,aAAa;gBAAA5C,QAAA,EAAC;cAAI;gBAAAZ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACzCnB,OAAA;gBACEmK,IAAI,EAAC,MAAM;gBACX1I,EAAE,EAAC,aAAa;gBAChBgH,IAAI,EAAC,MAAM;gBACXhE,KAAK,EAAEsG,cAAc,CAAChG,IAAK;gBAC3BL,QAAQ,EAAE8D,iBAAkB;gBAC5B6B,WAAW,EAAC;cAAwB;gBAAArJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENnB,OAAA;YAAK2B,SAAS,EAAC,UAAU;YAAAC,QAAA,gBACvB5B,OAAA;cAAK2B,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzB5B,OAAA;gBAAOwE,OAAO,EAAC,cAAc;gBAAA5C,QAAA,EAAC;cAAK;gBAAAZ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC3CnB,OAAA;gBACEmK,IAAI,EAAC,MAAM;gBACX1I,EAAE,EAAC,cAAc;gBACjBgH,IAAI,EAAC,OAAO;gBACZhE,KAAK,EAAEsG,cAAc,CAAChJ,KAAM;gBAC5B2C,QAAQ,EAAE8D,iBAAkB;gBAC5B6B,WAAW,EAAC;cAAiB;gBAAArJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9B,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNnB,OAAA;cAAK2B,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzB5B,OAAA;gBAAOwE,OAAO,EAAC,iBAAiB;gBAAA5C,QAAA,EAAC;cAAS;gBAAAZ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAClDnB,OAAA;gBACEyB,EAAE,EAAC,iBAAiB;gBACpBgH,IAAI,EAAC,UAAU;gBACfhE,KAAK,EAAEsG,cAAc,CAAC/D,QAAS;gBAC/BtC,QAAQ,EAAE8D,iBAAkB;gBAAA5G,QAAA,gBAE5B5B,OAAA;kBAAQyE,KAAK,EAAC,EAAE;kBAAA7C,QAAA,EAAC;gBAAQ;kBAAAZ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAClCnB,OAAA;kBAAQyE,KAAK,EAAC,QAAQ;kBAAA7C,QAAA,EAAC;gBAAM;kBAAAZ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACtCnB,OAAA;kBAAQyE,KAAK,EAAC,UAAU;kBAAA7C,QAAA,EAAC;gBAAQ;kBAAAZ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAC1CnB,OAAA;kBAAQyE,KAAK,EAAC,gBAAgB;kBAAA7C,QAAA,EAAC;gBAAc;kBAAAZ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACtDnB,OAAA;kBAAQyE,KAAK,EAAC,iBAAiB;kBAAA7C,QAAA,EAAC;gBAAe;kBAAAZ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACxDnB,OAAA;kBAAQyE,KAAK,EAAC,UAAU;kBAAA7C,QAAA,EAAC;gBAAQ;kBAAAZ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENnB,OAAA;YAAK2B,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzB5B,OAAA;cAAOwE,OAAO,EAAC,iBAAiB;cAAA5C,QAAA,EAAC;YAAQ;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACjDnB,OAAA;cACEmK,IAAI,EAAC,MAAM;cACX1I,EAAE,EAAC,iBAAiB;cACpBgH,IAAI,EAAC,eAAe;cACpBhE,KAAK,EAAEsG,cAAc,CAACvD,aAAc;cACpC9C,QAAQ,EAAE8D,iBAAkB;cAC5B6B,WAAW,EAAC;YAA6B;cAAArJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1C,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENnB,OAAA;UAAK2B,SAAS,EAAC,gBAAgB;UAAAC,QAAA,gBAC7B5B,OAAA;YACEmK,IAAI,EAAC,QAAQ;YACbxI,SAAS,EAAC,kBAAkB;YAC5BM,OAAO,EAAEe,YAAa;YACtBoC,QAAQ,EAAEgG,WAAY;YAAAxJ,QAAA,EAErBwJ,WAAW,GAAG,YAAY,GAAG;UAAsB;YAAApK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9C,CAAC,eACTnB,OAAA;YACEmK,IAAI,EAAC,QAAQ;YACbxI,SAAS,EAAC,gBAAgB;YAC1ByD,QAAQ,EAAEgG,WAAY;YAAAxJ,QAAA,EAErBwJ,WAAW,GAAG,cAAc,GAAG;UAAQ;YAAApK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,EAENmK,WAAW,iBACVtL,OAAA;QAAK2B,SAAS,EAAC,gBAAgB;QAAAC,QAAA,gBAC7B5B,OAAA;UAAA4B,QAAA,EACGsJ,UAAU,CAACrG,MAAM,GAAG,CAAC,GAClB,SAASqG,UAAU,CAACrG,MAAM,QAAQqG,UAAU,CAACrG,MAAM,KAAK,CAAC,GAAG,EAAE,GAAG,GAAG,EAAE,GACtE;QAAuC;UAAA7D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAEzC,CAAC,EAEJ+J,UAAU,CAACrG,MAAM,GAAG,CAAC,iBACpB7E,OAAA;UAAK2B,SAAS,EAAC,WAAW;UAAAC,QAAA,EACvBsJ,UAAU,CAACxH,GAAG,CAAEC,IAAI,iBACnB3D,OAAA;YAAmB2B,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACtC5B,OAAA;cAAK2B,SAAS,EAAC,aAAa;cAAAC,QAAA,gBAC1B5B,OAAA;gBAAA4B,QAAA,GAAK+B,IAAI,CAACmB,KAAK,EAAC,GAAC,EAACnB,IAAI,CAACoB,IAAI,IAAI,cAAc;cAAA;gBAAA/D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACnDnB,OAAA;gBAAK2B,SAAS,EAAC,WAAW;gBAAAC,QAAA,gBACxB5B,OAAA;kBAAM2B,SAAS,EAAC,WAAW;kBAAAC,QAAA,EAAE+B,IAAI,CAACqB,SAAS,IAAI;gBAAc;kBAAAhE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,EACpEwC,IAAI,CAACsB,OAAO,iBAAIjF,OAAA;kBAAM2B,SAAS,EAAC,SAAS;kBAAAC,QAAA,GAAC,QAAM,EAAC+B,IAAI,CAACsB,OAAO;gBAAA;kBAAAjE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,EAGL,CAACX,QAAQ,KAAK,OAAO,IAAKmD,IAAI,CAACC,aAAa,IAAID,IAAI,CAACC,aAAa,KAAK,OAAQ,kBAC9E5D,OAAA,CAACT,mBAAmB;cAClBgE,MAAM,EAAEI,IAAI,CAAClC,EAAG;cAChByD,aAAa,EAAEvB,IAAI,CAACC,aAAa,IAAI,OAAQ;cAC7CuB,eAAe,EAAG3B,SAAS,IAAKF,wBAAwB,CAACK,IAAI,CAAClC,EAAE,EAAE+B,SAAS,CAAE;cAC7E4B,QAAQ,EAAE5E,QAAQ,KAAK;YAAQ;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChC,CACF,EAEAwC,IAAI,CAAC0B,UAAU,IAAI1B,IAAI,CAAC0B,UAAU,CAACR,MAAM,GAAG,CAAC,iBAC5C7E,OAAA;cAAK2B,SAAS,EAAC,aAAa;cAAAC,QAAA,EACzB+B,IAAI,CAAC0B,UAAU,CAACC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC5B,GAAG,CAAC,CAAC6B,QAAgB,EAAEC,KAAa,kBAC/DxF,OAAA;gBAEEyF,GAAG,EAAEF,QAAS;gBACdG,GAAG,EAAE,GAAG/B,IAAI,CAACmB,KAAK,IAAInB,IAAI,CAACoB,IAAI,IAAI,MAAM,IAAIS,KAAK,GAAG,CAAC,EAAG;gBACzD7D,SAAS,EAAC,YAAY;gBACtBgE,OAAO,EAAGhB,CAAC,IAAK;kBACd;kBACCA,CAAC,CAACC,MAAM,CAAsB/C,KAAK,CAAC+D,OAAO,GAAG,MAAM;gBACvD;cAAE,GAPGJ,KAAK;gBAAAxE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAQX,CACF;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CACN,eAEDnB,OAAA;cAAK2B,SAAS,EAAC,cAAc;cAAAC,QAAA,gBAC3B5B,OAAA;gBAAK2B,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzB5B,OAAA;kBAAM2B,SAAS,EAAC,OAAO;kBAAAC,QAAA,EAAC;gBAAM;kBAAAZ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACrCnB,OAAA;kBAAM2B,SAAS,EAAC,OAAO;kBAAAC,QAAA,EAAE+B,IAAI,CAAC5B;gBAAK;kBAAAf,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxC,CAAC,EAELwC,IAAI,CAACsD,MAAM,iBACVjH,OAAA;gBAAK2B,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzB5B,OAAA;kBAAM2B,SAAS,EAAC,OAAO;kBAAAC,QAAA,EAAC;gBAAO;kBAAAZ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACtCnB,OAAA;kBAAM2B,SAAS,EAAC,OAAO;kBAAAC,QAAA,GAAE+B,IAAI,CAACsD,MAAM,EAAC,GAAC;gBAAA;kBAAAjG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1C,CACN,eAEDnB,OAAA;gBAAK2B,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzB5B,OAAA;kBAAM2B,SAAS,EAAC,OAAO;kBAAAC,QAAA,EAAC;gBAAU;kBAAAZ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACzCnB,OAAA;kBAAM2B,SAAS,EAAC,OAAO;kBAAAC,QAAA,EAAE+B,IAAI,CAACuD,SAAS,IAAI;gBAAS;kBAAAlG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzD,CAAC,EAELwC,IAAI,CAACkC,WAAW,iBACf7F,OAAA;gBAAK2B,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzB5B,OAAA;kBAAM2B,SAAS,EAAC,OAAO;kBAAAC,QAAA,EAAC;gBAAO;kBAAAZ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACtCnB,OAAA;kBAAM2B,SAAS,EAAC,OAAO;kBAAAC,QAAA,EAAE+B,IAAI,CAACkC;gBAAW;kBAAA7E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9C,CACN,eAEDnB,OAAA;gBAAK2B,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzB5B,OAAA;kBAAM2B,SAAS,EAAC,OAAO;kBAAAC,QAAA,EAAC;gBAAkB;kBAAAZ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACjDnB,OAAA;kBAAM2B,SAAS,EAAC,OAAO;kBAAAC,QAAA,EAAE+B,IAAI,CAACmC;gBAAc;kBAAA9E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjD,CAAC,eAENnB,OAAA;gBAAK2B,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzB5B,OAAA;kBAAM2B,SAAS,EAAC,OAAO;kBAAAC,QAAA,EAAC;gBAAS;kBAAAZ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACxCnB,OAAA;kBAAM2B,SAAS,EAAC,OAAO;kBAAAC,QAAA,EAAE,IAAIkC,IAAI,CAACH,IAAI,CAACoC,UAAU,CAAC,CAACC,kBAAkB,CAAC;gBAAC;kBAAAhF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5E,CAAC,EAELwC,IAAI,CAACgG,YAAY,iBAChB3J,OAAA;gBAAK2B,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzB5B,OAAA;kBAAM2B,SAAS,EAAC,OAAO;kBAAAC,QAAA,EAAC;gBAAc;kBAAAZ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC7CnB,OAAA;kBAAM2B,SAAS,EAAC,OAAO;kBAAAC,QAAA,EAAE+B,IAAI,CAACgG;gBAAY;kBAAA3I,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/C,CACN,EAEAwC,IAAI,CAACiG,YAAY,iBAChB5J,OAAA;gBAAK2B,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzB5B,OAAA;kBAAM2B,SAAS,EAAC,OAAO;kBAAAC,QAAA,EAAC;gBAAa;kBAAAZ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC5CnB,OAAA;kBAAM2B,SAAS,EAAC,OAAO;kBAAAC,QAAA,EAAE+B,IAAI,CAACiG;gBAAY;kBAAA5I,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/C,CACN,EAEAwC,IAAI,CAAC+D,WAAW,iBACf1H,OAAA;gBAAK2B,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzB5B,OAAA;kBAAM2B,SAAS,EAAC,OAAO;kBAAAC,QAAA,EAAC;gBAAY;kBAAAZ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC3CnB,OAAA;kBAAM2B,SAAS,EAAC,OAAO;kBAAAC,QAAA,EAAE+B,IAAI,CAAC+D;gBAAW;kBAAA1G,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9C,CACN;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eAENnB,OAAA;cAAK2B,SAAS,EAAC,cAAc;cAAAC,QAAA,gBAC3B5B,OAAA;gBAAQ2B,SAAS,EAAC,sBAAsB;gBAAAC,QAAA,EAAC;cAEzC;gBAAAZ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACTnB,OAAA;gBAAQ2B,SAAS,EAAC,wBAAwB;gBAAAC,QAAA,EAAC;cAE3C;gBAAAZ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA,GApGEwC,IAAI,CAAClC,EAAE;YAAAT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAqGZ,CACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV;AAAC2J,GAAA,CAzTQzJ,UAAU;EAAA,QACIhC,OAAO;AAAA;AAAAsM,GAAA,GADrBtK,UAAU;AA2TnB,SAASC,KAAKA,CAAC;EAAEP;AAAsB,CAAC,EAAE;EAAA6K,GAAA;EACxC,MAAM;IAAEC,MAAM;IAAEC;EAAO,CAAC,GAAGzM,OAAO,CAAC,CAAC;EACpC,MAAM,CAAC0M,OAAO,EAAEC,UAAU,CAAC,GAAGjN,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAAC+H,QAAQ,EAAEC,WAAW,CAAC,GAAGhI,QAAQ,CAAC;IACvCoD,KAAK,EAAE,EAAE;IACT8J,QAAQ,EAAE,EAAE;IACZC,QAAQ,EAAE,EAAE;IACZC,eAAe,EAAE;EACnB,CAAC,CAAC;EACF,MAAM,CAACzL,OAAO,EAAE0L,UAAU,CAAC,GAAGrN,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACoK,OAAO,EAAEkD,UAAU,CAAC,GAAGtN,QAAQ,CAAC,EAAE,CAAC;EAE1C,MAAMyJ,iBAAiB,GAAI7D,CAAsC,IAAK;IACpE,MAAM;MAAE8D,IAAI;MAAEhE;IAAM,CAAC,GAAGE,CAAC,CAACC,MAAM;IAChCmC,WAAW,CAACtD,IAAI,KAAK;MACnB,GAAGA,IAAI;MACP,CAACgF,IAAI,GAAGhE;IACV,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAMiE,YAAY,GAAG,MAAO/D,CAAkB,IAAK;IACjDA,CAAC,CAACgE,cAAc,CAAC,CAAC;IAClByD,UAAU,CAAC,IAAI,CAAC;IAChBC,UAAU,CAAC,EAAE,CAAC;IAEd,IAAI;MACF,IAAIN,OAAO,EAAE;QACX,MAAM;UAAE7I;QAAM,CAAC,GAAG,MAAM2I,MAAM,CAAC/E,QAAQ,CAAC3E,KAAK,EAAE2E,QAAQ,CAACmF,QAAQ,CAAC;QACjE,IAAI/I,KAAK,EAAE;UACTmJ,UAAU,CAACnJ,KAAK,CAACiG,OAAO,CAAC;QAC3B,CAAC,MAAM;UACLkD,UAAU,CAAC,yBAAyB,CAAC;UACrCvD,UAAU,CAAC,MAAM/H,UAAU,CAAC,MAAM,CAAC,EAAE,IAAI,CAAC;QAC5C;MACF,CAAC,MAAM;QACL,IAAI+F,QAAQ,CAACmF,QAAQ,KAAKnF,QAAQ,CAACqF,eAAe,EAAE;UAClDE,UAAU,CAAC,wBAAwB,CAAC;UACpCD,UAAU,CAAC,KAAK,CAAC;UACjB;QACF;QACA,IAAItF,QAAQ,CAACmF,QAAQ,CAACpH,MAAM,GAAG,CAAC,EAAE;UAChCwH,UAAU,CAAC,wCAAwC,CAAC;UACpDD,UAAU,CAAC,KAAK,CAAC;UACjB;QACF;QAEA,MAAM;UAAElJ;QAAM,CAAC,GAAG,MAAM4I,MAAM,CAAChF,QAAQ,CAAC3E,KAAK,EAAE2E,QAAQ,CAACmF,QAAQ,EAAEnF,QAAQ,CAACoF,QAAQ,CAAC;QACpF,IAAIhJ,KAAK,EAAE;UACTmJ,UAAU,CAACnJ,KAAK,CAACiG,OAAO,CAAC;QAC3B,CAAC,MAAM;UACLkD,UAAU,CAAC,kEAAkE,CAAC;QAChF;MACF;IACF,CAAC,CAAC,OAAOnJ,KAAK,EAAE;MACdmJ,UAAU,CAAC,8BAA8B,CAAC;IAC5C,CAAC,SAAS;MACRD,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,oBACEpM,OAAA;IAAK2B,SAAS,EAAC,gBAAgB;IAAAC,QAAA,gBAC7B5B,OAAA;MAAK2B,SAAS,EAAC,aAAa;MAAAC,QAAA,gBAC1B5B,OAAA;QAAQ2B,SAAS,EAAC,aAAa;QAACM,OAAO,EAAEA,CAAA,KAAMlB,UAAU,CAAC,MAAM,CAAE;QAAAa,QAAA,EAAC;MAEnE;QAAAZ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACTnB,OAAA;QAAA4B,QAAA,EAAKmK,OAAO,GAAG,SAAS,GAAG;MAAgB;QAAA/K,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eACjDnB,OAAA;QAAA4B,QAAA,EAAImK,OAAO,GAAG,yBAAyB,GAAG;MAAkD;QAAA/K,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC9F,CAAC,eAENnB,OAAA;MAAK2B,SAAS,EAAC,gBAAgB;MAAAC,QAAA,gBAC7B5B,OAAA;QAAK2B,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACxB5B,OAAA;UACE2B,SAAS,EAAE,YAAYoK,OAAO,GAAG,QAAQ,GAAG,EAAE,EAAG;UACjD9J,OAAO,EAAEA,CAAA,KAAM+J,UAAU,CAAC,IAAI,CAAE;UAAApK,QAAA,EACjC;QAED;UAAAZ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTnB,OAAA;UACE2B,SAAS,EAAE,YAAY,CAACoK,OAAO,GAAG,QAAQ,GAAG,EAAE,EAAG;UAClD9J,OAAO,EAAEA,CAAA,KAAM+J,UAAU,CAAC,KAAK,CAAE;UAAApK,QAAA,EAClC;QAED;UAAAZ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,EAELgI,OAAO,iBACNnJ,OAAA;QAAK2B,SAAS,EAAE,kBAAkBwH,OAAO,CAACa,QAAQ,CAAC,OAAO,CAAC,IAAIb,OAAO,CAACa,QAAQ,CAAC,OAAO,CAAC,GAAG,OAAO,GAAG,SAAS,EAAG;QAAApI,QAAA,EAC9GuH;MAAO;QAAAnI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CACN,eAEDnB,OAAA;QAAM2B,SAAS,EAAC,WAAW;QAACsI,QAAQ,EAAEvB,YAAa;QAAA9G,QAAA,GAChD,CAACmK,OAAO,iBACP/L,OAAA;UAAK2B,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzB5B,OAAA;YAAOwE,OAAO,EAAC,UAAU;YAAA5C,QAAA,EAAC;UAAS;YAAAZ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAC3CnB,OAAA;YACEmK,IAAI,EAAC,MAAM;YACX1I,EAAE,EAAC,UAAU;YACbgH,IAAI,EAAC,UAAU;YACfhE,KAAK,EAAEqC,QAAQ,CAACoF,QAAS;YACzBxH,QAAQ,EAAE8D,iBAAkB;YAC5B4B,QAAQ,EAAE,CAAC2B,OAAQ;YACnB1B,WAAW,EAAC;UAAgB;YAAArJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7B,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CACN,eAEDnB,OAAA;UAAK2B,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzB5B,OAAA;YAAOwE,OAAO,EAAC,OAAO;YAAA5C,QAAA,EAAC;UAAK;YAAAZ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACpCnB,OAAA;YACEmK,IAAI,EAAC,OAAO;YACZ1I,EAAE,EAAC,OAAO;YACVgH,IAAI,EAAC,OAAO;YACZhE,KAAK,EAAEqC,QAAQ,CAAC3E,KAAM;YACtBuC,QAAQ,EAAE8D,iBAAkB;YAC5B4B,QAAQ;YACRC,WAAW,EAAC;UAAgB;YAAArJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7B,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAENnB,OAAA;UAAK2B,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzB5B,OAAA;YAAOwE,OAAO,EAAC,UAAU;YAAA5C,QAAA,EAAC;UAAQ;YAAAZ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAC1CnB,OAAA;YACEmK,IAAI,EAAC,UAAU;YACf1I,EAAE,EAAC,UAAU;YACbgH,IAAI,EAAC,UAAU;YACfhE,KAAK,EAAEqC,QAAQ,CAACmF,QAAS;YACzBvH,QAAQ,EAAE8D,iBAAkB;YAC5B4B,QAAQ;YACRC,WAAW,EAAE0B,OAAO,GAAG,eAAe,GAAG;UAAwB;YAAA/K,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,EAEL,CAAC4K,OAAO,iBACP/L,OAAA;UAAK2B,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzB5B,OAAA;YAAOwE,OAAO,EAAC,iBAAiB;YAAA5C,QAAA,EAAC;UAAgB;YAAAZ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACzDnB,OAAA;YACEmK,IAAI,EAAC,UAAU;YACf1I,EAAE,EAAC,iBAAiB;YACpBgH,IAAI,EAAC,iBAAiB;YACtBhE,KAAK,EAAEqC,QAAQ,CAACqF,eAAgB;YAChCzH,QAAQ,EAAE8D,iBAAkB;YAC5B4B,QAAQ,EAAE,CAAC2B,OAAQ;YACnB1B,WAAW,EAAC;UAAuB;YAAArJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CACN,eAEDnB,OAAA;UACEmK,IAAI,EAAC,QAAQ;UACbxI,SAAS,EAAC,2BAA2B;UACrCyD,QAAQ,EAAE1E,OAAQ;UAAAkB,QAAA,EAEjBlB,OAAO,GAAG,gBAAgB,GAAIqL,OAAO,GAAG,SAAS,GAAG;QAAiB;UAAA/K,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV;AAACyK,GAAA,CAhKQtK,KAAK;EAAA,QACejC,OAAO;AAAA;AAAAiN,GAAA,GAD3BhL,KAAK;AAkKd,eAAee,GAAG;AAAC,IAAAD,EAAA,EAAAE,GAAA,EAAAiE,GAAA,EAAAC,GAAA,EAAAqE,GAAA,EAAAc,GAAA,EAAAW,GAAA;AAAAC,YAAA,CAAAnK,EAAA;AAAAmK,YAAA,CAAAjK,GAAA;AAAAiK,YAAA,CAAAhG,GAAA;AAAAgG,YAAA,CAAA/F,GAAA;AAAA+F,YAAA,CAAA1B,GAAA;AAAA0B,YAAA,CAAAZ,GAAA;AAAAY,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}