{"ast": null, "code": "import { constructFrom } from \"./constructFrom.js\";\nimport { differenceInCalendarDays } from \"./differenceInCalendarDays.js\";\nimport { startOfISOWeekYear } from \"./startOfISOWeekYear.js\";\nimport { toDate } from \"./toDate.js\";\n\n/**\n * The {@link setISOWeekYear} function options.\n */\n\n/**\n * @name setISOWeekYear\n * @category ISO Week-Numbering Year Helpers\n * @summary Set the ISO week-numbering year to the given date.\n *\n * @description\n * Set the ISO week-numbering year to the given date,\n * saving the week number and the weekday number.\n *\n * ISO week-numbering year: http://en.wikipedia.org/wiki/ISO_week_date\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows using extensions like [`UTCDate`](https://github.com/date-fns/utc).\n * @typeParam ResultDate - The result `Date` type, it is the type returned from the context function if it is passed, or inferred from the arguments.\n *\n * @param date - The date to be changed\n * @param weekYear - The ISO week-numbering year of the new date\n * @param options - An object with options\n *\n * @returns The new date with the ISO week-numbering year set\n *\n * @example\n * // Set ISO week-numbering year 2007 to 29 December 2008:\n * const result = setISOWeekYear(new Date(2008, 11, 29), 2007)\n * //=> Mon Jan 01 2007 00:00:00\n */\nexport function setISOWeekYear(date, weekYear, options) {\n  let _date = toDate(date, options?.in);\n  const diff = differenceInCalendarDays(_date, startOfISOWeekYear(_date, options));\n  const fourthOfJanuary = constructFrom(options?.in || date, 0);\n  fourthOfJanuary.setFullYear(weekYear, 0, 4);\n  fourthOfJanuary.setHours(0, 0, 0, 0);\n  _date = startOfISOWeekYear(fourthOfJanuary);\n  _date.setDate(_date.getDate() + diff);\n  return _date;\n}\n\n// Fallback for modularized imports:\nexport default setISOWeekYear;", "map": {"version": 3, "names": ["constructFrom", "differenceInCalendarDays", "startOfISOWeekYear", "toDate", "setISOWeekYear", "date", "weekYear", "options", "_date", "in", "diff", "fourthOfJanuary", "setFullYear", "setHours", "setDate", "getDate"], "sources": ["C:/Users/<USER>/node_modules/date-fns/setISOWeekYear.js"], "sourcesContent": ["import { constructFrom } from \"./constructFrom.js\";\nimport { differenceInCalendarDays } from \"./differenceInCalendarDays.js\";\nimport { startOfISOWeekYear } from \"./startOfISOWeekYear.js\";\nimport { toDate } from \"./toDate.js\";\n\n/**\n * The {@link setISOWeekYear} function options.\n */\n\n/**\n * @name setISOWeekYear\n * @category ISO Week-Numbering Year Helpers\n * @summary Set the ISO week-numbering year to the given date.\n *\n * @description\n * Set the ISO week-numbering year to the given date,\n * saving the week number and the weekday number.\n *\n * ISO week-numbering year: http://en.wikipedia.org/wiki/ISO_week_date\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows using extensions like [`UTCDate`](https://github.com/date-fns/utc).\n * @typeParam ResultDate - The result `Date` type, it is the type returned from the context function if it is passed, or inferred from the arguments.\n *\n * @param date - The date to be changed\n * @param weekYear - The ISO week-numbering year of the new date\n * @param options - An object with options\n *\n * @returns The new date with the ISO week-numbering year set\n *\n * @example\n * // Set ISO week-numbering year 2007 to 29 December 2008:\n * const result = setISOWeekYear(new Date(2008, 11, 29), 2007)\n * //=> Mon Jan 01 2007 00:00:00\n */\nexport function setISOWeekYear(date, weekYear, options) {\n  let _date = toDate(date, options?.in);\n  const diff = differenceInCalendarDays(\n    _date,\n    startOfISOWeekYear(_date, options),\n  );\n  const fourthOfJanuary = constructFrom(options?.in || date, 0);\n  fourthOfJanuary.setFullYear(weekYear, 0, 4);\n  fourthOfJanuary.setHours(0, 0, 0, 0);\n  _date = startOfISOWeekYear(fourthOfJanuary);\n  _date.setDate(_date.getDate() + diff);\n  return _date;\n}\n\n// Fallback for modularized imports:\nexport default setISOWeekYear;\n"], "mappings": "AAAA,SAASA,aAAa,QAAQ,oBAAoB;AAClD,SAASC,wBAAwB,QAAQ,+BAA+B;AACxE,SAASC,kBAAkB,QAAQ,yBAAyB;AAC5D,SAASC,MAAM,QAAQ,aAAa;;AAEpC;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,cAAcA,CAACC,IAAI,EAAEC,QAAQ,EAAEC,OAAO,EAAE;EACtD,IAAIC,KAAK,GAAGL,MAAM,CAACE,IAAI,EAAEE,OAAO,EAAEE,EAAE,CAAC;EACrC,MAAMC,IAAI,GAAGT,wBAAwB,CACnCO,KAAK,EACLN,kBAAkB,CAACM,KAAK,EAAED,OAAO,CACnC,CAAC;EACD,MAAMI,eAAe,GAAGX,aAAa,CAACO,OAAO,EAAEE,EAAE,IAAIJ,IAAI,EAAE,CAAC,CAAC;EAC7DM,eAAe,CAACC,WAAW,CAACN,QAAQ,EAAE,CAAC,EAAE,CAAC,CAAC;EAC3CK,eAAe,CAACE,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;EACpCL,KAAK,GAAGN,kBAAkB,CAACS,eAAe,CAAC;EAC3CH,KAAK,CAACM,OAAO,CAACN,KAAK,CAACO,OAAO,CAAC,CAAC,GAAGL,IAAI,CAAC;EACrC,OAAOF,KAAK;AACd;;AAEA;AACA,eAAeJ,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}